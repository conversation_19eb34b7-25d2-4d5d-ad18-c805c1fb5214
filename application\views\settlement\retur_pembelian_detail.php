<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Settlement Retur Pembelian</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement/retur') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Retur</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Nomor Retur</th>
                                        <td><?= $retur->nomor_retur ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Retur</th>
                                        <td><?= date('d-m-Y', strtotime($retur->tanggal_retur)) ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nomor Pembelian</th>
                                        <td><?= $pembelian->nomor_pembelian ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Pembelian</th>
                                        <td><?= date('d-m-Y', strtotime($pembelian->tanggal_pembelian)) ?></td>
                                    </tr>
                                    <tr>
                                        <th>Alasan Retur</th>
                                        <td><?= $retur->alasan_retur ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status Penggantian</th>
                                        <td>
                                            <?php
                                            switch ($retur->status_penggantian) {
                                                case 'belum_diganti':
                                                    echo '<span class="badge badge-warning">Belum Diganti</span>';
                                                    break;
                                                case 'diganti_barang':
                                                    echo '<span class="badge badge-success">Diganti Barang</span>';
                                                    break;
                                                case 'diganti_uang':
                                                    echo '<span class="badge badge-info">Diganti Uang</span>';
                                                    break;
                                                case 'tidak_diganti':
                                                    echo '<span class="badge badge-danger">Tidak Diganti</span>';
                                                    break;
                                                default:
                                                    echo $retur->status_penggantian;
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-info">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Supplier</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Nama Supplier</th>
                                        <td><?= $pembelian->nama_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Alamat</th>
                                        <td><?= $pembelian->alamat_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Telepon</th>
                                        <td><?= $pembelian->telepon_supplier ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email</th>
                                        <td><?= $pembelian->email_supplier ?? '-' ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Detail Barang Retur</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Kode Barang</th>
                                                <th>Nama Barang</th>
                                                <th>Qty Retur</th>
                                                <th>Satuan</th>
                                                <th>Harga Beli</th>
                                                <th>Subtotal</th>
                                                <th>Alasan Retur</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            $total_qty = 0;
                                            $total_nilai = 0;
                                            
                                            if (!empty($detail)):
                                                foreach ($detail as $row): 
                                                    $subtotal = $row->qty_retur * $row->harga_beli;
                                                    $total_qty += $row->qty_retur;
                                                    $total_nilai += $subtotal;
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->kode_barang ?></td>
                                                <td><?= $row->nama_barang ?> <?= $row->merk ? '- '.$row->merk : '' ?> <?= $row->tipe ? '- '.$row->tipe : '' ?></td>
                                                <td class="text-right"><?= number_format($row->qty_retur, 2) ?></td>
                                                <td><?= $row->nama_satuan ?></td>
                                                <td class="text-right"><?= 'Rp ' . number_format($row->harga_beli, 2, ',', '.') ?></td>
                                                <td class="text-right"><?= 'Rp ' . number_format($subtotal, 2, ',', '.') ?></td>
                                                <td><?= $row->alasan_retur_detail ?? $retur->alasan_retur ?></td>
                                            </tr>
                                            <?php 
                                                endforeach;
                                            else:
                                            ?>
                                            <tr>
                                                <td colspan="8" class="text-center">Tidak ada data detail retur</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="3" class="text-right">Total</th>
                                                <th class="text-right"><?= number_format($total_qty, 2) ?></th>
                                                <th></th>
                                                <th></th>
                                                <th class="text-right"><?= 'Rp ' . number_format($total_nilai, 2, ',', '.') ?></th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($retur->status_penggantian == 'diganti_barang'): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Penggantian Barang</h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($penggantian)): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Nomor Penggantian</th>
                                                <th>Tanggal Penggantian</th>
                                                <th>Kode Barang</th>
                                                <th>Nama Barang</th>
                                                <th>Qty Penggantian</th>
                                                <th>Satuan</th>
                                                <th>Harga Satuan</th>
                                                <th>Subtotal</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            $total_qty_penggantian = 0;
                                            $total_nilai_penggantian = 0;
                                            
                                            foreach ($penggantian as $row): 
                                                $subtotal = $row->qty_penggantian * $row->harga_satuan;
                                                $total_qty_penggantian += $row->qty_penggantian;
                                                $total_nilai_penggantian += $subtotal;
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->nomor_penggantian ?></td>
                                                <td><?= date('d-m-Y', strtotime($row->tanggal_penggantian)) ?></td>
                                                <td><?= $row->kode_barang ?></td>
                                                <td><?= $row->nama_barang ?> <?= $row->merk ? '- '.$row->merk : '' ?> <?= $row->tipe ? '- '.$row->tipe : '' ?></td>
                                                <td class="text-right"><?= number_format($row->qty_penggantian, 2) ?></td>
                                                <td><?= $row->nama_satuan ?></td>
                                                <td class="text-right"><?= 'Rp ' . number_format($row->harga_satuan, 2, ',', '.') ?></td>
                                                <td class="text-right"><?= 'Rp ' . number_format($subtotal, 2, ',', '.') ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="5" class="text-right">Total</th>
                                                <th class="text-right"><?= number_format($total_qty_penggantian, 2) ?></th>
                                                <th></th>
                                                <th></th>
                                                <th class="text-right"><?= 'Rp ' . number_format($total_nilai_penggantian, 2, ',', '.') ?></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="alert <?= $total_nilai == $total_nilai_penggantian ? 'alert-success' : 'alert-warning' ?>">
                                        <h5><i class="icon fas fa-<?= $total_nilai == $total_nilai_penggantian ? 'check' : 'exclamation-triangle' ?>"></i> Informasi</h5>
                                        <p>
                                            Nilai Retur: <?= 'Rp ' . number_format($total_nilai, 2, ',', '.') ?><br>
                                            Nilai Penggantian: <?= 'Rp ' . number_format($total_nilai_penggantian, 2, ',', '.') ?><br>
                                            Selisih: <?= 'Rp ' . number_format($total_nilai - $total_nilai_penggantian, 2, ',', '.') ?>
                                        </p>
                                        <?php if ($total_nilai != $total_nilai_penggantian): ?>
                                        <p>Terdapat selisih antara nilai retur dan nilai penggantian. Silakan periksa kembali data penggantian.</p>
                                        <?php else: ?>
                                        <p>Nilai retur dan nilai penggantian telah sesuai.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Data penggantian barang tidak ditemukan meskipun status penggantian adalah "Diganti Barang". Silakan periksa kembali data penggantian.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php elseif ($retur->status_penggantian == 'diganti_uang'): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Penggantian Uang</h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($penggantian)): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Nomor Penggantian</th>
                                                <th>Tanggal Penggantian</th>
                                                <th>Metode Pembayaran</th>
                                                <th>Nomor Referensi</th>
                                                <th>Jumlah Penggantian</th>
                                                <th>Keterangan</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            $total_penggantian = 0;
                                            
                                            foreach ($penggantian as $row): 
                                                $total_penggantian += $row->jumlah_penggantian;
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->nomor_penggantian ?></td>
                                                <td><?= date('d-m-Y', strtotime($row->tanggal_penggantian)) ?></td>
                                                <td>
                                                    <?php
                                                    switch ($row->metode_pembayaran) {
                                                        case 'tunai':
                                                            echo 'Tunai';
                                                            break;
                                                        case 'transfer':
                                                            echo 'Transfer Bank';
                                                            break;
                                                        case 'cek':
                                                            echo 'Cek/Giro';
                                                            break;
                                                        case 'kartu_kredit':
                                                            echo 'Kartu Kredit';
                                                            break;
                                                        case 'kartu_debit':
                                                            echo 'Kartu Debit';
                                                            break;
                                                        default:
                                                            echo $row->metode_pembayaran;
                                                    }
                                                    ?>
                                                </td>
                                                <td><?= $row->nomor_referensi ?? '-' ?></td>
                                                <td class="text-right"><?= 'Rp ' . number_format($row->jumlah_penggantian, 2, ',', '.') ?></td>
                                                <td><?= $row->keterangan ?? '-' ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="5" class="text-right">Total</th>
                                                <th class="text-right"><?= 'Rp ' . number_format($total_penggantian, 2, ',', '.') ?></th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="alert <?= $total_nilai == $total_penggantian ? 'alert-success' : 'alert-warning' ?>">
                                        <h5><i class="icon fas fa-<?= $total_nilai == $total_penggantian ? 'check' : 'exclamation-triangle' ?>"></i> Informasi</h5>
                                        <p>
                                            Nilai Retur: <?= 'Rp ' . number_format($total_nilai, 2, ',', '.') ?><br>
                                            Nilai Penggantian: <?= 'Rp ' . number_format($total_penggantian, 2, ',', '.') ?><br>
                                            Selisih: <?= 'Rp ' . number_format($total_nilai - $total_penggantian, 2, ',', '.') ?>
                                        </p>
                                        <?php if ($total_nilai != $total_penggantian): ?>
                                        <p>Terdapat selisih antara nilai retur dan nilai penggantian. Silakan periksa kembali data penggantian.</p>
                                        <?php else: ?>
                                        <p>Nilai retur dan nilai penggantian telah sesuai.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Data penggantian uang tidak ditemukan meskipun status penggantian adalah "Diganti Uang". Silakan periksa kembali data penggantian.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php elseif ($retur->status_penggantian == 'belum_diganti'): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                            <p>Retur ini belum diproses penggantiannya. Silakan lakukan penggantian barang atau uang.</p>
                            <div class="mt-2">
                                <a href="<?= site_url('ReturPembelian/proses_penggantian/' . $retur->id) ?>" class="btn btn-primary"><i class="fas fa-exchange-alt"></i> Proses Penggantian</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php elseif ($retur->status_penggantian == 'tidak_diganti'): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <h5><i class="icon fas fa-ban"></i> Informasi</h5>
                            <p>Retur ini tidak akan diganti karena <?= $retur->alasan_tidak_diganti ?? 'alasan tidak diganti tidak dicatat' ?>.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>