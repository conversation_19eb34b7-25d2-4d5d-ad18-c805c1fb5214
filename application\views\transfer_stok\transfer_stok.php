<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-exchange text-blue"></i> Data Transfer Stok</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_transfer_stok" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Transfer</th>
                                    <th>Tanggal</th>
                                    <th>Gudang Asal</th>
                                    <th>Gudang Tujuan</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Header -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Transfer Stok</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Transfer -->
<div class="modal fade" id="modal_detail" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Transfer Stok</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-detail-body">
                <!-- Detail akan dimuat di sini -->
            </div>
            <div class="modal-footer justify-content-end">
                <span id="detail-action-buttons">
                    <!-- Action buttons will be loaded here dynamically -->
                </span>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_transfer_stok").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Transfer Stok Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('TransferStok/ajax_list') ?>",
                "type": "POST"
            },

        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });

    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Tambah Transfer Stok'); // Set Title to Bootstrap modal title
        $('#btnSave').text('Simpan'); // change button text
        $('#btnSave').attr('disabled', false); // set button enable

        // Load form input
        $.ajax({
            url: "<?php echo site_url('TransferStok/form_input') ?>",
            type: "GET",
            dataType: "HTML", // Ubah dari JSON ke HTML karena controller mengembalikan view
            success: function(data) {
                $('#modal-body').html(data);
                
                // Reset form setelah form dimuat
                if ($('#form').length > 0) {
                    $('#form')[0].reset();
                }
                
                // Initialize Select2 setelah form dimuat
                $('.select2').select2({
                    dropdownParent: $('#modal_form'),
                    placeholder: "-- Pilih --",
                    allowClear: true
                });
                
                // Show modal setelah form berhasil dimuat
                $('#modal_form').modal('show');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('AJAX Error:', textStatus, errorThrown);
                console.error('Response:', jqXHR.responseText);
                
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal memuat form. Silakan coba lagi.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Edit Transfer Stok'); // Set title to Bootstrap modal title
        $('#btnSave').text('Update'); // change button text
        $('#btnSave').attr('disabled', false); // set button enable

        // Load form input terlebih dahulu
        $.ajax({
            url: "<?php echo site_url('TransferStok/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(formData) {
                $('#modal-body').html(formData);
                
                // Initialize Select2 setelah form dimuat
                $('.select2').select2({
                    dropdownParent: $('#modal_form'),
                    placeholder: "-- Pilih --",
                    allowClear: true
                });
                
                // Kemudian load data untuk edit
                $.ajax({
                    url: "<?php echo site_url('TransferStok/edit/') ?>" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_transfer"]').val(data.nomor_transfer);
                        $('[name="tanggal_transfer"]').val(data.tanggal_transfer);
                        $('[name="gudang_asal_id"]').val(data.gudang_asal_id).trigger('change');
                        $('[name="gudang_tujuan_id"]').val(data.gudang_tujuan_id).trigger('change');
                        $('[name="keterangan"]').val(data.keterangan);

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Error loading edit data:', textStatus, errorThrown);
                        Swal.fire({
                            title: 'Error!',
                            text: 'Gagal memuat data untuk edit.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Error loading form:', textStatus, errorThrown);
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal memuat form edit.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('TransferStok/insert') ?>";
        } else {
            url = "<?php echo site_url('TransferStok/update') ?>";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload(null, false);

                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data transfer stok berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                    }
                    
                    // Reinisialisasi Select2 setelah validasi gagal
                    $('.select2').select2();
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable
            }
        });
    }

    function detail(id) {
        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('TransferStok/detail/') ?>" + id,
            type: "GET",
            success: function(data) {
                $('#modal-detail-body').html(data);
                $('#modal_detail').modal('show'); // show bootstrap modal when complete loaded
                $('.modal-title').text('Detail Transfer Stok'); // Set title to Bootstrap modal title
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Error:', textStatus, errorThrown);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil data detail.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function hapus(id) {
        Swal.fire({
            title: 'Apakah Anda yakin?',
            text: "Data yang dihapus tidak dapat dikembalikan!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('TransferStok/delete/') ?>" + id,
                    type: "POST",
                    dataType: "JSON",
                    data: { id: id },
                    success: function(data) {
                        if (data.status) {
                            table.ajax.reload(null, false);
                            Swal.fire(
                                'Terhapus!',
                                data.message || 'Data transfer stok berhasil dihapus.',
                                'success'
                            );
                        } else {
                            Swal.fire(
                                'Error!',
                                data.message || 'Gagal menghapus data transfer stok.',
                                'error'
                            );
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Delete error:', jqXHR.responseText);
                        
                        let errorMessage = 'Terjadi kesalahan saat menghapus data.';
                        
                        // Coba parse respons JSON untuk pesan error yang lebih spesifik
                        try {
                            const response = JSON.parse(jqXHR.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            // Jika tidak bisa parse JSON, gunakan pesan default berdasarkan status
                            if (jqXHR.status === 404) {
                                errorMessage = 'Endpoint tidak ditemukan.';
                            } else if (jqXHR.status === 500) {
                                errorMessage = 'Terjadi kesalahan server internal.';
                            }
                        }
                        
                        Swal.fire(
                            'Error!',
                            errorMessage,
                            'error'
                        );
                    }
                });
            }
        });
    }

    function kirim(id) {
        Swal.fire({
            title: 'Kirim Transfer Stok?',
            text: "Setelah dikirim, transfer tidak dapat diubah lagi!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, kirim!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('TransferStok/update_status') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id,
                        status: 'dikirim'
                    },
                    success: function(data) {
                        if (data.status) {
                            table.ajax.reload(null, false);
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat mengirim transfer.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function terima(id) {
        Swal.fire({
            title: 'Terima Transfer Stok?',
            text: "Setelah diterima, stok akan otomatis ditambahkan ke gudang tujuan!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, terima!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('TransferStok/update_status') ?>",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id,
                        status: 'diterima'
                    },
                    success: function(data) {
                        if (data.status) {
                            table.ajax.reload(null, false);
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menerima transfer.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    // Function untuk generate nomor otomatis
    function generateNomor() {
        $.ajax({
            url: "<?php echo site_url('TransferStok/generate_nomor') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="nomor_transfer"]').val(data.nomor);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal generate nomor.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function print_transfer(id) {
        var url = "<?php echo site_url('TransferStok/cetak_transfer/') ?>" + id;
        window.open(url, '_blank');
    }

    // Event listener untuk reload data saat modal detail ditutup
    $(document).ready(function() {
        $('#modal_detail').on('hidden.bs.modal', function () {
            // Reload tabel transfer stok saat modal detail ditutup
            if (table) {
                table.ajax.reload(null, false);
                console.log('Data tabel transfer stok berhasil di-reload setelah modal ditutup');
            }
        });
    });
</script>