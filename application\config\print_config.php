<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Print Template Configuration
|--------------------------------------------------------------------------
|
| Konfigurasi untuk template print yang akan digunakan dalam sistem.
| Pengembang dapat memilih template yang sesuai dengan kebutuhan.
|
*/

/*
|--------------------------------------------------------------------------
| Print Template Type
|--------------------------------------------------------------------------
|
| Pilihan template print yang tersedia:
| 1 = Template dengan gradasi dan warna (print_template.php)
| 2 = Template sederhana dengan kotak hitam putih (print_template_v2.php)
| 3 = Template minimalis dengan garis (print_template_v3.php)
|
| Default: 1
|
*/
$config['print_template_type'] = 2;

/*
|--------------------------------------------------------------------------
| Print Template Files
|--------------------------------------------------------------------------
|
| Mapping file template berdasarkan tipe yang dipilih
|
*/
$config['print_template_files'] = [
    1 => 'templates/print_template',
    2 => 'templates/print_template_v2', 
    3 => 'templates/print_template_v3',
    4 => 'templates/print_template_v4',
    5 => 'templates/print_template_v5',
];

/*
|--------------------------------------------------------------------------
| Available Templates
|--------------------------------------------------------------------------
|
| Template yang tersedia dengan informasi lengkap
|
*/
$config['available_templates'] = [
    1 => [
        'name' => 'Modern',
        'file' => 'print_template.php',
        'description' => 'Template dengan gradasi warna, shadow, dan styling modern'
    ],
    2 => [
        'name' => 'Profesional', 
        'file' => 'print_template_v2.php',
        'description' => 'Template sederhana dengan kotak hitam putih, tanpa gradasi'
    ],
    3 => [
        'name' => 'Minimalis',
        'file' => 'print_template_v3.php', 
        'description' => 'Template sangat sederhana dengan garis pemisah'
    ],
    4 => [
        'name' => 'Profesional Tipis',
        'file' => 'print_template_v4.php',
        'description' => 'Template profesional dengan garis dan kotak tipis, tampilan halus'
    ],
    5 => [
            'name' => 'Modern Professional',
            'file' => 'print_template_v5.php',
            'description' => 'Template modern dengan gradasi elegan, typography premium, dan desain sophisticated'
        ]
];

/*
|--------------------------------------------------------------------------
| Print Template Descriptions
|--------------------------------------------------------------------------
|
| Deskripsi untuk setiap template (untuk dokumentasi pengembang)
|
*/
$config['print_template_descriptions'] = [
    1 => 'Template Modern - Dengan gradasi warna, shadow, dan styling modern',
    2 => 'Template Profesional - Sederhana dengan kotak hitam putih, tanpa gradasi',
    3 => 'Template Minimalis - Sangat sederhana dengan garis pemisah, font monospace',
    4 => 'Template Profesional Tipis - Garis dan kotak tipis, tampilan halus',
    5 => 'Template Black White Pro - Typography premium, warna hitam putih'
];

/*
|--------------------------------------------------------------------------
| Company Default Information
|--------------------------------------------------------------------------
|
| Informasi default perusahaan yang akan muncul di semua template
|
*/
$config['company_info'] = [
    'name' => 'TOKO ELEKTRONIK',
    'address' => 'Jl. Contoh No. 123, Kota Contoh',
    'phone' => '(021) 1234567',
    'email' => '<EMAIL>',
    'website' => 'www.tokoelektronik.com'
];

/*
|--------------------------------------------------------------------------
| Print Settings
|--------------------------------------------------------------------------
|
| Pengaturan umum untuk print
|
*/
$config['print_settings'] = [
    'auto_print' => false,          // Auto print saat halaman dimuat
    'auto_close' => false,          // Auto close setelah print
    'show_print_date' => true,      // Tampilkan tanggal print
    'show_document_number' => true, // Tampilkan nomor dokumen
    'page_size' => 'A4',           // Ukuran halaman (A4, Letter, etc)
    'orientation' => 'portrait'     // Orientasi (portrait, landscape)
];

/*
|--------------------------------------------------------------------------
| Template Features
|--------------------------------------------------------------------------
|
| Fitur yang tersedia untuk setiap template
|
*/
$config['template_features'] = [
    1 => [
        'gradients' => true,
        'colors' => true,
        'shadows' => true,
        'icons' => true,
        'modern_styling' => true
    ],
    2 => [
        'gradients' => false,
        'colors' => false,
        'shadows' => false,
        'icons' => false,
        'modern_styling' => false,
        'simple_boxes' => true
    ],
    3 => [
        'gradients' => false,
        'colors' => false,
        'shadows' => false,
        'icons' => false,
        'modern_styling' => false,
        'minimal_lines' => true,
        'monospace_font' => true
    ],
    4 => [
        'gradients' => false,
        'colors' => false,
        'shadows' => false,
        'icons' => true,
        'thin_borders' => true,
        'clean_styling' => true
    ],
    5 => [
            'gradients' => true,
            'colors' => true,
            'shadows' => true,
            'icons' => true,
            'borders' => 'modern',
            'style' => 'modern_professional',
            'typography' => 'premium',
            'layout' => 'sophisticated',
            'design' => 'elegant',
            'effects' => 'subtle'
        ]
];

/*
|--------------------------------------------------------------------------
| Print Quality Settings
|--------------------------------------------------------------------------
|
| Pengaturan kualitas print untuk setiap template
|
*/
$config['print_quality'] = [
    1 => [
        'dpi' => 300,
        'color_mode' => 'color',
        'recommended_printer' => 'inkjet'
    ],
    2 => [
        'dpi' => 200,
        'color_mode' => 'grayscale',
        'recommended_printer' => 'laser'
    ],
    3 => [
        'dpi' => 150,
        'color_mode' => 'monochrome',
        'recommended_printer' => 'dot_matrix'
    ]
];

/*
|--------------------------------------------------------------------------
| Debug Mode
|--------------------------------------------------------------------------
|
| Aktifkan untuk melihat informasi debug template
|
*/
$config['print_debug'] = false;