<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Retur Penjualan',
    'document_number' => $retur->nomor_retur,
    'document_title' => 'RETUR PENJUALAN'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Retur',
        'items' => [
            ['label' => 'Nomor Retur', 'value' => '<strong>' . $retur->nomor_retur . '</strong>'],
            ['label' => 'Tanggal Retur', 'value' => format_date_indonesia($retur->tanggal_retur)],
            ['label' => 'Status', 'value' => get_status_badge($retur->status)],
            ['label' => 'Alasan Retur', 'value' => $retur->alasan_retur ?? '-']
        ]
    ],
    [
        'title' => '<span class="icon-user"></span>Informasi Pelanggan',
        'items' => [
            ['label' => 'Nama Pelanggan', 'value' => '<strong>' . $retur->nama_pelanggan . '</strong> (' . $retur->kode_pelanggan . ')'],
            ['label' => 'Alamat', 'value' => $retur->alamat_pelanggan ?: '-'],
            ['label' => 'Telepon', 'value' => $retur->no_telepon ?: '-'],
            ['label' => 'Email', 'value' => $retur->email ?? '-']
        ]
    ]
];

// Add reference documents if available
if (!empty($retur->nomor_faktur) || !empty($retur->nomor_pengiriman)) {
    $ref_items = [];
    if (!empty($retur->nomor_faktur)) {
        $ref_items[] = ['label' => 'No. Faktur Penjualan', 'value' => $retur->nomor_faktur];
    }
    if (!empty($retur->nomor_pengiriman)) {
        $ref_items[] = ['label' => 'No. Pengiriman', 'value' => $retur->nomor_pengiriman];
    }
    
    $info_sections[] = [
        'title' => '<span class="icon-document"></span>Dokumen Referensi',
        'items' => $ref_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '25%'],
    ['label' => 'Gudang', 'width' => '12%'],
    ['label' => 'Kondisi', 'width' => '10%', 'align' => 'center'],
    ['label' => 'Qty Retur', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Harga Satuan', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Total Harga', 'width' => '14%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$no = 1;

if (!empty($detail)) {
    foreach ($detail as $item) {
        $nama_barang = $item->nama_barang;
        if ($item->merk) {
            $nama_barang .= ' - ' . $item->merk;
        }
        if ($item->tipe) {
            $nama_barang .= ' (' . $item->tipe . ')';
        }
        
        // Color coding untuk kondisi
        $kondisi = ucfirst($item->kondisi_barang);
        if ($item->kondisi_barang == 'rusak') {
            $kondisi = '<span class="color-danger">' . $kondisi . '</span>';
        } elseif ($item->kondisi_barang == 'baik') {
            $kondisi = '<span class="color-success">' . $kondisi . '</span>';
        } elseif ($item->kondisi_barang == 'cacat') {
            $kondisi = '<span class="color-warning">' . $kondisi . '</span>';
        }
        
        $table_data[] = [
            $no++,
            $item->kode_barang,
            $nama_barang,
            $item->nama_gudang,
            $kondisi,
            number_format($item->qty_retur, 2),
            format_currency($item->harga_satuan),
            format_currency($item->total_harga)
        ];
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL</strong>', 'colspan' => '5', 'align' => 'right'],
        ['value' => '<strong>' . number_format($retur->total_qty, 2) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>Total Nilai</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($retur->total_nilai) . '</strong>', 'align' => 'right']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Barang Retur', $table_headers, $table_data, $table_options);

// Add notes if available
if (!empty($retur->keterangan)) {
    $content .= create_notes_section('Keterangan', $retur->keterangan);
}

// Add return policy notes
$content .= create_notes_section('Kebijakan Retur dari Pelanggan', 
    '• Barang yang diretur harus sesuai dengan kondisi yang dilaporkan' . "\n" .
    '• Proses pengembalian dana akan dilakukan setelah verifikasi barang' . "\n" .
    '• Barang rusak akan diklaim sesuai dengan ketentuan garansi' . "\n" .
    '• Credit note akan diproses setelah verifikasi kondisi barang' . "\n" .
    '• Dokumen ini merupakan bukti sah penerimaan barang retur dari pelanggan');

// Signatures - get user names if available
$user_created = '';
$user_updated = '';

if (isset($retur->created_by)) {
    // Note: In real implementation, you might want to get this from a user service or model
    $user_created = 'Staff Retur'; // Placeholder
}

if (isset($retur->updated_by)) {
    $user_updated = 'Supervisor'; // Placeholder
}

$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => '(............................)',
        'position' => 'Staff Customer Service'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Customer Service'
    ],
    [
        'title' => 'Disetujui Oleh',
        'name' => '(............................)',
        'position' => 'Manager'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>