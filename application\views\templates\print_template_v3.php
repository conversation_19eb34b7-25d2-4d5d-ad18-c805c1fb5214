<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Print Document' ?> - <?= $document_number ?? '' ?></title>
    <style>
        /* Reset dan Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background: #fff;
            margin: 0;
            padding: 15px;
        }
        
        /* Print Styles */
        @media print {
            body {
                margin: 0;
                padding: 10px;
                font-size: 11px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            @page {
                margin: 1cm;
                size: A4;
            }
        }
        
        /* Header Styles */
        .print-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 20px;
            font-weight: bold;
            color: #000;
            margin-bottom: 5px;
            text-transform: uppercase;
        }
        
        .company-info {
            font-size: 10px;
            color: #000;
            margin-bottom: 8px;
            line-height: 1.2;
        }
        
        .document-title {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            margin-top: 8px;
        }
        
        .header-line {
            border-top: 1px solid #000;
            margin: 10px 0;
        }
        
        /* Info Section Styles */
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 15px;
        }
        
        .info-box {
            flex: 1;
            margin-right: 15px;
        }
        
        .info-box:last-child {
            margin-right: 0;
        }
        
        .info-box-title {
            font-size: 12px;
            font-weight: bold;
            color: #000;
            margin-bottom: 8px;
            text-decoration: underline;
        }
        
        .info-item {
            margin-bottom: 5px;
            font-size: 11px;
        }
        
        .info-label {
            font-weight: bold;
            color: #000;
            display: inline-block;
            width: 100px;
        }
        
        .info-separator {
            margin: 0 5px;
            color: #000;
        }
        
        .info-value {
            color: #000;
        }
        
        /* Status Badge */
        .status-badge {
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            background: #fff;
            color: #000;
            padding: 2px 4px;
            border: 1px solid #000;
        }
        
        /* Table Styles */
        .table-container {
            margin: 20px 0;
        }
        
        .table-title {
            font-size: 13px;
            font-weight: bold;
            color: #000;
            margin-bottom: 8px;
            text-decoration: underline;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        
        .data-table th {
            background: #fff;
            color: #000;
            font-weight: bold;
            padding: 6px 4px;
            text-align: center;
            font-size: 10px;
            text-transform: uppercase;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
        }
        
        .data-table td {
            padding: 4px;
            vertical-align: top;
            background: #fff;
            font-size: 11px;
        }
        
        .data-table tbody tr {
            border-bottom: 1px dotted #000;
        }
        
        .total-row {
            background: #fff !important;
            font-weight: bold;
        }
        
        .total-row td {
            padding: 6px 4px;
            font-weight: bold;
            color: #000;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
        }
        
        /* Text Alignment */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        
        /* Summary Section */
        .summary-section {
            margin-top: 20px;
        }
        
        .summary-table {
            width: 250px;
            float: right;
            border-collapse: collapse;
        }
        
        .summary-table th {
            background: #fff;
            color: #000;
            padding: 5px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
        }
        
        .summary-table td {
            padding: 5px;
            text-align: right;
            background: #fff;
            color: #000;
            font-size: 11px;
            border-bottom: 1px dotted #000;
        }
        
        .summary-table .total-final {
            background: #fff;
            color: #000;
            font-weight: bold;
            font-size: 12px;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
        }
        
        /* Notes Section */
        .notes-section {
            margin-top: 15px;
            clear: both;
        }
        
        .notes-title {
            font-weight: bold;
            color: #000;
            margin-bottom: 5px;
            font-size: 12px;
            text-decoration: underline;
        }
        
        .notes-content {
            color: #000;
            line-height: 1.4;
            font-size: 11px;
        }
        
        /* Signature Section */
        .signature-section {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 150px;
            text-align: center;
        }
        
        .signature-title {
            font-weight: bold;
            color: #000;
            margin-bottom: 30px;
            font-size: 11px;
        }
        
        .signature-line {
            border-top: 1px solid #000;
            margin-top: 8px;
            padding-top: 3px;
            font-size: 10px;
            font-weight: bold;
            color: #000;
        }
        
        /* Footer */
        .print-footer {
            margin-top: 25px;
            text-align: center;
            font-size: 9px;
            color: #000;
            border-top: 1px solid #000;
            padding-top: 8px;
            line-height: 1.2;
        }
        
        /* Print Controls */
        .print-controls {
            text-align: center;
            margin-bottom: 20px;
            padding: 8px;
            background: #f9f9f9;
        }
        
        .btn-print {
            background: #fff;
            color: #000;
            padding: 6px 12px;
            border: 1px solid #000;
            cursor: pointer;
            font-size: 11px;
            margin: 0 3px;
        }
        
        .btn-print:hover {
            background: #f0f0f0;
        }
        
        /* Responsive Design */
        @media screen and (max-width: 768px) {
            .info-row {
                flex-direction: column;
            }
            
            .info-box {
                margin-right: 0;
                margin-bottom: 10px;
            }
            
            .summary-table {
                width: 100%;
                float: none;
            }
            
            .signature-section {
                flex-direction: column;
                gap: 20px;
            }
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 20px 10px;
            color: #000;
            font-style: italic;
            font-size: 11px;
        }
        
        /* Utility Classes */
        .font-bold { font-weight: bold; }
        .font-normal { font-weight: normal; }
        .text-small { font-size: 10px; }
        .text-large { font-size: 13px; }
        
        .mb-5 { margin-bottom: 5px; }
        .mb-10 { margin-bottom: 10px; }
        .mb-15 { margin-bottom: 15px; }
        .mt-5 { margin-top: 5px; }
        .mt-10 { margin-top: 10px; }
        .mt-15 { margin-top: 15px; }
        
        .p-5 { padding: 5px; }
        .p-10 { padding: 10px; }
        
        /* Simple Dividers */
        .divider {
            border-top: 1px solid #000;
            margin: 10px 0;
        }
        
        .divider-dotted {
            border-top: 1px dotted #000;
            margin: 8px 0;
        }
        
        .divider-double {
            border-top: 3px double #000;
            margin: 12px 0;
        }
        
        /* Clear float */
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <button class="btn-print" onclick="window.print()">
            CETAK
        </button>
        <button class="btn-print" onclick="window.close()">
            TUTUP
        </button>
    </div>

    <!-- Header -->
    <div class="print-header">
        <div class="company-name"><?= $company_name ?? 'TOKO ELEKTRONIK' ?></div>
        <div class="company-info">
            <?= $company_address ?? 'Jl. Contoh No. 123, Kota Contoh' ?><br>
            Telp: <?= $company_phone ?? '(021) 1234567' ?> | 
            Email: <?= $company_email ?? '<EMAIL>' ?>
        </div>
        <div class="header-line"></div>
        <div class="document-title"><?= $document_title ?? 'DOKUMEN' ?></div>
        <div class="header-line"></div>
    </div>

    <!-- Content akan diisi oleh view yang menggunakan template ini -->
    <?= $content ?? '' ?>

    <!-- Footer -->
    <div class="print-footer">
        <div class="divider"></div>
        <div>
            Dicetak: <?= date('d/m/Y H:i:s') ?> | 
            No. Dok: <?= $document_number ?? '-' ?> | 
            Sistem Toko Elektronik <?= date('Y') ?>
        </div>
    </div>

    <script>
        // Auto print functionality
        if (window.location.search.includes('auto_print=1')) {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }
        
        // Close window after printing (optional)
        window.onafterprint = function() {
            if (window.location.search.includes('auto_close=1')) {
                setTimeout(function() {
                    window.close();
                }, 1000);
            }
        };
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+P for print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape to close
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>