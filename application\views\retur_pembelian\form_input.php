<form id="form" class="form-horizontal">
    <input type="hidden" name="id" value="">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group row">
                    <label for="nomor_retur" class="col-sm-3 col-form-label">Nomor Retur</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="nomor_retur" name="nomor_retur" placeholder="Nomor Retur" value="<?= $nomor_retur ?>" readonly>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="tanggal_retur" class="col-sm-3 col-form-label">Tanggal Retur</label>
                    <div class="col-sm-9">
                        <input type="date" class="form-control" id="tanggal_retur" name="tanggal_retur" placeholder="Tanggal Retur" value="<?= date('Y-m-d') ?>">
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="id_pembelian" class="col-sm-3 col-form-label">Pembelian</label>
                    <div class="col-sm-9">
                        <select class="form-control select2" id="id_pembelian" name="id_pembelian" style="width: 100%;">
                            <option value="">-- Pilih Pembelian --</option>
                            <?php foreach ($pembelian_list as $pembelian) : ?>
                                <option value="<?= $pembelian->id ?>"><?= $pembelian->nomor_pembelian ?> - <?= date('d/m/Y', strtotime($pembelian->tanggal_pembelian)) ?> - <?= $pembelian->nama_supplier ?></option>
                            <?php endforeach; ?>
                        </select>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="id_penerimaan" class="col-sm-3 col-form-label">Penerimaan</label>
                    <div class="col-sm-9">
                        <select class="form-control select2" id="id_penerimaan" name="id_penerimaan" style="width: 100%;">
                            <option value="">-- Pilih Penerimaan --</option>
                            <?php foreach ($penerimaan_list as $penerimaan) : ?>
                                <option value="<?= $penerimaan->id ?>" data-pembelian="<?= $penerimaan->id_pembelian ?>"><?= $penerimaan->nomor_penerimaan ?> - <?= date('d/m/Y', strtotime($penerimaan->tanggal_penerimaan)) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group row">
                    <label for="id_supplier_display" class="col-sm-3 col-form-label">Supplier</label>
                    <div class="col-sm-9">
                        <select class="form-control select2" id="id_supplier_display" style="width: 100%;" disabled>
                            <option value="">-- Supplier --</option>
                            <?php foreach ($supplier_list as $supplier) : ?>
                                <option value="<?= $supplier->id ?>"><?= $supplier->nama ?> (<?= $supplier->kode ?>)</option>
                            <?php endforeach; ?>
                        </select>
                        <input type="hidden" name="id_supplier" id="id_supplier">
                        <input type="hidden" id="id_supplier_hidden">
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="alasan_retur" class="col-sm-3 col-form-label">Alasan Retur</label>
                    <div class="col-sm-9">
                        <textarea class="form-control" id="alasan_retur" name="alasan_retur" rows="2" placeholder="Alasan Retur"></textarea>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                    <div class="col-sm-9">
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="2" placeholder="Keterangan"></textarea>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detail Items Section -->
        <div class="row mt-3" id="detail-items-section" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title">Detail Item Retur</h3>
                    </div>
                    <div class="card-body" id="detail-items-container">
                        <!-- Detail items will be loaded here -->
                        <div class="text-center">
                            <i class="fa fa-info-circle"></i> Pilih penerimaan terlebih dahulu untuk menampilkan item.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        // Initialize select2
        $('.select2').select2();

        // Variable untuk menyimpan nilai penerimaan yang akan dipilih saat edit
        var selectedPenerimaanId = null;
        
        // Handle pembelian selection
        $('#id_pembelian').change(function() {
            var id_pembelian = $(this).val();
            if (id_pembelian) {
                // Get pembelian info
                $.ajax({
                    url: "<?php echo site_url('returpembelian/get_pembelian_info') ?>",
                    type: "POST",
                    data: {
                        id: id_pembelian
                    },
                    dataType: "JSON",
                    success: function(response) {
                        if (response.status) {
                            // Set supplier
                            $('#id_supplier_display').val(response.data.id_supplier).trigger('change');
                            $('#id_supplier').val(response.data.id_supplier);
                            $('#id_supplier_hidden').val(response.data.id_supplier);
                            
                            // Reset penerimaan selection first
                            $('#id_penerimaan').val('').trigger('change');
                            
                            // Update penerimaan dropdown with available penerimaan for this pembelian
                            if (response.penerimaan_list && response.penerimaan_list.length > 0) {
                                $('#id_penerimaan').empty();
                                $('#id_penerimaan').append('<option value="">-- Pilih Penerimaan --</option>');
                                
                                $.each(response.penerimaan_list, function(index, penerimaan) {
                                    var tanggal = new Date(penerimaan.tanggal_penerimaan);
                                    var formattedDate = String(tanggal.getDate()).padStart(2, '0') + '/' + 
                                                       String(tanggal.getMonth() + 1).padStart(2, '0') + '/' + 
                                                       tanggal.getFullYear();
                                    
                                    $('#id_penerimaan').append('<option value="' + penerimaan.id + '" data-pembelian="' + id_pembelian + '">' + 
                                        penerimaan.nomor_penerimaan + ' - ' + formattedDate + '</option>');
                                });
                                
                                // Refresh select2 dropdown
                                $('#id_penerimaan').select2('destroy').select2();
                                
                                // Jika ada nilai penerimaan yang harus dipilih (saat edit), pilih sekarang
                                if (selectedPenerimaanId) {
                                    $('#id_penerimaan').val(selectedPenerimaanId).trigger('change');
                                    selectedPenerimaanId = null; // Reset setelah digunakan
                                }
                            } else {
                                // If no penerimaan available
                                $('#id_penerimaan').empty();
                                $('#id_penerimaan').append('<option value="">-- Tidak ada penerimaan tersedia --</option>');
                                $('#id_penerimaan').select2('destroy').select2();
                                $('#detail-items-section').hide();
                                $('#detail-items-container').html('<div class="alert alert-warning">Tidak ada penerimaan yang tersedia untuk pembelian ini.</div>');
                            }
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat mengambil data pembelian.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            } else {
                // Reset supplier
                $('#id_supplier_display').val('').trigger('change');
                $('#id_supplier').val('');
                $('#id_supplier_hidden').val('');
                
                // Reset penerimaan dropdown to original state
                $('#id_penerimaan').val('').trigger('change');
                
                // Reload original penerimaan options
                $.ajax({
                    url: "<?php echo site_url('returpembelian/get_all_penerimaan') ?>",
                    type: "POST",
                    dataType: "JSON",
                    success: function(response) {
                        if (response.status) {
                            $('#id_penerimaan').empty();
                            $('#id_penerimaan').append('<option value="">-- Pilih Penerimaan --</option>');
                            
                            $.each(response.data, function(index, penerimaan) {
                                var tanggal = new Date(penerimaan.tanggal_penerimaan);
                                var formattedDate = String(tanggal.getDate()).padStart(2, '0') + '/' + 
                                                   String(tanggal.getMonth() + 1).padStart(2, '0') + '/' + 
                                                   tanggal.getFullYear();
                                
                                $('#id_penerimaan').append('<option value="' + penerimaan.id + '" data-pembelian="' + penerimaan.id_pembelian + '">' + 
                                    penerimaan.nomor_penerimaan + ' - ' + formattedDate + '</option>');
                            });
                            
                            $('#id_penerimaan').select2('destroy').select2();
                        }
                    }
                });
                
                // Hide detail items section
                $('#detail-items-section').hide();
            }
        });

        // Handle penerimaan selection
        $('#id_penerimaan').change(function() {
            var id_penerimaan = $(this).val();
            if (id_penerimaan) {
                // Show detail items section
                $('#detail-items-section').show();
                $('#detail-items-container').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading items...</div>');
                
                // Load detail items form
                $.ajax({
                    url: "<?php echo site_url('returpembelian/form_detail_item') ?>",
                    type: "POST",
                    data: {
                        id_penerimaan: id_penerimaan,
                        id_retur: $('[name="id"]').val()
                    },
                    dataType: "HTML",
                    success: function(response) {
                        $('#detail-items-container').html(response);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        $('#detail-items-container').html('<div class="alert alert-danger">Terjadi kesalahan saat memuat data item.</div>');
                    }
                });
                
                // Get penerimaan info to update supplier if needed
                $.ajax({
                    url: "<?php echo site_url('returpembelian/get_penerimaan_info') ?>",
                    type: "POST",
                    data: {
                        id: id_penerimaan
                    },
                    dataType: "JSON",
                    success: function(response) {
                        if (response.status) {
                            // Set supplier if not already set
                            if (!$('#id_supplier').val()) {
                                $('#id_supplier_display').val(response.data.id_supplier).trigger('change');
                                $('#id_supplier').val(response.data.id_supplier);
                                $('#id_supplier_hidden').val(response.data.id_supplier);
                            }
                            
                            // Set pembelian if not already set
                            if (!$('#id_pembelian').val()) {
                                $('#id_pembelian').val(response.data.id_pembelian).trigger('change');
                            }
                        }
                    }
                });
            } else {
                // Hide detail items section
                $('#detail-items-section').hide();
            }
        });
        
        // Function untuk set nilai penerimaan saat edit
        window.setPenerimaanForEdit = function(penerimaanId) {
            selectedPenerimaanId = penerimaanId;
        };
    });
</script>