<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Tambah Stok Opname</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('StokOpname') ?>">Stok Opname</a></li>
                        <li class="breadcrumb-item active">Tambah</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Form Tambah Stok Opname</h3>
                            <div class="card-tools">
                                <a href="<?= base_url('StokOpname') ?>" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> Kembali
                                </a>
                            </div>
                        </div>
                        
                        <form id="form-opname" method="post">
                            <div class="card-body">
                                <div class="form-group row">
                                    <label for="nomor_opname" class="col-sm-3 col-form-label">Nomor Opname</label>
                                    <div class="col-sm-9">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="nomor_opname" id="nomor_opname" placeholder="Nomor akan di-generate otomatis" autocomplete="off">
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-secondary" id="btn-generate-nomor" onclick="generateNomor()" title="Generate Nomor">
                                                    <i class="fas fa-sync"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">Format: SO-2025-001, SO-2025-002, dst. Kosongkan untuk generate otomatis.</small>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="tanggal_opname" class="col-sm-3 col-form-label">Tanggal Opname <span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="date" class="form-control" name="tanggal_opname" id="tanggal_opname" value="<?= date('Y-m-d') ?>" required>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="id_gudang" class="col-sm-3 col-form-label">Gudang <span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <select class="form-control select2" name="id_gudang" id="id_gudang" required style="width: 100%;">
                                            <option value="">-- Pilih Gudang --</option>
                                            <?php foreach ($gudang_list as $gudang): ?>
                                                <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                                    <div class="col-sm-9">
                                        <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan opname (opsional)" rows="3"></textarea>
                                        <small class="form-text text-muted">Informasi tambahan mengenai stok opname ini.</small>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Auto Generate Detail</label>
                                    <div class="col-sm-9">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="auto_generate" id="auto_generate" value="1">
                                            <label class="form-check-label" for="auto_generate">
                                                Generate detail otomatis untuk semua barang di gudang yang dipilih
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">Jika dicentang, sistem akan otomatis membuat detail opname untuk semua barang yang ada di gudang.</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer">
                                <div class="row">
                                    <div class="col-sm-9 offset-sm-3">
                                        <button type="submit" class="btn btn-primary" id="btn-save">
                                            <i class="fas fa-save"></i> Simpan
                                        </button>
                                        <a href="<?= base_url('StokOpname') ?>" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Batal
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4'
    });
    
    // Form submission
    $('#form-opname').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        
        $.ajax({
            url: '<?= base_url('StokOpname/insert') ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#btn-save').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
                $('.help-block').text('');
            },
            success: function(response) {
                if (response.status) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data stok opname berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        if (result.value) {
                            window.location.href = '<?= base_url('StokOpname') ?>';
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menyimpan data.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON;
                    $.each(errors, function(field, message) {
                        $('[name="' + field + '"]').closest('.form-group').find('.help-block').text(message);
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            complete: function() {
                $('#btn-save').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
            }
        });
    });
});

// Function untuk generate nomor otomatis
function generateNomor() {
    $.ajax({
        url: '<?= base_url('StokOpname/generate_nomor') ?>',
        type: 'GET',
        dataType: 'json',
        beforeSend: function() {
            $('#btn-generate-nomor').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
        },
        success: function(response) {
            $('#nomor_opname').val(response.nomor);
        },
        error: function() {
            Swal.fire({
                title: 'Error!',
                text: 'Gagal generate nomor.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        },
        complete: function() {
            $('#btn-generate-nomor').prop('disabled', false).html('<i class="fas fa-sync"></i>');
        }
    });
}
</script>