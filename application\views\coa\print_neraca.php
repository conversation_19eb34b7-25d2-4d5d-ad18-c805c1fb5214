<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Neraca',
    'document_number' => 'NRC-' . date('Ymd-His'),
    'document_title' => 'NERACA'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Laporan',
        'items' => [
            ['label' => 'Per Tanggal', 'value' => '<strong>' . format_date_indonesia($tanggal) . '</strong>'],
            ['label' => 'Tanggal Cetak', 'value' => format_date_indonesia(date('Y-m-d'), true)],
            ['label' => 'Mata Uang', 'value' => 'IDR (Rupiah)'],
            ['label' => 'Basis', 'value' => 'Akrual']
        ]
    ]
];

// Initialize totals
$total_aktiva_lancar = 0;
$total_aktiva_tetap = 0;
$total_aktiva_lain = 0;
$total_hutang_lancar = 0;
$total_hutang_jangka_panjang = 0;
$total_modal = 0;

// Build content
$content = '';
$content .= create_info_section($info_sections);

// Create two-column layout for Neraca
$content .= '<div style="display: flex; gap: 20px;">';

// LEFT SIDE - AKTIVA
$content .= '<div style="flex: 1;">';

// AKTIVA LANCAR SECTION
$aktiva_headers = [
    ['label' => 'Nama Akun', 'width' => '70%'],
    ['label' => 'Jumlah', 'width' => '30%', 'align' => 'right']
];

if (!empty($aktiva_lancar_list)) {
    $aktiva_lancar_data = [];
    foreach ($aktiva_lancar_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $aktiva_lancar_data[] = [
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_aktiva_lancar += $item->saldo;
    }
    
    $aktiva_lancar_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL AKTIVA LANCAR</strong>', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_aktiva_lancar) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-money"></span>AKTIVA LANCAR', $aktiva_headers, $aktiva_lancar_data, $aktiva_lancar_options);
}

// AKTIVA TETAP SECTION
if (!empty($aktiva_tetap_list)) {
    $aktiva_tetap_data = [];
    foreach ($aktiva_tetap_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $aktiva_tetap_data[] = [
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_aktiva_tetap += $item->saldo;
    }
    
    $aktiva_tetap_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL AKTIVA TETAP</strong>', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_aktiva_tetap) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-warehouse"></span>AKTIVA TETAP', $aktiva_headers, $aktiva_tetap_data, $aktiva_tetap_options);
}

// AKTIVA LAIN-LAIN SECTION
if (!empty($aktiva_lain_list)) {
    $aktiva_lain_data = [];
    foreach ($aktiva_lain_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $aktiva_lain_data[] = [
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_aktiva_lain += $item->saldo;
    }
    
    $aktiva_lain_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL AKTIVA LAIN-LAIN</strong>', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_aktiva_lain) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-package"></span>AKTIVA LAIN-LAIN', $aktiva_headers, $aktiva_lain_data, $aktiva_lain_options);
}

// TOTAL AKTIVA
$total_aktiva = $total_aktiva_lancar + $total_aktiva_tetap + $total_aktiva_lain;
$content .= '<div class="summary-section mb-20">';
$total_aktiva_items = [
    ['label' => 'TOTAL AKTIVA', 'value' => format_currency($total_aktiva), 'class' => 'total-final']
];
$content .= create_summary_table($total_aktiva_items);
$content .= '</div>';

$content .= '</div>'; // End left side

// RIGHT SIDE - PASIVA
$content .= '<div style="flex: 1;">';

// HUTANG LANCAR SECTION
if (!empty($hutang_lancar_list)) {
    $hutang_lancar_data = [];
    foreach ($hutang_lancar_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $hutang_lancar_data[] = [
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_hutang_lancar += $item->saldo;
    }
    
    $hutang_lancar_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL HUTANG LANCAR</strong>', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_hutang_lancar) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-warning"></span>HUTANG LANCAR', $aktiva_headers, $hutang_lancar_data, $hutang_lancar_options);
}

// HUTANG JANGKA PANJANG SECTION
if (!empty($hutang_jangka_panjang_list)) {
    $hutang_jangka_panjang_data = [];
    foreach ($hutang_jangka_panjang_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $hutang_jangka_panjang_data[] = [
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_hutang_jangka_panjang += $item->saldo;
    }
    
    $hutang_jangka_panjang_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL HUTANG JANGKA PANJANG</strong>', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_hutang_jangka_panjang) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-warning"></span>HUTANG JANGKA PANJANG', $aktiva_headers, $hutang_jangka_panjang_data, $hutang_jangka_panjang_options);
}

// MODAL SECTION
if (!empty($modal_list)) {
    $modal_data = [];
    foreach ($modal_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $modal_data[] = [
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_modal += $item->saldo;
    }
    
    $modal_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL MODAL</strong>', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_modal) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-user"></span>MODAL', $aktiva_headers, $modal_data, $modal_options);
}

// TOTAL PASIVA
$total_pasiva = $total_hutang_lancar + $total_hutang_jangka_panjang + $total_modal;
$content .= '<div class="summary-section mb-20">';
$total_pasiva_items = [
    ['label' => 'TOTAL PASIVA', 'value' => format_currency($total_pasiva), 'class' => 'total-final']
];
$content .= create_summary_table($total_pasiva_items);
$content .= '</div>';

$content .= '</div>'; // End right side
$content .= '</div>'; // End two-column layout

// BALANCE VALIDATION
$is_balanced = ($total_aktiva == $total_pasiva);
$content .= '<div class="notes-section">';
$content .= '<div class="notes-title">Validasi Keseimbangan Neraca:</div>';
$content .= '<div class="notes-content text-center">';
if ($is_balanced) {
    $content .= '<span class="color-success font-bold text-large">✅ NERACA SEIMBANG</span><br>';
    $content .= 'Total Aktiva = Total Pasiva = ' . format_currency($total_aktiva);
} else {
    $content .= '<span class="color-danger font-bold text-large">❌ NERACA TIDAK SEIMBANG</span><br>';
    $content .= 'Selisih: ' . format_currency(abs($total_aktiva - $total_pasiva)) . '<br>';
    $content .= '<small>Harap periksa kembali pencatatan transaksi</small>';
}
$content .= '</div>';
$content .= '</div>';

// FINANCIAL RATIOS ANALYSIS
$content .= '<div class="summary-section">';
$content .= '<div class="summary-left">';

$ratio_content = 'Analisis Rasio Keuangan:' . "\n\n";

// Liquidity Ratios
if ($total_hutang_lancar > 0) {
    $current_ratio = $total_aktiva_lancar / $total_hutang_lancar;
    $ratio_content .= 'Rasio Likuiditas:' . "\n";
    $ratio_content .= '• Current Ratio: ' . number_format($current_ratio, 2) . ':1' . "\n";
    
    if ($current_ratio >= 2) {
        $ratio_content .= '  Status: ✅ Sangat Likuid' . "\n";
    } elseif ($current_ratio >= 1.5) {
        $ratio_content .= '  Status: ✅ Likuid' . "\n";
    } elseif ($current_ratio >= 1) {
        $ratio_content .= '  Status: ⚠️ Cukup Likuid' . "\n";
    } else {
        $ratio_content .= '  Status: ❌ Kurang Likuid' . "\n";
    }
    $ratio_content .= "\n";
}

// Solvency Ratios
if ($total_aktiva > 0) {
    $total_hutang = $total_hutang_lancar + $total_hutang_jangka_panjang;
    $debt_ratio = $total_hutang / $total_aktiva;
    $equity_ratio = $total_modal / $total_aktiva;
    
    $ratio_content .= 'Rasio Solvabilitas:' . "\n";
    $ratio_content .= '• Debt Ratio: ' . number_format($debt_ratio * 100, 2) . '%' . "\n";
    $ratio_content .= '• Equity Ratio: ' . number_format($equity_ratio * 100, 2) . '%' . "\n";
    
    if ($debt_ratio <= 0.3) {
        $ratio_content .= '  Status: ✅ Struktur Modal Konservatif' . "\n";
    } elseif ($debt_ratio <= 0.5) {
        $ratio_content .= '  Status: ✅ Struktur Modal Seimbang' . "\n";
    } elseif ($debt_ratio <= 0.7) {
        $ratio_content .= '  Status: ⚠️ Struktur Modal Agresif' . "\n";
    } else {
        $ratio_content .= '  Status: ❌ Risiko Tinggi' . "\n";
    }
}

$content .= create_notes_section('Analisis Rasio', $ratio_content);

$content .= '</div>';
$content .= '<div class="summary-right">';

// Summary comparison table
$comparison_items = [
    ['label' => 'Total Aktiva', 'value' => format_currency($total_aktiva)],
    ['label' => 'Total Hutang', 'value' => format_currency($total_hutang_lancar + $total_hutang_jangka_panjang)],
    ['label' => 'Total Modal', 'value' => format_currency($total_modal)],
    ['label' => 'Total Pasiva', 'value' => format_currency($total_pasiva)],
    ['label' => 'Selisih', 'value' => format_currency($total_aktiva - $total_pasiva), 'class' => $is_balanced ? 'color-success' : 'color-danger']
];

$content .= create_summary_table($comparison_items);

$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => '(............................)',
        'position' => 'Staff Accounting'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Accounting'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Direktur'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>