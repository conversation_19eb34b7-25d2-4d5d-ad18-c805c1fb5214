<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Transfer Stok
 * Mengatur transfer stok dan detailnya
 */
class TransferStok extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_transfer_stok', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'transfer_stok/transfer_stok', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $list = $this->Mod_transfer_stok->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $transfer) {
            $no++;
            $row = array();
            $row[] = $transfer->nomor_transfer;
            $row[] = date('d/m/Y', strtotime($transfer->tanggal_transfer));
            $row[] = $transfer->nama_gudang_asal;
            $row[] = $transfer->nama_gudang_tujuan;
            $row[] = $transfer->total_item;
            $row[] = number_format($transfer->total_qty, 0);
            
            // Status badge
            $status_badge = '';
            switch ($transfer->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-warning">DRAFT</span>';
                    break;
                case 'dikirim':
                    $status_badge = '<span class="badge badge-info">DIKIRIM</span>';
                    break;
                case 'diterima':
                    $status_badge = '<span class="badge badge-success">DITERIMA</span>';
                    break;
                case 'batal':
                    $status_badge = '<span class="badge badge-danger">BATAL</span>';
                    break;
            }
            $row[] = $status_badge;
            
            // Action buttons
            $actions = '';
            if ($transfer->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $transfer->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $transfer->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success kirim" href="javascript:void(0)" title="Kirim" onclick="kirim(' . $transfer->id . ')"><i class="fas fa-paper-plane"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="print_transfer(' . $transfer->id . ')"><i class="fas fa-print"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $transfer->id . ')"><i class="fas fa-trash"></i></a>';
            } else if ($transfer->status == 'dikirim') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $transfer->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success terima" href="javascript:void(0)" title="Terima" onclick="terima(' . $transfer->id . ')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="print_transfer(' . $transfer->id . ')"><i class="fas fa-print"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $transfer->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="print_transfer(' . $transfer->id . ')"><i class="fas fa-print"></i></a>';
            }
            
            $row[] = $actions;
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_transfer_stok->count_all(),
            "recordsFiltered" => $this->Mod_transfer_stok->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_transfer');
        if (empty($nomor)) {
            $nomor = $this->Mod_transfer_stok->generate_nomor();
        }

        $save = array(
            'nomor_transfer' => $nomor,
            'tanggal_transfer' => $this->input->post('tanggal_transfer'),
            'gudang_asal_id' => $this->input->post('gudang_asal_id'),
            'gudang_tujuan_id' => $this->input->post('gudang_tujuan_id'),
            'keterangan' => $this->input->post('keterangan'),
            'status' => 'draft',
            'dibuat_oleh' => $this->session->userdata('username'),
        );
        
        $id_transfer = $this->Mod_transfer_stok->insert($save);
        
        echo json_encode(array(
            "status" => TRUE,
            "id_transfer" => $id_transfer
        ));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');

        $save = array(
            'tanggal_transfer' => $this->input->post('tanggal_transfer'),
            'gudang_asal_id' => $this->input->post('gudang_asal_id'),
            'gudang_tujuan_id' => $this->input->post('gudang_tujuan_id'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_transfer_stok->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_transfer_stok->get($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['gudang_list'] = $this->Mod_transfer_stok->get_gudang_dropdown();
        $this->load->view('transfer_stok/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        
        try {
            // Cek apakah transfer exists
            $transfer = $this->Mod_transfer_stok->get($id);
            if (!$transfer) {
                throw new Exception('Data transfer tidak ditemukan');
            }
            
            // Cek status transfer - hanya bisa hapus jika status draft
            if ($transfer->status != 'draft') {
                $status_label = ucfirst($transfer->status);
                throw new Exception("Transfer dengan status {$status_label} tidak dapat dihapus!");
            }
            
            // Hapus detail terlebih dahulu
            $this->Mod_transfer_stok->delete_detail_by_transfer($id);
            
            // Hapus header transfer
            $this->Mod_transfer_stok->delete($id);
            
            echo json_encode(array(
                "status" => TRUE,
                "message" => "Data transfer berhasil dihapus"
            ));
            
        } catch (Exception $e) {
            log_message('error', 'Error in TransferStok::delete(): ' . $e->getMessage());
            echo json_encode(array(
                "status" => FALSE,
                "message" => $e->getMessage()
            ));
        }
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_transfer_stok->generate_nomor();
        echo json_encode(array('nomor' => $nomor));
    }

    // Update status transfer
    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $user = $this->session->userdata('username');
        
        if ($this->Mod_transfer_stok->can_update_status($id, $status)) {
            $result = $this->Mod_transfer_stok->update_status($id, $status, $user);
            
            if ($result) {
                echo json_encode(array(
                    "status" => TRUE, 
                    "message" => "Status transfer berhasil diupdate"
                ));
        } else {
                echo json_encode(array(
                    "status" => FALSE, 
                    "message" => "Gagal update status transfer"
                ));
            }
        } else {
            echo json_encode(array(
                "status" => FALSE, 
                "message" => "Status tidak dapat diupdate"
            ));
        }
    }

    // ===== DETAIL TRANSFER METHODS =====

    public function detail($id)
    {
        try {
            // Get transfer data
            $transfer = $this->Mod_transfer_stok->get($id);
            if (!$transfer) {
                throw new Exception('Data transfer tidak ditemukan');
            }

            // Get detail list
            $detail_list = $this->Mod_transfer_stok->get_detail($id);
            
            // Get available items
            $barang_available = $this->Mod_transfer_stok->get_barang_available($id);

            // Load view with data
            $data = array(
                'transfer' => $transfer,
                'detail_list' => $detail_list,
                'barang_available' => $barang_available
            );
            
            $this->load->view('transfer_stok/detail_transfer', $data);
            
        } catch (Exception $e) {
            log_message('error', 'Error in TransferStok::detail(): ' . $e->getMessage());
            echo json_encode(array(
                'status' => false,
                'message' => $e->getMessage()
            ));
        }
    }

    /**
     * Method untuk mendapatkan hanya bagian tabel detail
     * Digunakan untuk reload tabel tanpa reload seluruh modal
     */
    public function get_detail_table($id)
    {
        $data['transfer'] = $this->Mod_transfer_stok->get($id);
        $data['detail_list'] = $this->Mod_transfer_stok->get_detail($id);
        $this->load->view('transfer_stok/detail_table_only', $data);
    }

    public function insert_detail()
    {
        $this->_validate_detail();

        $id_transfer = $this->input->post('id_transfer');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');

        // Cek stok di gudang asal
        $stok = $this->Mod_transfer_stok->get_stok_barang($id_barang, $id_gudang);
        if ($stok < $this->input->post('qty')) {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Stok tidak mencukupi. Stok tersedia: " . $stok
            ));
            return;
        }

        $save = array(
            'transfer_stok_id' => $id_transfer,
            'barang_id' => $id_barang,
            'satuan_id' => $this->input->post('satuan_id'),
            'qty' => $this->input->post('qty'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_transfer_stok->insert_detail($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $this->_validate_detail();

        $id = $this->input->post('id');
        $id_transfer = $this->input->post('id_transfer');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');

        // Cek stok di gudang asal
        $stok = $this->Mod_transfer_stok->get_stok_barang($id_barang, $id_gudang);
        if ($stok < $this->input->post('qty')) {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Stok tidak mencukupi. Stok tersedia: " . $stok
            ));
            return;
        }

        $save = array(
            'qty' => $this->input->post('qty'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_transfer_stok->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_transfer_stok->get_detail_by_id($id);
        echo json_encode($data);
    }

    public function delete_detail($id = null)
    {
        try {
            // Ambil ID dari URL parameter atau POST data
            if (!$id) {
                $id = $this->input->post('id');
            }
            
            // Validasi ID
            if (empty($id) || !is_numeric($id)) {
                throw new Exception('ID detail tidak valid');
            }
            
            // Cek apakah detail exists
            $detail = $this->Mod_transfer_stok->get_detail_by_id($id);
            if (!$detail) {
                throw new Exception('Data detail tidak ditemukan');
            }
            
            // Cek status transfer - hanya bisa hapus jika status draft
            $transfer = $this->Mod_transfer_stok->get($detail->transfer_stok_id);
            if ($transfer->status != 'draft') {
                throw new Exception('Tidak dapat menghapus detail. Transfer sudah tidak dalam status draft.');
            }
            
            // Hapus detail
            $this->Mod_transfer_stok->delete_detail($id);
            
            echo json_encode(array(
                "status" => TRUE,
                "message" => "Data detail berhasil dihapus"
            ));
            
        } catch (Exception $e) {
            log_message('error', 'Error in TransferStok::delete_detail(): ' . $e->getMessage());
            echo json_encode(array(
                "status" => FALSE,
                "message" => $e->getMessage()
            ));
        }
    }

    public function get_stok_barang()
    {
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');

        $stok = $this->Mod_transfer_stok->get_stok_barang($id_barang, $id_gudang);

        echo json_encode(array('stok' => $stok));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $nomor = $this->input->post('nomor_transfer');
        $id = $this->input->post('id');

        // Validasi nomor transfer
        if (empty($nomor)) {
            // Nomor boleh kosong, akan di-generate otomatis
        } else {
            if (!preg_match('/^TSK-\d{8}-\d{4}$/', $nomor)) {
                $data['inputerror'][] = 'nomor_transfer';
                $data['error_string'][] = 'Format nomor harus TSK-YYYYMMDD-XXXX (contoh: TSK-20250628-0001)';
                $data['status'] = FALSE;
            } else if ($this->Mod_transfer_stok->check_nomor_exists($nomor, $id)) {
                $data['inputerror'][] = 'nomor_transfer';
                $data['error_string'][] = 'Nomor transfer sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi tanggal
        if (empty($this->input->post('tanggal_transfer'))) {
            $data['inputerror'][] = 'tanggal_transfer';
            $data['error_string'][] = 'Tanggal transfer wajib diisi';
            $data['status'] = FALSE;
        }

        // Validasi gudang asal
        if (empty($this->input->post('gudang_asal_id'))) {
            $data['inputerror'][] = 'gudang_asal_id';
            $data['error_string'][] = 'Gudang asal wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi gudang tujuan
        if (empty($this->input->post('gudang_tujuan_id'))) {
            $data['inputerror'][] = 'gudang_tujuan_id';
            $data['error_string'][] = 'Gudang tujuan wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi gudang asal dan tujuan tidak boleh sama
        if ($this->input->post('gudang_asal_id') == $this->input->post('gudang_tujuan_id')) {
            $data['inputerror'][] = 'gudang_tujuan_id';
            $data['error_string'][] = 'Gudang tujuan tidak boleh sama dengan gudang asal';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    private function _validate_detail()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        // Validasi barang (untuk insert baru)
        if (empty($this->input->post('id')) && empty($this->input->post('id_barang'))) {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi satuan (untuk insert baru)
        if (empty($this->input->post('id')) && empty($this->input->post('satuan_id'))) {
            $data['inputerror'][] = 'id_barang'; // Error tetap di field barang karena satuan terkait
            $data['error_string'][] = 'Satuan barang tidak ditemukan. Pilih barang yang valid.';
            $data['status'] = FALSE;
        }

        // Validasi qty
        $qty = $this->input->post('qty');
        if (empty($qty) && $qty !== '0') {
            $data['inputerror'][] = 'qty';
            $data['error_string'][] = 'Qty wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty) || $qty <= 0) {
            $data['inputerror'][] = 'qty';
            $data['error_string'][] = 'Qty harus berupa angka positif';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    public function cetak_transfer($id)
    {
        // Load data untuk cetak
        $data['transfer'] = $this->Mod_transfer_stok->get($id);
        $data['detail_list'] = $this->Mod_transfer_stok->get_detail($id);
        
        // Load view cetak
        $this->load->view('transfer_stok/cetak_transfer', $data);
    }
}