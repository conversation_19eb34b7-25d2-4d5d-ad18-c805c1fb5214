<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Settlement
 * Mengelola proses rekonsiliasi dan verifikasi data transaksi
 */
class Settlement extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_settlement');
        $this->load->helper('myfunction_helper');
        $this->load->library('Fungsi');
        $this->load->library('form_validation');
        $this->load->library('datatables');
    }

    public function index()
    {
        $this->template->load('layoutbackend', 'settlement/index');
    }

    /**
     * Settlement Penjualan (Faktur vs Pengiriman)
     */
    public function penjualan()
    {
        $data['tanggal_awal'] = date('Y-m-01');
        $data['tanggal_akhir'] = date('Y-m-d');
        $this->template->load('layoutbackend', 'settlement/penjualan', $data);
    }

    public function penjualan_data()
    {
        try {
            $tanggal_awal = $this->input->post('tanggal_awal');
            $tanggal_akhir = $this->input->post('tanggal_akhir');
            $status = $this->input->post('status');

            $filter = [
                'tanggal_awal' => $tanggal_awal,
                'tanggal_akhir' => $tanggal_akhir,
                'status' => $status
            ];

            $data = $this->Mod_settlement->get_penjualan_settlement($filter);
            
            // Pastikan response dalam format yang benar untuk DataTables
            $response = [
                'data' => $data ? $data : []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            // Log error dan return response kosong
            log_message('error', 'Settlement penjualan_data error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['data' => []]);
        }
    }

    public function penjualan_detail($id_pengiriman)
    {
        $data['pengiriman'] = $this->Mod_settlement->get_pengiriman_by_id($id_pengiriman);
        $data['detail'] = $this->Mod_settlement->get_pengiriman_detail($id_pengiriman);
        $data['faktur'] = $this->Mod_settlement->get_faktur_by_pengiriman($id_pengiriman);
        
        if ($data['faktur']) {
            $data['faktur_detail'] = $this->Mod_settlement->get_faktur_detail($data['faktur']->id);
        } else {
            $data['faktur_detail'] = [];
        }
        
        $this->template->load('layoutbackend', 'settlement/penjualan_detail', $data);
    }

    public function penjualan_create_faktur($id_pengiriman)
    {
        // Redirect ke controller FakturPenjualan untuk membuat faktur baru
        redirect('FakturPenjualan/create/' . $id_pengiriman);
    }

    /**
     * Settlement Pembelian (Pembelian vs Penerimaan)
     */
    public function pembelian()
    {
        $data['tanggal_awal'] = date('Y-m-01');
        $data['tanggal_akhir'] = date('Y-m-d');
        $this->template->load('layoutbackend', 'settlement/pembelian', $data);
    }

    public function pembelian_data()
    {
        try {
            $tanggal_awal = $this->input->post('tanggal_awal');
            $tanggal_akhir = $this->input->post('tanggal_akhir');
            $status = $this->input->post('status');

            $filter = [
                'tanggal_awal' => $tanggal_awal,
                'tanggal_akhir' => $tanggal_akhir,
                'status' => $status
            ];

            $data = $this->Mod_settlement->get_pembelian_settlement($filter);
            
            // Pastikan response dalam format yang benar untuk DataTables
            $response = [
                'data' => $data ? $data : []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            // Log error dan return response kosong
            log_message('error', 'Settlement pembelian_data error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['data' => []]);
        }
    }

    public function pembelian_detail($id_pembelian)
    {
        $data['pembelian'] = $this->Mod_settlement->get_pembelian_by_id($id_pembelian);
        $data['detail'] = $this->Mod_settlement->get_pembelian_detail($id_pembelian);
        $data['penerimaan'] = $this->Mod_settlement->get_penerimaan_by_pembelian($id_pembelian);
        
        $this->template->load('layoutbackend', 'settlement/pembelian_detail', $data);
    }

    /**
     * Settlement Pembayaran (Faktur vs Pembayaran)
     */
    public function pembayaran()
    {
        $data['tanggal_awal'] = date('Y-m-01');
        $data['tanggal_akhir'] = date('Y-m-d');
        $this->template->load('layoutbackend', 'settlement/pembayaran', $data);
    }

    public function pembayaran_data()
    {
        try {
            $tanggal_awal = $this->input->post('tanggal_awal');
            $tanggal_akhir = $this->input->post('tanggal_akhir');
            $status = $this->input->post('status');
            $jenis = $this->input->post('jenis');

            $filter = [
                'tanggal_awal' => $tanggal_awal,
                'tanggal_akhir' => $tanggal_akhir,
                'status' => $status,
                'jenis' => $jenis
            ];

            $data = $this->Mod_settlement->get_pembayaran_settlement($filter);
            
            // Pastikan response dalam format yang benar untuk DataTables
            $response = [
                'data' => $data ? $data : []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            // Log error dan return response kosong
            log_message('error', 'Settlement pembayaran_data error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['data' => []]);
        }
    }

    public function pembayaran_detail($jenis, $id)
    {
        if ($jenis == 'penjualan') {
            $data['faktur'] = $this->Mod_settlement->get_faktur_by_id($id);
            $data['pembayaran'] = $this->Mod_settlement->get_pembayaran_faktur($id);
            $data['jenis'] = 'penjualan';
            $this->template->load('layoutbackend', 'settlement/pembayaran_detail_penjualan', $data);
        } else {
            $data['pembelian'] = $this->Mod_settlement->get_pembelian_by_id($id);
            $data['pembayaran'] = $this->Mod_settlement->get_pembayaran_pembelian($id);
            $data['jenis'] = 'pembelian';
            $this->template->load('layoutbackend', 'settlement/pembayaran_detail_pembelian', $data);
        }
    }

    /**
     * Settlement Stok (Stok Fisik vs Stok Sistem)
     */
    public function stok()
    {
        try {
            $data['gudang'] = $this->Mod_settlement->get_gudang_aktif();
            $this->template->load('layoutbackend', 'settlement/stok', $data);
        } catch (Exception $e) {
            log_message('error', 'Error in Settlement::stok(): ' . $e->getMessage());
            $data['gudang'] = [];
            $this->template->load('layoutbackend', 'settlement/stok', $data);
        }
    }

    public function stok_data()
    {
        try {
            $id_gudang = $this->input->post('id_gudang');
            $filter_barang = $this->input->post('filter_barang');
            $status = $this->input->post('status');

            $filter = [
                'id_gudang' => $id_gudang,
                'filter_barang' => $filter_barang,
                'status' => $status
            ];

            $data = $this->Mod_settlement->get_stok_settlement($filter);
            
            // Pastikan response dalam format yang benar untuk DataTables
            $response = [
                'data' => $data ? $data : []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            // Log error dan return response kosong
            log_message('error', 'Settlement stok_data error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['data' => []]);
        }
    }

    public function stok_detail($id_barang, $id_gudang)
    {
        $data['barang'] = $this->Mod_settlement->get_barang_by_id($id_barang);
        $data['gudang'] = $this->Mod_settlement->get_gudang_by_id($id_gudang);
        
        // Get stok sistem
        $stok_sistem_qty = $this->Mod_settlement->get_stok_sistem($id_barang, $id_gudang);
        $data['stok_sistem'] = (object) ['stok_tersedia' => $stok_sistem_qty];
        
        $data['stok_opname'] = $this->Mod_settlement->get_stok_opname_terakhir($id_barang, $id_gudang);
        
        // Calculate selisih if stok_opname exists
        if ($data['stok_opname']) {
            $selisih = $data['stok_opname']->qty_fisik - $stok_sistem_qty;
            $data['stok_opname']->selisih = $selisih;
        }
        
        $data['mutasi'] = $this->Mod_settlement->get_mutasi_stok($id_barang, $id_gudang);
        
        $this->template->load('layoutbackend', 'settlement/stok_detail', $data);
    }

    public function stok_create_penyesuaian($id_barang, $id_gudang)
    {
        // Redirect ke controller Penyesuaian untuk membuat penyesuaian stok
        redirect('Penyesuaian/create/' . $id_barang . '/' . $id_gudang);
    }

    /**
     * Settlement Kas dan Bank
     */
    public function kas_bank()
    {
        $data['tanggal_awal'] = date('Y-m-01');
        $data['tanggal_akhir'] = date('Y-m-d');
        $data['bank'] = $this->Mod_settlement->get_bank_list();
        $this->template->load('layoutbackend', 'settlement/kas_bank', $data);
    }

    public function kas_bank_data()
    {
        try {
            $tanggal_awal = $this->input->post('tanggal_awal');
            $tanggal_akhir = $this->input->post('tanggal_akhir');
            $id_bank = $this->input->post('id_bank');

            $filter = [
                'tanggal_awal' => $tanggal_awal,
                'tanggal_akhir' => $tanggal_akhir,
                'id_bank' => $id_bank
            ];

            $data = $this->Mod_settlement->get_kas_bank_settlement($filter);
            
            // Pastikan response dalam format yang benar untuk DataTables
            $response = [
                'data' => $data ? $data : []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            // Log error dan return response kosong
            log_message('error', 'Settlement kas_bank_data error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['data' => []]);
        }
    }

    public function kas_bank_detail($id_bank, $tanggal)
    {
        $data['bank'] = $this->Mod_settlement->get_bank_by_id($id_bank);
        $data['tanggal'] = $tanggal;
        $data['transaksi_sistem'] = $this->Mod_settlement->get_transaksi_kas_bank($id_bank, $tanggal);
        $data['transaksi_bank'] = $this->Mod_settlement->get_bank_statement($id_bank, $tanggal);
        
        $this->template->load('layoutbackend', 'settlement/kas_bank_detail', $data);
    }

    /**
     * Settlement Pajak (PPN Masukan vs PPN Keluaran)
     */
    public function pajak()
    {
        $data['tahun'] = date('Y');
        $data['bulan'] = date('m');
        $this->template->load('layoutbackend', 'settlement/pajak', $data);
    }

    public function pajak_data()
    {
        try {
            $tahun = $this->input->post('tahun');
            $bulan = $this->input->post('bulan');

            $filter = [
                'tahun' => $tahun,
                'bulan' => $bulan
            ];

            $data = $this->Mod_settlement->get_pajak_settlement($filter);
            
            // Pastikan response dalam format yang benar untuk DataTables
            $response = [
                'data' => $data ? $data : []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            // Log error dan return response kosong
            log_message('error', 'Settlement pajak_data error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['data' => []]);
        }
    }

    public function pajak_detail($jenis, $tahun, $bulan)
    {
        $data['jenis'] = $jenis;
        $data['tahun'] = $tahun;
        $data['bulan'] = $bulan;
        
        if ($jenis == 'masukan') {
            $data['transaksi'] = $this->Mod_settlement->get_pajak_masukan_detail($tahun, $bulan);
            $this->template->load('layoutbackend', 'settlement/pajak_masukan_detail', $data);
        } else {
            $data['transaksi'] = $this->Mod_settlement->get_pajak_keluaran_detail($tahun, $bulan);
            $this->template->load('layoutbackend', 'settlement/pajak_keluaran_detail', $data);
        }
    }



    /**
     * Settlement Retur (Retur Penjualan dan Pembelian)
     */
    public function retur()
    {
        $data['tanggal_awal'] = date('Y-m-01');
        $data['tanggal_akhir'] = date('Y-m-d');
        $this->template->load('layoutbackend', 'settlement/retur', $data);
    }

    public function retur_data()
    {
        try {
            $tanggal_awal = $this->input->post('tanggal_awal');
            $tanggal_akhir = $this->input->post('tanggal_akhir');
            $jenis = $this->input->post('jenis');
            $status = $this->input->post('status');

            $filter = [
                'tanggal_awal' => $tanggal_awal,
                'tanggal_akhir' => $tanggal_akhir,
                'jenis' => $jenis,
                'status' => $status
            ];

            $data = $this->Mod_settlement->get_retur_settlement($filter);
            
            // Pastikan response dalam format yang benar untuk DataTables
            $response = [
                'data' => $data ? $data : []
            ];
            
            header('Content-Type: application/json');
            echo json_encode($response);
        } catch (Exception $e) {
            // Log error dan return response kosong
            log_message('error', 'Settlement retur_data error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['data' => []]);
        }
    }

    public function retur_detail($jenis, $id)
    {
        if ($jenis == 'penjualan') {
            $data['retur'] = $this->Mod_settlement->get_retur_penjualan_by_id($id);
            $data['detail'] = $this->Mod_settlement->get_retur_penjualan_detail($id);
            $data['faktur'] = $this->Mod_settlement->get_faktur_by_id($data['retur']->id_faktur);
            $data['jenis'] = 'penjualan';
            $this->template->load('layoutbackend', 'settlement/retur_penjualan_detail', $data);
        } else {
            $data['retur'] = $this->Mod_settlement->get_retur_pembelian_by_id($id);
            $data['detail'] = $this->Mod_settlement->get_retur_pembelian_detail($id);
            $data['pembelian'] = $this->Mod_settlement->get_pembelian_by_id($data['retur']->id_pembelian);
            $data['jenis'] = 'pembelian';
            $this->template->load('layoutbackend', 'settlement/retur_pembelian_detail', $data);
        }
    }

    /**
     * Export Data Settlement
     */
    public function export($jenis)
    {
        switch ($jenis) {
            case 'penjualan':
                $this->_export_penjualan();
                break;
            case 'pembelian':
                $this->_export_pembelian();
                break;
            case 'pembayaran':
                $this->_export_pembayaran();
                break;
            case 'stok':
                $this->_export_stok();
                break;
            case 'kas_bank':
                $this->_export_kas_bank();
                break;
            case 'pajak':
                $this->_export_pajak();
                break;

            case 'retur':
                $this->_export_retur();
                break;
            default:
                show_error('Jenis export tidak valid');
                break;
        }
    }

    private function _export_penjualan()
    {
        $tanggal_awal = $this->input->get('tanggal_awal');
        $tanggal_akhir = $this->input->get('tanggal_akhir');
        $status = $this->input->get('status');

        $filter = [
            'tanggal_awal' => $tanggal_awal,
            'tanggal_akhir' => $tanggal_akhir,
            'status' => $status
        ];

        $data = $this->Mod_settlement->get_penjualan_settlement($filter);
        
        // Proses export Excel
        $this->load->library('PHPExcel');
        $objPHPExcel = new PHPExcel();
        
        // Set document properties
        $objPHPExcel->getProperties()->setCreator("Toko Elektronik")
                                     ->setLastModifiedBy("Toko Elektronik")
                                     ->setTitle("Settlement Penjualan")
                                     ->setSubject("Settlement Penjualan")
                                     ->setDescription("Settlement Penjualan");
        
        // Add header
        $objPHPExcel->setActiveSheetIndex(0)
                    ->setCellValue('A1', 'No')
                    ->setCellValue('B1', 'Nomor Pengiriman')
                    ->setCellValue('C1', 'Tanggal Pengiriman')
                    ->setCellValue('D1', 'Pelanggan')
                    ->setCellValue('E1', 'Total Item')
                    ->setCellValue('F1', 'Total Qty')
                    ->setCellValue('G1', 'Status Faktur')
                    ->setCellValue('H1', 'Nomor Faktur')
                    ->setCellValue('I1', 'Tanggal Faktur')
                    ->setCellValue('J1', 'Selisih Item')
                    ->setCellValue('K1', 'Selisih Qty')
                    ->setCellValue('L1', 'Selisih Nilai');
        
        // Add data
        $row = 2;
        $no = 1;
        foreach ($data as $item) {
            $objPHPExcel->setActiveSheetIndex(0)
                        ->setCellValue('A' . $row, $no++)
                        ->setCellValue('B' . $row, $item->nomor_pengiriman)
                        ->setCellValue('C' . $row, $item->tanggal_pengiriman)
                        ->setCellValue('D' . $row, $item->nama_pelanggan)
                        ->setCellValue('E' . $row, $item->total_item)
                        ->setCellValue('F' . $row, $item->total_qty)
                        ->setCellValue('G' . $row, $item->status_faktur)
                        ->setCellValue('H' . $row, $item->nomor_faktur)
                        ->setCellValue('I' . $row, $item->tanggal_faktur)
                        ->setCellValue('J' . $row, $item->selisih_item)
                        ->setCellValue('K' . $row, $item->selisih_qty)
                        ->setCellValue('L' . $row, $item->selisih_nilai);
            $row++;
        }
        
        // Set column width
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(5);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(20);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(30);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(20);
        $objPHPExcel->getActiveSheet()->getColumnDimension('I')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('J')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('K')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('L')->setWidth(15);
        
        // Set active sheet index to the first sheet
        $objPHPExcel->setActiveSheetIndex(0);
        
        // Rename worksheet
        $objPHPExcel->getActiveSheet()->setTitle('Settlement Penjualan');
        
        // Set active sheet index to the first sheet
        $objPHPExcel->setActiveSheetIndex(0);
        
        // Redirect output to a client's web browser (Excel5)
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="Settlement_Penjualan_' . date('YmdHis') . '.xls"');
        header('Cache-Control: max-age=0');
        
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save('php://output');
    }

    // Implementasi fungsi export lainnya serupa dengan _export_penjualan
}