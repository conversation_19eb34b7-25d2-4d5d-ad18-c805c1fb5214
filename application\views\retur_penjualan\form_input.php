<form action="#" id="form" class="form-horizontal">
    <input type="hidden" name="id" value="">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group row">
                    <label for="nomor_retur" class="col-sm-3 col-form-label">Nomor Retur</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="nomor_retur" name="nomor_retur" placeholder="Nomor Retur" value="<?= $nomor_retur ?>" readonly>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="tanggal_retur" class="col-sm-3 col-form-label">Tanggal Retur</label>
                    <div class="col-sm-9">
                        <input type="date" class="form-control" id="tanggal_retur" name="tanggal_retur" placeholder="Tanggal Retur" value="<?= date('Y-m-d') ?>">
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="sumber_retur" class="col-sm-3 col-form-label">Sumber Retur</label>
                    <div class="col-sm-9">
                        <select class="form-control" id="sumber_retur" name="sumber_retur">
                            <option value="">-- Pilih Sumber Retur --</option>
                            <option value="faktur">Faktur Penjualan</option>
                            <option value="pengiriman">Pengiriman</option>
                        </select>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row" id="faktur_container" style="display: none;">
                    <label for="id_faktur" class="col-sm-3 col-form-label">Faktur Penjualan</label>
                    <div class="col-sm-9">
                        <select class="form-control select2" id="id_faktur" name="id_faktur" style="width: 100%;">
                            <option value="">-- Pilih Faktur --</option>
                            <?php foreach ($faktur_list as $faktur) : ?>
                                <option value="<?= $faktur->id ?>"><?= $faktur->nomor_faktur ?> - <?= date('d/m/Y', strtotime($faktur->tanggal_faktur)) ?> - <?= $faktur->nama_pelanggan ?></option>
                            <?php endforeach; ?>
                        </select>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row" id="pengiriman_container" style="display: none;">
                    <label for="id_pengiriman" class="col-sm-3 col-form-label">Pengiriman</label>
                    <div class="col-sm-9">
                        <select class="form-control select2" id="id_pengiriman" name="id_pengiriman" style="width: 100%;">
                            <option value="">-- Pilih Pengiriman --</option>
                            <?php foreach ($pengiriman_list as $pengiriman) : ?>
                                <option value="<?= $pengiriman->id ?>"><?= $pengiriman->nomor_pengiriman ?> - <?= date('d/m/Y', strtotime($pengiriman->tanggal_pengiriman)) ?> - <?= $pengiriman->nama_pelanggan ?></option>
                            <?php endforeach; ?>
                        </select>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group row">
                    <label for="id_pelanggan" class="col-sm-3 col-form-label">Pelanggan</label>
                    <div class="col-sm-9">
                        <select class="form-control select2" id="id_pelanggan" name="id_pelanggan" style="width: 100%;">
                            <option value="">-- Pilih Pelanggan --</option>
                            <?php foreach ($pelanggan_list as $pelanggan) : ?>
                                <option value="<?= $pelanggan->id ?>"><?= $pelanggan->nama ?> (<?= $pelanggan->kode ?>)</option>
                            <?php endforeach; ?>
                        </select>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="alasan_retur" class="col-sm-3 col-form-label">Alasan Retur</label>
                    <div class="col-sm-9">
                        <textarea class="form-control" id="alasan_retur" name="alasan_retur" rows="2" placeholder="Alasan Retur"></textarea>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                    <div class="col-sm-9">
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="2" placeholder="Keterangan"></textarea>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detail Items Section -->
        <div class="row mt-3" id="detail-items-section" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title">Detail Item Retur</h3>
                    </div>
                    <div class="card-body" id="detail_items_container">
                        <!-- Detail items will be loaded here -->
                        <div class="text-center">
                            <i class="fa fa-info-circle"></i> Pilih sumber retur terlebih dahulu untuk menampilkan item.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('.select2').select2();
        
        // Sumber retur change handler
        $('#sumber_retur').change(function() {
            var sumber = $(this).val();
            
            // Reset containers
            $('#faktur_container').hide();
            $('#pengiriman_container').hide();
            $('#detail-items-section').hide();
            $('#detail_items_container').html('<div class="text-center"><i class="fa fa-info-circle"></i> Pilih sumber retur terlebih dahulu untuk menampilkan item.</div>');
            
            // Show relevant container
            if (sumber == 'faktur') {
                $('#faktur_container').show();
                $('#id_pengiriman').val('').trigger('change');
            } else if (sumber == 'pengiriman') {
                $('#pengiriman_container').show();
                $('#id_faktur').val('').trigger('change');
            }
        });
        
        // Faktur change handler
        $('#id_faktur').change(function() {
            var id_faktur = $(this).val();
            var id_retur = $('[name="id"]').val();
            
            if (id_faktur) {
                // Get faktur info
                $.ajax({
                    url: "<?= site_url('ReturPenjualan/get_faktur_info') ?>",
                    type: "POST",
                    data: {
                        id: id_faktur
                    },
                    dataType: "JSON",
                    success: function(response) {
                        if (response.status) {
                            // Set pelanggan
                            $('#id_pelanggan').val(response.data.id_pelanggan).trigger('change');
                            
                            // Load detail items form
                            loadDetailItemsForm('faktur', id_faktur, null, id_retur);
                        }
                    }
                });
            }
        });
        
        // Pengiriman change handler
        $('#id_pengiriman').change(function() {
            var id_pengiriman = $(this).val();
            var id_retur = $('[name="id"]').val();
            
            if (id_pengiriman) {
                // Get pengiriman info
                $.ajax({
                    url: "<?= site_url('ReturPenjualan/get_pengiriman_info') ?>",
                    type: "POST",
                    data: {
                        id: id_pengiriman
                    },
                    dataType: "JSON",
                    success: function(response) {
                        if (response.status) {
                            // Set pelanggan
                            $('#id_pelanggan').val(response.data.id_pelanggan).trigger('change');
                            
                            // Load detail items form
                            loadDetailItemsForm('pengiriman', null, id_pengiriman, id_retur);
                        }
                    }
                });
            }
        });
    });
    
    function loadDetailItemsForm(sumber, id_faktur, id_pengiriman, id_retur) {
        $('#detail_items_container').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');
        $('#detail-items-section').show();
        
        console.log('Loading detail items form:', {
            sumber: sumber,
            id_faktur: id_faktur,
            id_pengiriman: id_pengiriman,
            id_retur: id_retur
        });
        
        $.ajax({
            url: "<?= site_url('ReturPenjualan/form_detail_item') ?>",
            type: "POST",
            data: {
                sumber: sumber,
                id_faktur: id_faktur,
                id_pengiriman: id_pengiriman,
                id_retur: id_retur
            },
            dataType: "HTML",
            success: function(response) {
                console.log('Detail items response received');
                $('#detail_items_container').html(response);
            },
            error: function(xhr, status, error) {
                console.error('Error loading detail items:', status, error);
                console.log('XHR Response:', xhr.responseText);
                $('#detail_items_container').html('<div class="text-center text-danger"><i class="fa fa-exclamation-triangle"></i> Gagal memuat form detail: ' + error + '</div>');
            }
        });
    }
</script>