<?php
defined('BASEPATH') or exit('No direct script access allowed');

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * Controller <PERSON><PERSON>an <PERSON>alan
 * Mengatur berbagai jenis laporan penjualan
 */
class Laporan_penjualan extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_laporan_penjualan', 'Mod_dashboard'));
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $data['title'] = 'Laporan Penjualan';
            $data['pelanggan_list'] = $this->Mod_laporan_penjualan->get_pelanggan_list();
            $data['barang_list'] = $this->Mod_laporan_penjualan->get_barang_list();
            $data['status_list'] = $this->Mod_laporan_penjualan->get_status_list();
            $data['summary'] = $this->Mod_laporan_penjualan->get_summary_penjualan();
            
            $this->template->load('layoutbackend', 'laporan_penjualan/laporan_penjualan', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    /**
     * Generate laporan berdasarkan jenis
     */
    public function generate_laporan()
    {
        $jenis_laporan = $this->input->post('jenis_laporan');
        $id_pelanggan = $this->input->post('id_pelanggan');
        $id_barang = $this->input->post('id_barang');
        $status = $this->input->post('status');
        $tanggal_dari = $this->input->post('tanggal_dari');
        $tanggal_sampai = $this->input->post('tanggal_sampai');

        // Debug: Log input parameters
        log_message('debug', 'Laporan Penjualan - Jenis: ' . $jenis_laporan);
        log_message('debug', 'Laporan Penjualan - ID Pelanggan: ' . $id_pelanggan);
        log_message('debug', 'Laporan Penjualan - ID Barang: ' . $id_barang);

        $data['jenis_laporan'] = $jenis_laporan;
        $data['filter'] = array(
            'id_pelanggan' => $id_pelanggan,
            'id_barang' => $id_barang,
            'status' => $status,
            'tanggal_dari' => $tanggal_dari,
            'tanggal_sampai' => $tanggal_sampai
        );

        // Prepare filter info for display
        $data['info_pelanggan'] = '';
        $data['info_barang'] = '';

        if (!empty($id_pelanggan)) {
            $pelanggan = $this->db->get_where('pelanggan', array('id' => $id_pelanggan))->row();
            if ($pelanggan) {
                $data['info_pelanggan'] = $pelanggan->kode . ' - ' . $pelanggan->nama;
            }
        }

        if (!empty($id_barang)) {
            $barang = $this->db->get_where('barang', array('id' => $id_barang))->row();
            if ($barang) {
                $data['info_barang'] = $barang->kode_barang . ' - ' . $barang->nama_barang;
            }
        }

        switch ($jenis_laporan) {
            case 'penjualan_periode':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_penjualan_periode($id_pelanggan, $status, $tanggal_dari, $tanggal_sampai);
                log_message('debug', 'Laporan Penjualan - Query: ' . $this->db->last_query());
                log_message('debug', 'Laporan Penjualan - Result count: ' . ($data['laporan'] ? $data['laporan']->num_rows() : 0));
                break;
            case 'detail_penjualan':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_detail_penjualan($id_pelanggan, $id_barang, $tanggal_dari, $tanggal_sampai);
                log_message('debug', 'Laporan Penjualan - Query: ' . $this->db->last_query());
                log_message('debug', 'Laporan Penjualan - Result count: ' . ($data['laporan'] ? $data['laporan']->num_rows() : 0));
                break;
            case 'penjualan_pelanggan':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_penjualan_pelanggan($id_pelanggan, $tanggal_dari, $tanggal_sampai);
                log_message('debug', 'Laporan Penjualan - Query: ' . $this->db->last_query());
                log_message('debug', 'Laporan Penjualan - Result count: ' . ($data['laporan'] ? $data['laporan']->num_rows() : 0));
                break;
            case 'penjualan_barang':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_penjualan_barang($id_barang, $id_pelanggan, $tanggal_dari, $tanggal_sampai);
                log_message('debug', 'Laporan Penjualan - Query: ' . $this->db->last_query());
                log_message('debug', 'Laporan Penjualan - Result count: ' . ($data['laporan'] ? $data['laporan']->num_rows() : 0));
                break;
            case 'outstanding_invoice':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_outstanding_invoice($id_pelanggan);
                log_message('debug', 'Laporan Penjualan - Query: ' . $this->db->last_query());
                log_message('debug', 'Laporan Penjualan - Result count: ' . ($data['laporan'] ? $data['laporan']->num_rows() : 0));
                break;
            default:
                $data['laporan'] = null;
                break;
        }

        $this->load->view('laporan_penjualan/view_laporan', $data);
    }

    /**
     * Export laporan ke Excel
     */
    public function export_excel()
    {
        $jenis_laporan = $this->input->post('jenis_laporan');
        $id_pelanggan = $this->input->post('id_pelanggan');
        $id_barang = $this->input->post('id_barang');
        $status = $this->input->post('status');
        $tanggal_dari = $this->input->post('tanggal_dari');
        $tanggal_sampai = $this->input->post('tanggal_sampai');

        // Get data berdasarkan jenis laporan
        switch ($jenis_laporan) {
            case 'penjualan_periode':
                $data = $this->Mod_laporan_penjualan->get_laporan_penjualan_periode($id_pelanggan, $status, $tanggal_dari, $tanggal_sampai)->result();
                $filename = 'Laporan_Penjualan_Periode_' . date('Y-m-d_H-i-s');
                break;
            case 'detail_penjualan':
                $data = $this->Mod_laporan_penjualan->get_laporan_detail_penjualan($id_pelanggan, $id_barang, $tanggal_dari, $tanggal_sampai)->result();
                $filename = 'Laporan_Detail_Penjualan_' . date('Y-m-d_H-i-s');
                break;
            case 'penjualan_pelanggan':
                $data = $this->Mod_laporan_penjualan->get_laporan_penjualan_pelanggan($id_pelanggan, $tanggal_dari, $tanggal_sampai)->result();
                $filename = 'Laporan_Penjualan_Per_Pelanggan_' . date('Y-m-d_H-i-s');
                break;
            case 'penjualan_barang':
                $data = $this->Mod_laporan_penjualan->get_laporan_penjualan_barang($id_barang, $id_pelanggan, $tanggal_dari, $tanggal_sampai)->result();
                $filename = 'Laporan_Penjualan_Per_Barang_' . date('Y-m-d_H-i-s');
                break;
            case 'outstanding_invoice':
                $data = $this->Mod_laporan_penjualan->get_laporan_outstanding_invoice($id_pelanggan)->result();
                $filename = 'Laporan_Outstanding_Invoice_' . date('Y-m-d_H-i-s');
                break;
            default:
                show_error('Jenis laporan tidak valid');
                return;
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set header berdasarkan jenis laporan
        $this->_set_excel_header($sheet, $jenis_laporan, $tanggal_dari, $tanggal_sampai);
        
        // Set data berdasarkan jenis laporan
        $this->_set_excel_data($sheet, $jenis_laporan, $data);

        // Set style
        $this->_set_excel_style($sheet, $jenis_laporan, count($data));

        // Download file
        $writer = new Xlsx($spreadsheet);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
    }

    /**
     * Set header Excel berdasarkan jenis laporan
     */
    private function _set_excel_header($sheet, $jenis_laporan, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $periode = '';
        if ($tanggal_dari && $tanggal_sampai) {
            $periode = ' Periode: ' . date('d/m/Y', strtotime($tanggal_dari)) . ' - ' . date('d/m/Y', strtotime($tanggal_sampai));
        }

        switch ($jenis_laporan) {
            case 'penjualan_periode':
                $sheet->mergeCells('A1:L1');
                $sheet->setCellValue('A1', 'LAPORAN PENJUALAN PERIODE' . $periode);
                $sheet->setCellValue('A3', 'No');
                $sheet->setCellValue('B3', 'Nomor Faktur');
                $sheet->setCellValue('C3', 'Tanggal');
                $sheet->setCellValue('D3', 'Pelanggan');
                $sheet->setCellValue('E3', 'Status');
                $sheet->setCellValue('F3', 'Status Pembayaran');
                $sheet->setCellValue('G3', 'Total Item');
                $sheet->setCellValue('H3', 'Total Qty');
                $sheet->setCellValue('I3', 'Subtotal');
                $sheet->setCellValue('J3', 'Diskon');
                $sheet->setCellValue('K3', 'Pajak');
                $sheet->setCellValue('L3', 'Total Faktur');
                break;

            case 'detail_penjualan':
                $sheet->mergeCells('A1:N1');
                $sheet->setCellValue('A1', 'LAPORAN DETAIL PENJUALAN' . $periode);
                $sheet->setCellValue('A3', 'No');
                $sheet->setCellValue('B3', 'Nomor Faktur');
                $sheet->setCellValue('C3', 'Tanggal');
                $sheet->setCellValue('D3', 'Pelanggan');
                $sheet->setCellValue('E3', 'Kode Barang');
                $sheet->setCellValue('F3', 'Nama Barang');
                $sheet->setCellValue('G3', 'Merk');
                $sheet->setCellValue('H3', 'Satuan');
                $sheet->setCellValue('I3', 'Qty');
                $sheet->setCellValue('J3', 'Harga Satuan');
                $sheet->setCellValue('K3', 'Diskon');
                $sheet->setCellValue('L3', 'Subtotal');
                $sheet->setCellValue('M3', 'Pajak');
                $sheet->setCellValue('N3', 'Total');
                break;

            case 'penjualan_pelanggan':
                $sheet->mergeCells('A1:I1');
                $sheet->setCellValue('A1', 'LAPORAN PENJUALAN PER PELANGGAN' . $periode);
                $sheet->setCellValue('A3', 'No');
                $sheet->setCellValue('B3', 'Kode Pelanggan');
                $sheet->setCellValue('C3', 'Nama Pelanggan');
                $sheet->setCellValue('D3', 'Total Transaksi');
                $sheet->setCellValue('E3', 'Total Item');
                $sheet->setCellValue('F3', 'Total Qty');
                $sheet->setCellValue('G3', 'Total Penjualan');
                $sheet->setCellValue('H3', 'Total Dibayar');
                $sheet->setCellValue('I3', 'Sisa Pembayaran');
                break;

            case 'penjualan_barang':
                $sheet->mergeCells('A1:K1');
                $sheet->setCellValue('A1', 'LAPORAN PENJUALAN PER BARANG' . $periode);
                $sheet->setCellValue('A3', 'No');
                $sheet->setCellValue('B3', 'Kode Barang');
                $sheet->setCellValue('C3', 'Nama Barang');
                $sheet->setCellValue('D3', 'Merk');
                $sheet->setCellValue('E3', 'Satuan');
                $sheet->setCellValue('F3', 'Total Qty');
                $sheet->setCellValue('G3', 'Harga Rata-rata');
                $sheet->setCellValue('H3', 'Harga Terendah');
                $sheet->setCellValue('I3', 'Harga Tertinggi');
                $sheet->setCellValue('J3', 'Total Penjualan');
                $sheet->setCellValue('K3', 'Jumlah Pelanggan');
                break;

            case 'outstanding_invoice':
                $sheet->mergeCells('A1:J1');
                $sheet->setCellValue('A1', 'LAPORAN OUTSTANDING INVOICE');
                $sheet->setCellValue('A3', 'No');
                $sheet->setCellValue('B3', 'Nomor Faktur');
                $sheet->setCellValue('C3', 'Tanggal Faktur');
                $sheet->setCellValue('D3', 'Pelanggan');
                $sheet->setCellValue('E3', 'Status');
                $sheet->setCellValue('F3', 'Total Faktur');
                $sheet->setCellValue('G3', 'Jatuh Tempo');
                $sheet->setCellValue('H3', 'Umur Invoice (Hari)');
                $sheet->setCellValue('I3', 'Status Pembayaran');
                $sheet->setCellValue('J3', 'Sisa Pembayaran');
                break;
        }
    }

    /**
     * Set data Excel berdasarkan jenis laporan
     */
    private function _set_excel_data($sheet, $jenis_laporan, $data)
    {
        $row = 4; // Mulai dari baris 4
        $no = 1;

        foreach ($data as $item) {
            switch ($jenis_laporan) {
                case 'penjualan_periode':
                    $sheet->setCellValue('A' . $row, $no);
                    $sheet->setCellValue('B' . $row, $item->nomor_faktur);
                    $sheet->setCellValue('C' . $row, date('d/m/Y', strtotime($item->tanggal_faktur)));
                    $sheet->setCellValue('D' . $row, $item->nama_pelanggan);
                    $sheet->setCellValue('E' . $row, ucfirst($item->status));
                    $sheet->setCellValue('F' . $row, ucfirst($item->status_pembayaran));
                    $sheet->setCellValue('G' . $row, $item->total_item);
                    $sheet->setCellValue('H' . $row, $item->total_qty);
                    $sheet->setCellValue('I' . $row, $item->subtotal);
                    $sheet->setCellValue('J' . $row, $item->diskon);
                    $sheet->setCellValue('K' . $row, $item->pajak);
                    $sheet->setCellValue('L' . $row, $item->total_faktur);
                    break;

                case 'detail_penjualan':
                    $sheet->setCellValue('A' . $row, $no);
                    $sheet->setCellValue('B' . $row, $item->nomor_faktur);
                    $sheet->setCellValue('C' . $row, date('d/m/Y', strtotime($item->tanggal_faktur)));
                    $sheet->setCellValue('D' . $row, $item->nama_pelanggan);
                    $sheet->setCellValue('E' . $row, $item->kode_barang);
                    $sheet->setCellValue('F' . $row, $item->nama_barang);
                    $sheet->setCellValue('G' . $row, $item->merk);
                    $sheet->setCellValue('H' . $row, $item->nama_satuan);
                    $sheet->setCellValue('I' . $row, $item->qty);
                    $sheet->setCellValue('J' . $row, $item->harga_satuan);
                    $sheet->setCellValue('K' . $row, $item->diskon_nilai);
                    $sheet->setCellValue('L' . $row, $item->subtotal);
                    $sheet->setCellValue('M' . $row, $item->pajak_nilai);
                    $sheet->setCellValue('N' . $row, $item->total);
                    break;

                case 'penjualan_pelanggan':
                    $sheet->setCellValue('A' . $row, $no);
                    $sheet->setCellValue('B' . $row, $item->kode_pelanggan);
                    $sheet->setCellValue('C' . $row, $item->nama_pelanggan);
                    $sheet->setCellValue('D' . $row, $item->total_transaksi);
                    $sheet->setCellValue('E' . $row, $item->total_item);
                    $sheet->setCellValue('F' . $row, $item->total_qty);
                    $sheet->setCellValue('G' . $row, $item->total_penjualan);
                    $sheet->setCellValue('H' . $row, $item->total_dibayar);
                    $sheet->setCellValue('I' . $row, $item->sisa_pembayaran);
                    break;

                case 'penjualan_barang':
                    $sheet->setCellValue('A' . $row, $no);
                    $sheet->setCellValue('B' . $row, $item->kode_barang);
                    $sheet->setCellValue('C' . $row, $item->nama_barang);
                    $sheet->setCellValue('D' . $row, $item->merk);
                    $sheet->setCellValue('E' . $row, $item->nama_satuan);
                    $sheet->setCellValue('F' . $row, $item->total_qty);
                    $sheet->setCellValue('G' . $row, $item->harga_rata_rata);
                    $sheet->setCellValue('H' . $row, $item->harga_terendah);
                    $sheet->setCellValue('I' . $row, $item->harga_tertinggi);
                    $sheet->setCellValue('J' . $row, $item->total_penjualan);
                    $sheet->setCellValue('K' . $row, $item->jumlah_pelanggan);
                    break;

                case 'outstanding_invoice':
                    $umur_invoice = floor((strtotime(date('Y-m-d')) - strtotime($item->tanggal_faktur)) / (60 * 60 * 24));
                    $sheet->setCellValue('A' . $row, $no);
                    $sheet->setCellValue('B' . $row, $item->nomor_faktur);
                    $sheet->setCellValue('C' . $row, date('d/m/Y', strtotime($item->tanggal_faktur)));
                    $sheet->setCellValue('D' . $row, $item->nama_pelanggan);
                    $sheet->setCellValue('E' . $row, ucfirst($item->status));
                    $sheet->setCellValue('F' . $row, $item->total_faktur);
                    $sheet->setCellValue('G' . $row, !empty($item->jatuh_tempo) ? date('d/m/Y', strtotime($item->jatuh_tempo)) : '-');
                    $sheet->setCellValue('H' . $row, $umur_invoice);
                    $sheet->setCellValue('I' . $row, ucfirst($item->status_pembayaran));
                    $sheet->setCellValue('J' . $row, $item->sisa_pembayaran);
                    break;
            }
            $row++;
            $no++;
        }
    }

    /**
     * Set style Excel
     */
    private function _set_excel_style($sheet, $jenis_laporan, $data_count)
    {
        // Style untuk header
        $headerStyle = [
            'font' => ['bold' => true, 'size' => 14],
            'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]
        ];

        $columnHeaderStyle = [
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E2E2E2']
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                ]
            ]
        ];

        // Apply header style
        $sheet->getStyle('A1')->applyFromArray($headerStyle);

        // Determine column range based on report type
        $columnRange = '';
        switch ($jenis_laporan) {
            case 'penjualan_periode':
                $columnRange = 'A3:L3';
                break;
            case 'detail_penjualan':
                $columnRange = 'A3:N3';
                break;
            case 'penjualan_pelanggan':
                $columnRange = 'A3:I3';
                break;
            case 'penjualan_barang':
                $columnRange = 'A3:K3';
                break;
            case 'outstanding_invoice':
                $columnRange = 'A3:J3';
                break;
        }

        // Apply column header style
        $sheet->getStyle($columnRange)->applyFromArray($columnHeaderStyle);

        // Auto size columns
        foreach (range('A', $sheet->getHighestColumn()) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Add borders to data
        if ($data_count > 0) {
            $lastColumn = $sheet->getHighestColumn();
            $lastRow = 3 + $data_count;
            $dataRange = 'A4:' . $lastColumn . $lastRow;

            $sheet->getStyle($dataRange)->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                    ]
                ]
            ]);
        }
    }

    /**
     * Cetak laporan
     */
    public function cetak()
    {
        $jenis_laporan = $this->input->post('jenis_laporan');
        $id_pelanggan = $this->input->post('id_pelanggan');
        $id_barang = $this->input->post('id_barang');
        $status = $this->input->post('status');
        $tanggal_dari = $this->input->post('tanggal_dari');
        $tanggal_sampai = $this->input->post('tanggal_sampai');

        $data['jenis_laporan'] = $jenis_laporan;
        $data['filter'] = array(
            'id_pelanggan' => $id_pelanggan,
            'id_barang' => $id_barang,
            'status' => $status,
            'tanggal_dari' => $tanggal_dari,
            'tanggal_sampai' => $tanggal_sampai
        );

        // Prepare filter info for display
        $data['info_pelanggan'] = '';
        $data['info_barang'] = '';

        if (!empty($id_pelanggan)) {
            $pelanggan = $this->db->get_where('pelanggan', array('id' => $id_pelanggan))->row();
            if ($pelanggan) {
                $data['info_pelanggan'] = $pelanggan->kode . ' - ' . $pelanggan->nama;
            }
        }

        if (!empty($id_barang)) {
            $barang = $this->db->get_where('barang', array('id' => $id_barang))->row();
            if ($barang) {
                $data['info_barang'] = $barang->kode_barang . ' - ' . $barang->nama_barang;
            }
        }

        // Get data berdasarkan jenis laporan
        switch ($jenis_laporan) {
            case 'penjualan_periode':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_penjualan_periode($id_pelanggan, $status, $tanggal_dari, $tanggal_sampai);
                break;
            case 'detail_penjualan':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_detail_penjualan($id_pelanggan, $id_barang, $tanggal_dari, $tanggal_sampai);
                break;
            case 'penjualan_pelanggan':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_penjualan_pelanggan($id_pelanggan, $tanggal_dari, $tanggal_sampai);
                break;
            case 'penjualan_barang':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_penjualan_barang($id_barang, $id_pelanggan, $tanggal_dari, $tanggal_sampai);
                break;
            case 'outstanding_invoice':
                $data['laporan'] = $this->Mod_laporan_penjualan->get_laporan_outstanding_invoice($id_pelanggan);
                break;
            default:
                $data['laporan'] = null;
                break;
        }

        $this->load->view('laporan_penjualan/cetak_laporan', $data);
    }
}