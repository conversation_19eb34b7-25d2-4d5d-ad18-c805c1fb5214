<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Settlement Retur (Retur Penjualan dan Pembelian)</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            Settlement Retur digunakan untuk memastikan bahwa semua retur telah diproses dengan benar.
                            Proses ini akan membandingkan data retur dengan nota kredit atau penggantian barang untuk mengidentifikasi perbedaan atau kesalahan.
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card card-outline card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Filter Data</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="form-filter" class="form-horizontal">
                                    <div class="form-group row">
                                        <label for="tanggal_awal" class="col-sm-2 col-form-label">Tanggal Retur</label>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?= $tanggal_awal ?>">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">s/d</span>
                                                </div>
                                                <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?= $tanggal_akhir ?>">
                                            </div>
                                        </div>
                                        <label for="jenis" class="col-sm-2 col-form-label">Jenis Retur</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="jenis" name="jenis">
                                                <option value="">Semua Jenis</option>
                                                <option value="penjualan">Retur Penjualan</option>
                                                <option value="pembelian">Retur Pembelian</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="status" class="col-sm-2 col-form-label">Status Penggantian</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="status" name="status">
                                                <option value="">Semua Status</option>
                                                <option value="belum_diganti">Belum Diganti</option>
                                                <option value="diganti_barang">Diganti Barang</option>
                                                <option value="diganti_uang">Diganti Uang</option>
                                                <option value="tidak_diganti">Tidak Diganti</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-6">
                                            <button type="button" id="btn-filter" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                                            <button type="button" id="btn-reset" class="btn btn-default"><i class="fas fa-undo"></i> Reset</button>
                                            <button type="button" id="btn-export" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="table-settlement" class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Jenis</th>
                                        <th>Nomor Retur</th>
                                        <th>Tanggal Retur</th>
                                        <th>Partner</th>
                                        <th>Nomor Referensi</th>
                                        <th>Tanggal Referensi</th>
                                        <th>Total Item</th>
                                        <th>Total Qty</th>
                                        <th>Total Nilai</th>
                                        <th>Status Penggantian</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#table-settlement').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?= site_url('settlement/retur_data') ?>",
            "type": "POST",
            "data": function(data) {
                data.tanggal_awal = $('#tanggal_awal').val();
                data.tanggal_akhir = $('#tanggal_akhir').val();
                data.jenis = $('#jenis').val();
                data.status = $('#status').val();
            },
            "dataSrc": "data"
        },
        "columns": [
            { "data": null, "orderable": false, "render": function(data, type, row, meta) {
                return meta.row + 1;
            }},
            { "data": "jenis", "render": function(data, type, row) {
                if (data == 'penjualan') {
                    return '<span class="badge badge-info">Retur Penjualan</span>';
                } else if (data == 'pembelian') {
                    return '<span class="badge badge-success">Retur Pembelian</span>';
                } else {
                    return data;
                }
            }},
            { "data": "nomor_retur" },
            { "data": "tanggal_retur" },
            { "data": "nama_partner" },
            { "data": null, "render": function(data, type, row) {
                if (row.jenis == 'penjualan') {
                    return row.nomor_faktur;
                } else {
                    return row.nomor_pembelian;
                }
            }},
            { "data": null, "render": function(data, type, row) {
                if (row.jenis == 'penjualan') {
                    return row.tanggal_faktur;
                } else {
                    return row.tanggal_pembelian;
                }
            }},
            { "data": "total_item" },
            { "data": "total_qty" },
            { "data": "total_nilai_retur", "render": function(data, type, row) {
                return formatRupiah(data);
            }},
            { "data": "status_penggantian_text" },
            { "data": null, "orderable": false, "render": function(data, type, row) {
                var html = '<div class="btn-group">';
                html += '<a href="<?= site_url('settlement/retur_detail/') ?>' + row.jenis + '/' + row.id + '" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>';
                html += '</div>';
                return html;
            }}
        ],
        "order": [[3, 'desc']],
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    $('#btn-filter').click(function() {
        table.ajax.reload();
    });

    $('#btn-reset').click(function() {
        $('#form-filter')[0].reset();
        table.ajax.reload();
    });

    $('#btn-export').click(function() {
        var tanggal_awal = $('#tanggal_awal').val();
        var tanggal_akhir = $('#tanggal_akhir').val();
        var jenis = $('#jenis').val();
        var status = $('#status').val();
        
        window.location.href = '<?= site_url('settlement/export/retur') ?>?tanggal_awal=' + tanggal_awal + '&tanggal_akhir=' + tanggal_akhir + '&jenis=' + jenis + '&status=' + status;
    });

    function formatRupiah(angka) {
        // Periksa apakah angka adalah null, undefined, atau bukan angka
        if (angka === null || angka === undefined || angka === '' || isNaN(angka)) {
            return 'Rp 0';
        }
        
        // Konversi ke number jika berupa string
        var nilai = parseFloat(angka);
        if (isNaN(nilai)) {
            return 'Rp 0';
        }
        
        var number_string = nilai.toString(),
            split = number_string.split('.'),
            sisa = split[0].length % 3,
            rupiah = split[0].substr(0, sisa),
            ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }
});
</script>