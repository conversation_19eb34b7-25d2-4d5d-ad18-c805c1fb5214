-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               10.4.27-MariaDB - mariadb.org binary distribution
-- Server OS:                    Win64
-- HeidiSQL Version:             12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Dumping structure for table toko_elektronik.aplikasi
CREATE TABLE IF NOT EXISTS `aplikasi` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `nama_owner` varchar(100) DEFAULT NULL,
  `alamat` mediumtext DEFAULT NULL,
  `tlp` varchar(50) DEFAULT NULL,
  `brand` varchar(10) DEFAULT NULL,
  `title` varchar(20) DEFAULT NULL,
  `nama_aplikasi` varchar(100) DEFAULT NULL,
  `logo` varchar(100) DEFAULT NULL,
  `copy_right` varchar(50) DEFAULT NULL,
  `versi` varchar(20) DEFAULT NULL,
  `tahun` year(4) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `nama_pengirim` varchar(100) DEFAULT NULL,
  `password` varchar(100) DEFAULT NULL,
  `npwp` varchar(20) DEFAULT NULL COMMENT 'NPWP Perusahaan',
  `no_telepon` varchar(20) DEFAULT NULL COMMENT 'Telepon Perusahaan',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.aplikasi: ~0 rows (approximately)
INSERT INTO `aplikasi` (`id`, `nama_owner`, `alamat`, `tlp`, `brand`, `title`, `nama_aplikasi`, `logo`, `copy_right`, `versi`, `tahun`, `email`, `nama_pengirim`, `password`, `npwp`, `no_telepon`) VALUES
	(1, 'PT. FOE', 'Tangerang', '************', NULL, 'Toko Elektronik', 'Toko Elektronik', 'Logo.png', 'Copyright ©', '1.0.0.0', '2025', '<EMAIL>', 'Aryo Coding', 'pfpinffqxutdjexq', '01.234.567.8-901.000', '021-********');

-- Dumping structure for table toko_elektronik.bank
CREATE TABLE IF NOT EXISTS `bank` (
  `id_bank` int(11) NOT NULL AUTO_INCREMENT,
  `kode_bank` varchar(10) NOT NULL,
  `nama_bank` varchar(100) NOT NULL,
  `nomor_rekening` varchar(50) NOT NULL,
  `atas_nama` varchar(100) NOT NULL,
  `saldo_awal` decimal(15,2) DEFAULT 0.00,
  `saldo_akhir` decimal(15,2) DEFAULT 0.00,
  `status` enum('AKTIF','NONAKTIF') DEFAULT 'AKTIF',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id_bank`),
  UNIQUE KEY `kode_bank` (`kode_bank`),
  UNIQUE KEY `nomor_rekening` (`nomor_rekening`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.bank: ~3 rows (approximately)
INSERT INTO `bank` (`id_bank`, `kode_bank`, `nama_bank`, `nomor_rekening`, `atas_nama`, `saldo_awal`, `saldo_akhir`, `status`, `created_at`, `updated_at`) VALUES
	(1, 'BCA001', 'Bank Central Asia', '**********', 'Toko Elektronik', ********.00, 0.00, 'AKTIF', '2025-06-25 23:02:03', '2025-06-25 23:02:03'),
	(2, 'BNI001', 'Bank Negara Indonesia', '**********', 'Toko Elektronik', ********.00, 0.00, 'AKTIF', '2025-06-25 23:02:03', '2025-06-25 23:02:03'),
	(3, 'MANDIRI001', 'Bank Mandiri', '**********', 'Toko Elektronik', ********.00, 0.00, 'AKTIF', '2025-06-25 23:02:03', '2025-06-25 23:02:03');

-- Dumping structure for table toko_elektronik.bank_statement
CREATE TABLE IF NOT EXISTS `bank_statement` (
  `id_statement` int(11) NOT NULL AUTO_INCREMENT,
  `id_bank` int(11) NOT NULL,
  `tanggal_transaksi` date NOT NULL,
  `nomor_referensi` varchar(50) NOT NULL,
  `deskripsi` text NOT NULL,
  `bank_masuk` decimal(15,2) DEFAULT 0.00,
  `bank_keluar` decimal(15,2) DEFAULT 0.00,
  `saldo_berjalan` decimal(15,2) DEFAULT 0.00,
  `kode_transaksi` varchar(20) DEFAULT NULL,
  `status_reconcile` enum('UNMATCHED','MATCHED','MANUAL') DEFAULT 'UNMATCHED',
  `id_transaksi_kas` int(11) DEFAULT NULL,
  `imported_at` timestamp NULL DEFAULT current_timestamp(),
  `reconciled_at` timestamp NULL DEFAULT NULL,
  `reconciled_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id_statement`),
  KEY `idx_bank_tanggal` (`id_bank`,`tanggal_transaksi`),
  KEY `idx_referensi` (`nomor_referensi`),
  KEY `idx_status` (`status_reconcile`),
  KEY `id_transaksi_kas` (`id_transaksi_kas`),
  KEY `idx_bank_statement_search` (`tanggal_transaksi`,`status_reconcile`,`id_bank`),
  CONSTRAINT `bank_statement_ibfk_1` FOREIGN KEY (`id_bank`) REFERENCES `bank` (`id_bank`) ON DELETE CASCADE,
  CONSTRAINT `bank_statement_ibfk_2` FOREIGN KEY (`id_transaksi_kas`) REFERENCES `transaksi_kas` (`id_transaksi`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.bank_statement: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.barang
CREATE TABLE IF NOT EXISTS `barang` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_barang` varchar(50) NOT NULL,
  `nama_barang` varchar(255) NOT NULL,
  `merk` varchar(100) DEFAULT NULL,
  `tipe` varchar(100) DEFAULT NULL,
  `spesifikasi` mediumtext DEFAULT NULL,
  `satuan_id` int(11) DEFAULT NULL,
  `jenis_pajak_id` int(11) DEFAULT NULL,
  `stok_minimum` int(11) DEFAULT 0,
  `harga_beli` decimal(18,2) NOT NULL,
  `harga_jual` decimal(18,2) DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_barang` (`kode_barang`),
  KEY `fk_barang_satuan` (`satuan_id`),
  KEY `fk_barang_jenis_pajak` (`jenis_pajak_id`),
  KEY `idx_barang_nama` (`nama_barang`),
  CONSTRAINT `fk_barang_jenis_pajak` FOREIGN KEY (`jenis_pajak_id`) REFERENCES `jenis_pajak` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_barang_satuan` FOREIGN KEY (`satuan_id`) REFERENCES `satuan` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.barang: ~5 rows (approximately)
INSERT INTO `barang` (`id`, `kode_barang`, `nama_barang`, `merk`, `tipe`, `spesifikasi`, `satuan_id`, `jenis_pajak_id`, `stok_minimum`, `harga_beli`, `harga_jual`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'BRG0001', 'Smartphone Samsung Galaxy A54 5G', 'Samsung', 'Galaxy A54 5G', 'RAM 8GB, Storage 256GB, Camera 50MP, Battery 5000mAh, Display 6.4" Super AMOLED', 1, 1, 5, 4500000.00, 5500000.00, 1, '2025-05-29 07:29:25', '2025-06-27 02:15:32'),
	(2, 'BRG0002', 'Laptop ASUS VivoBook 14', 'ASUS', 'VivoBook 14 X1404VA', 'Intel Core i5-1335U, RAM 8GB DDR4, SSD 512GB, Intel Iris Xe Graphics, 14" FHD IPS', 1, 1, 3, 7800000.00, 8900000.00, 1, '2025-05-29 07:29:25', '2025-06-27 02:13:01'),
	(3, 'BRG0003', 'Smart TV LG 43" 4K UHD', 'LG', '43UP7750PTB', '43 inch 4K UHD, WebOS Smart TV, HDR10 Pro, AI ThinQ, Magic Remote', 1, 1, 2, 4200000.00, 4800000.00, 1, '2025-05-29 07:29:25', '2025-06-27 02:13:01'),
	(4, 'BRG0004', 'Headphone Sony WH-1000XM4', 'Sony', 'WH-1000XM4', 'Wireless Noise Canceling, 30 Hours Battery, Quick Charge, Touch Control', 1, 1, 10, 3200000.00, 3800000.00, 1, '2025-05-29 07:29:25', '2025-06-27 02:13:01'),
	(5, 'BRG0005', 'Kamera Canon EOS M50 Mark II', 'Canon', 'EOS M50 Mark II', '24.1MP APS-C CMOS, 4K Video, Dual Pixel CMOS AF, WiFi, Bluetooth', 1, 1, 2, 8500000.00, 9800000.00, 1, '2025-05-29 07:29:25', '2025-06-27 02:13:01');

-- Dumping structure for table toko_elektronik.barang_keluar
CREATE TABLE IF NOT EXISTS `barang_keluar` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_pengeluaran` varchar(50) NOT NULL,
  `tanggal` date NOT NULL,
  `id_pelanggan` int(11) DEFAULT NULL,
  `jenis` enum('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL,
  `ref_nomor` varchar(50) DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `status` enum('draft','final') DEFAULT 'draft',
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) DEFAULT NULL,
  `finalized_by` int(11) DEFAULT NULL,
  `finalized_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pengeluaran` (`nomor_pengeluaran`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `idx_barang_keluar_tanggal` (`tanggal`),
  KEY `idx_barang_keluar_jenis` (`jenis`),
  KEY `idx_barang_keluar_status` (`status`),
  KEY `idx_barang_keluar_nomor` (`nomor_pengeluaran`),
  KEY `idx_barang_keluar_jenis_ref` (`jenis`,`ref_nomor`),
  CONSTRAINT `barang_keluar_ibfk_1` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.barang_keluar: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.barang_keluar_detail
CREATE TABLE IF NOT EXISTS `barang_keluar_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `id_barang_keluar` bigint(20) unsigned NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_keluar` decimal(12,2) NOT NULL DEFAULT 0.00,
  `id_satuan` int(11) DEFAULT NULL,
  `harga_satuan` decimal(15,2) DEFAULT 0.00,
  `total_harga` decimal(15,2) GENERATED ALWAYS AS (`qty_keluar` * `harga_satuan`) STORED,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_barang_keluar` (`id_barang_keluar`),
  KEY `id_satuan` (`id_satuan`),
  KEY `idx_barang_keluar_detail_barang` (`id_barang`),
  KEY `idx_barang_keluar_detail_gudang` (`id_gudang`),
  CONSTRAINT `barang_keluar_detail_ibfk_1` FOREIGN KEY (`id_barang_keluar`) REFERENCES `barang_keluar` (`id`) ON DELETE CASCADE,
  CONSTRAINT `barang_keluar_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`),
  CONSTRAINT `barang_keluar_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`),
  CONSTRAINT `barang_keluar_detail_ibfk_4` FOREIGN KEY (`id_satuan`) REFERENCES `satuan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.barang_keluar_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.barang_masuk
CREATE TABLE IF NOT EXISTS `barang_masuk` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_penerimaan` varchar(50) NOT NULL,
  `tanggal` date NOT NULL,
  `id_supplier` int(11) DEFAULT NULL,
  `jenis` enum('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL,
  `ref_nomor` varchar(50) DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `status` enum('draft','final') DEFAULT 'draft',
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) DEFAULT NULL,
  `finalized_by` int(11) DEFAULT NULL,
  `finalized_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_penerimaan` (`nomor_penerimaan`),
  KEY `created_by` (`created_by`),
  KEY `finalized_by` (`finalized_by`),
  KEY `idx_barang_masuk_tanggal` (`tanggal`),
  KEY `idx_barang_masuk_supplier` (`id_supplier`),
  KEY `idx_barang_masuk_jenis` (`jenis`),
  KEY `idx_barang_masuk_status` (`status`),
  KEY `idx_barang_masuk_nomor` (`nomor_penerimaan`),
  KEY `idx_barang_masuk_jenis_ref` (`jenis`,`ref_nomor`),
  CONSTRAINT `barang_masuk_ibfk_1` FOREIGN KEY (`id_supplier`) REFERENCES `supplier` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_ibfk_3` FOREIGN KEY (`finalized_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.barang_masuk: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.barang_masuk_detail
CREATE TABLE IF NOT EXISTS `barang_masuk_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `id_barang_masuk` bigint(20) unsigned NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_diterima` decimal(12,2) NOT NULL DEFAULT 0.00,
  `id_satuan` int(11) DEFAULT NULL,
  `harga_satuan` decimal(15,2) DEFAULT 0.00,
  `total_harga` decimal(15,2) GENERATED ALWAYS AS (`qty_diterima` * `harga_satuan`) STORED,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_barang_masuk_barang_gudang` (`id_barang_masuk`,`id_barang`,`id_gudang`),
  KEY `id_satuan` (`id_satuan`),
  KEY `idx_barang_masuk_detail_barang_masuk` (`id_barang_masuk`),
  KEY `idx_barang_masuk_detail_barang` (`id_barang`),
  KEY `idx_barang_masuk_detail_gudang` (`id_gudang`),
  CONSTRAINT `barang_masuk_detail_ibfk_1` FOREIGN KEY (`id_barang_masuk`) REFERENCES `barang_masuk` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_detail_ibfk_4` FOREIGN KEY (`id_satuan`) REFERENCES `satuan` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.barang_masuk_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.coa_akun
CREATE TABLE IF NOT EXISTS `coa_akun` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_akun` varchar(20) NOT NULL,
  `nama_akun` varchar(100) NOT NULL,
  `id_kategori` int(11) NOT NULL,
  `level` int(11) NOT NULL DEFAULT 0,
  `id_parent` int(11) DEFAULT NULL,
  `saldo_normal` enum('Debit','Kredit') NOT NULL,
  `dapat_diinput` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Apakah akun dapat diinput transaksi',
  `saldo_awal` decimal(18,2) DEFAULT 0.00,
  `tanggal_saldo_awal` date DEFAULT NULL,
  `deskripsi` mediumtext DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_akun` (`kode_akun`),
  KEY `id_kategori` (`id_kategori`),
  KEY `id_parent` (`id_parent`),
  KEY `idx_coa_akun_level` (`level`),
  KEY `idx_coa_akun_aktif` (`aktif`),
  CONSTRAINT `fk_coa_akun_kategori` FOREIGN KEY (`id_kategori`) REFERENCES `coa_kategori` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_coa_akun_parent` FOREIGN KEY (`id_parent`) REFERENCES `coa_akun` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.coa_akun: ~94 rows (approximately)
INSERT INTO `coa_akun` (`id`, `kode_akun`, `nama_akun`, `id_kategori`, `level`, `id_parent`, `saldo_normal`, `dapat_diinput`, `saldo_awal`, `tanggal_saldo_awal`, `deskripsi`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, '1-0000', 'ASET', 1, 0, NULL, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(2, '1-1000', 'Aset Lancar', 1, 1, 1, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(3, '1-1100', 'Kas & Bank', 1, 2, 2, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(4, '1-1101', 'Kas', 1, 3, 3, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(5, '1-1102', 'Kas Kecil', 1, 3, 3, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(6, '1-1103', 'Bank BCA', 1, 3, 3, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(7, '1-1104', 'Bank Mandiri', 1, 3, 3, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(8, '1-1105', 'Bank BNI', 1, 3, 3, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(9, '1-1200', 'Piutang Usaha', 1, 2, 2, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(10, '1-1300', 'Cadangan Kerugian Piutang', 1, 2, 2, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(11, '1-1400', 'Piutang Lain-lain', 1, 2, 2, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(12, '1-1500', 'Persediaan', 1, 2, 2, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(13, '1-1600', 'Biaya Dibayar Dimuka', 1, 2, 2, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(14, '1-1601', 'Sewa Dibayar Dimuka', 1, 3, 13, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(15, '1-1602', 'Asuransi Dibayar Dimuka', 1, 3, 13, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(16, '1-1700', 'PPN Masukan', 1, 2, 2, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(17, '1-1800', 'Uang Muka Pembelian', 1, 2, 2, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(18, '1-2000', 'Aset Tetap', 1, 1, 1, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(19, '1-2100', 'Tanah', 1, 2, 18, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(20, '1-2200', 'Bangunan', 1, 2, 18, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(21, '1-2300', 'Akumulasi Penyusutan Bangunan', 1, 2, 18, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(22, '1-2400', 'Kendaraan', 1, 2, 18, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(23, '1-2500', 'Akumulasi Penyusutan Kendaraan', 1, 2, 18, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(24, '1-2600', 'Peralatan Kantor', 1, 2, 18, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(25, '1-2700', 'Akumulasi Penyusutan Peralatan Kantor', 1, 2, 18, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(26, '1-3000', 'Aset Lainnya', 1, 1, 1, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(27, '1-3100', 'Investasi Jangka Panjang', 1, 2, 26, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(28, '1-3200', 'Aset Tak Berwujud', 1, 2, 26, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(29, '1-3300', 'Akumulasi Amortisasi Aset Tak Berwujud', 1, 2, 26, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(30, '2-0000', 'LIABILITAS', 2, 0, NULL, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(31, '2-1000', 'Liabilitas Jangka Pendek', 2, 1, 30, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(32, '2-1100', 'Hutang Usaha', 2, 2, 31, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(33, '2-1200', 'Hutang Bank Jangka Pendek', 2, 2, 31, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(34, '2-1300', 'Hutang Pajak', 2, 2, 31, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(35, '2-1301', 'Hutang PPN', 2, 3, 34, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(36, '2-1302', 'Hutang PPh 21', 2, 3, 34, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(37, '2-1303', 'Hutang PPh 23', 2, 3, 34, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(38, '2-1304', 'Hutang PPh 25', 2, 3, 34, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(39, '2-1305', 'Hutang PPh 29', 2, 3, 34, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(40, '2-1400', 'Biaya Yang Masih Harus Dibayar', 2, 2, 31, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(41, '2-1500', 'Pendapatan Diterima Dimuka', 2, 2, 31, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(42, '2-1600', 'Hutang Lain-lain', 2, 2, 31, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(43, '2-2000', 'Liabilitas Jangka Panjang', 2, 1, 30, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(44, '2-2100', 'Hutang Bank Jangka Panjang', 2, 2, 43, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(45, '2-2200', 'Hutang Leasing', 2, 2, 43, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(46, '2-2300', 'Hutang Obligasi', 2, 2, 43, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(47, '3-0000', 'EKUITAS', 3, 0, NULL, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(48, '3-1000', 'Modal Disetor', 3, 1, 47, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(49, '3-2000', 'Tambahan Modal Disetor', 3, 1, 47, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(50, '3-3000', 'Laba Ditahan', 3, 1, 47, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(51, '3-4000', 'Laba Tahun Berjalan', 3, 1, 47, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(52, '3-5000', 'Prive', 3, 1, 47, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(53, '4-0000', 'PENDAPATAN', 4, 0, NULL, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(54, '4-1000', 'Pendapatan Usaha', 4, 1, 53, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(55, '4-1100', 'Penjualan', 4, 2, 54, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(56, '4-1200', 'Retur Penjualan', 4, 2, 54, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(57, '4-1300', 'Diskon Penjualan', 4, 2, 54, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(58, '4-2000', 'Pendapatan Lain-lain', 4, 1, 53, 'Kredit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(59, '4-2100', 'Pendapatan Bunga', 4, 2, 58, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(60, '4-2200', 'Pendapatan Sewa', 4, 2, 58, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(61, '4-2300', 'Laba Selisih Kurs', 4, 2, 58, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(62, '4-2400', 'Pendapatan Lainnya', 4, 2, 58, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(63, '5-0000', 'BEBAN', 5, 0, NULL, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(64, '5-1000', 'Beban Pokok Penjualan', 5, 1, 63, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(65, '5-1100', 'Pembelian', 5, 2, 64, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(66, '5-1200', 'Retur Pembelian', 5, 2, 64, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(67, '5-1300', 'Diskon Pembelian', 5, 2, 64, 'Kredit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(68, '5-1400', 'Beban Angkut Pembelian', 5, 2, 64, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(69, '5-1500', 'Beban Pokok Penjualan Lainnya', 5, 2, 64, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(70, '5-2000', 'Beban Operasional', 5, 1, 63, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(71, '5-2100', 'Beban Gaji', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(72, '5-2200', 'Beban Tunjangan', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(73, '5-2300', 'Beban Lembur', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(74, '5-2400', 'Beban THR & Bonus', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(75, '5-2500', 'Beban Listrik, Air & Telepon', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(76, '5-2600', 'Beban Internet', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(77, '5-2700', 'Beban Sewa', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(78, '5-2800', 'Beban Penyusutan Bangunan', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(79, '5-2900', 'Beban Penyusutan Kendaraan', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(80, '5-3000', 'Beban Penyusutan Peralatan Kantor', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(81, '5-3100', 'Beban Amortisasi', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(82, '5-3200', 'Beban Perlengkapan Kantor', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(83, '5-3300', 'Beban Pemeliharaan', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(84, '5-3400', 'Beban Asuransi', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(85, '5-3500', 'Beban Perjalanan Dinas', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(86, '5-3600', 'Beban Iklan & Promosi', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(87, '5-3700', 'Beban Representasi & Jamuan', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(88, '5-3800', 'Beban Operasional Lainnya', 5, 2, 70, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(89, '5-4000', 'Beban Lain-lain', 5, 1, 63, 'Debit', 0, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(90, '5-4100', 'Beban Bunga', 5, 2, 89, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(91, '5-4200', 'Beban Administrasi Bank', 5, 2, 89, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(92, '5-4300', 'Rugi Selisih Kurs', 5, 2, 89, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(93, '5-4400', 'Beban Pajak', 5, 2, 89, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(94, '5-4500', 'Beban Lainnya', 5, 2, 89, 'Debit', 1, 0.00, NULL, NULL, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43');

-- Dumping structure for table toko_elektronik.coa_kategori
CREATE TABLE IF NOT EXISTS `coa_kategori` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode` varchar(10) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `tipe` enum('Aset','Liabilitas','Ekuitas','Pendapatan','Beban') NOT NULL,
  `deskripsi` mediumtext DEFAULT NULL,
  `urutan` int(11) DEFAULT 0,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode` (`kode`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.coa_kategori: ~5 rows (approximately)
INSERT INTO `coa_kategori` (`id`, `kode`, `nama`, `tipe`, `deskripsi`, `urutan`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, '1', 'Aset', 'Aset', 'Aset perusahaan', 1, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(2, '2', 'Liabilitas', 'Liabilitas', 'Kewajiban perusahaan', 2, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(3, '3', 'Ekuitas', 'Ekuitas', 'Modal perusahaan', 3, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(4, '4', 'Pendapatan', 'Pendapatan', 'Pendapatan perusahaan', 4, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43'),
	(5, '5', 'Beban', 'Beban', 'Beban perusahaan', 5, 1, '2025-06-15 02:16:43', '2025-06-15 02:16:43');

-- Dumping structure for table toko_elektronik.coa_transaksi
CREATE TABLE IF NOT EXISTS `coa_transaksi` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `nomor_transaksi` varchar(50) NOT NULL,
  `tanggal_transaksi` date NOT NULL,
  `tipe_transaksi` enum('Jurnal Umum','Penerimaan Kas','Pengeluaran Kas','Penyesuaian','Penutupan') NOT NULL,
  `deskripsi` mediumtext DEFAULT NULL,
  `referensi` varchar(100) DEFAULT NULL,
  `total_debit` decimal(18,2) NOT NULL DEFAULT 0.00,
  `total_kredit` decimal(18,2) NOT NULL DEFAULT 0.00,
  `status` enum('draft','posting','batal') NOT NULL DEFAULT 'draft',
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_transaksi` (`nomor_transaksi`),
  KEY `idx_coa_transaksi_tanggal` (`tanggal_transaksi`),
  KEY `idx_coa_transaksi_tipe` (`tipe_transaksi`),
  KEY `idx_coa_transaksi_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.coa_transaksi: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.coa_transaksi_detail
CREATE TABLE IF NOT EXISTS `coa_transaksi_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `id_transaksi` bigint(20) NOT NULL,
  `id_akun` int(11) NOT NULL,
  `debit` decimal(18,2) NOT NULL DEFAULT 0.00,
  `kredit` decimal(18,2) NOT NULL DEFAULT 0.00,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_transaksi` (`id_transaksi`),
  KEY `id_akun` (`id_akun`),
  CONSTRAINT `fk_coa_transaksi_detail_akun` FOREIGN KEY (`id_akun`) REFERENCES `coa_akun` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_coa_transaksi_detail_transaksi` FOREIGN KEY (`id_transaksi`) REFERENCES `coa_transaksi` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.coa_transaksi_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.ekspedisi
CREATE TABLE IF NOT EXISTS `ekspedisi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_ekspedisi` varchar(20) NOT NULL,
  `nama_ekspedisi` varchar(100) NOT NULL,
  `website` varchar(100) DEFAULT NULL,
  `telepon` varchar(20) DEFAULT NULL,
  `tarif_per_kg` decimal(10,2) DEFAULT 0.00,
  `aktif` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_ekspedisi` (`kode_ekspedisi`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.ekspedisi: ~7 rows (approximately)
INSERT INTO `ekspedisi` (`id`, `kode_ekspedisi`, `nama_ekspedisi`, `website`, `telepon`, `tarif_per_kg`, `aktif`) VALUES
	(1, 'JNE', 'JNE Express', 'www.jne.co.id', '021-2927-8888', 15000.00, 1),
	(2, 'SCP', 'Sicepat Express', 'www.sicepat.com', '021-5020-0050', 12000.00, 1),
	(3, 'JNT', 'J&T Express', 'www.jet.co.id', '021-8066-0888', 13000.00, 1),
	(4, 'POS', 'Pos Indonesia', 'www.posindonesia.co.id', '161', 10000.00, 1),
	(5, 'ANT', 'Anteraja', 'www.anteraja.id', '021-5060-3030', 14000.00, 1),
	(6, 'TKI', 'Tiki', 'www.tiki.id', '1500-125', 16000.00, 1),
	(7, 'LNX', 'Lion Parcel', 'www.lionparcel.com', '021-6379-8888', 11000.00, 1);

-- Dumping structure for table toko_elektronik.error_log
CREATE TABLE IF NOT EXISTS `error_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) NOT NULL,
  `error_message` mediumtext NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.error_log: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.faktur_penjualan
CREATE TABLE IF NOT EXISTS `faktur_penjualan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_faktur` varchar(50) NOT NULL,
  `tanggal_faktur` date NOT NULL,
  `tanggal_jatuh_tempo` date DEFAULT NULL,
  `id_pengiriman` int(11) NOT NULL,
  `id_pelanggan` int(11) NOT NULL,
  `id_pesanan` int(11) NOT NULL,
  `status` enum('draft','final','batal') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `diskon` decimal(15,2) NOT NULL DEFAULT 0.00,
  `pajak` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total_faktur` decimal(15,2) NOT NULL DEFAULT 0.00,
  `keterangan` mediumtext DEFAULT NULL,
  `jatuh_tempo` date DEFAULT NULL,
  `status_pembayaran` enum('belum_bayar','sebagian','lunas') NOT NULL DEFAULT 'belum_bayar',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_faktur` (`nomor_faktur`),
  KEY `id_pengiriman` (`id_pengiriman`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `id_pesanan` (`id_pesanan`),
  KEY `idx_faktur_penjualan_tanggal` (`tanggal_faktur`),
  KEY `idx_faktur_penjualan_status` (`status`),
  KEY `idx_faktur_penjualan_status_pembayaran` (`status_pembayaran`),
  CONSTRAINT `faktur_penjualan_ibfk_1` FOREIGN KEY (`id_pengiriman`) REFERENCES `pengiriman` (`id`),
  CONSTRAINT `faktur_penjualan_ibfk_2` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`),
  CONSTRAINT `faktur_penjualan_ibfk_3` FOREIGN KEY (`id_pesanan`) REFERENCES `pesanan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.faktur_penjualan: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.faktur_penjualan_detail
CREATE TABLE IF NOT EXISTS `faktur_penjualan_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_faktur_penjualan` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `qty` decimal(10,2) NOT NULL,
  `harga_satuan` decimal(15,2) NOT NULL,
  `diskon_persen` decimal(5,2) DEFAULT 0.00,
  `diskon_nilai` decimal(15,2) DEFAULT 0.00,
  `subtotal` decimal(15,2) NOT NULL,
  `pajak_persen` decimal(5,2) DEFAULT 0.00,
  `pajak_nilai` decimal(15,2) DEFAULT 0.00,
  `total` decimal(15,2) NOT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_faktur_penjualan` (`id_faktur_penjualan`),
  KEY `id_barang` (`id_barang`),
  CONSTRAINT `faktur_penjualan_detail_ibfk_1` FOREIGN KEY (`id_faktur_penjualan`) REFERENCES `faktur_penjualan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `faktur_penjualan_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.faktur_penjualan_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.faktur_penjualan_pembayaran
CREATE TABLE IF NOT EXISTS `faktur_penjualan_pembayaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_faktur_penjualan` int(11) NOT NULL,
  `tanggal_pembayaran` date NOT NULL,
  `jumlah_pembayaran` decimal(15,2) NOT NULL,
  `metode_pembayaran` enum('tunai','transfer','cek','kartu_kredit','kartu_debit','lainnya') NOT NULL,
  `referensi_pembayaran` varchar(100) DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_faktur_penjualan` (`id_faktur_penjualan`),
  KEY `idx_faktur_penjualan_pembayaran_tanggal` (`tanggal_pembayaran`),
  CONSTRAINT `faktur_penjualan_pembayaran_ibfk_1` FOREIGN KEY (`id_faktur_penjualan`) REFERENCES `faktur_penjualan` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.faktur_penjualan_pembayaran: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.gudang
CREATE TABLE IF NOT EXISTS `gudang` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_gudang` varchar(50) DEFAULT NULL,
  `nama_gudang` varchar(100) NOT NULL,
  `alamat` mediumtext DEFAULT NULL,
  `penanggung_jawab` varchar(100) DEFAULT NULL,
  `no_telp` varchar(30) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_gudang` (`kode_gudang`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.gudang: ~3 rows (approximately)
INSERT INTO `gudang` (`id`, `kode_gudang`, `nama_gudang`, `alamat`, `penanggung_jawab`, `no_telp`, `email`, `keterangan`, `aktif`, `created_at`, `updated_at`) VALUES
	(4, 'GDG0001', 'Gudang Utama', 'Jl. Raya Industri No. 123, Jakarta Timur', 'Budi Santoso', '021-8765432', '<EMAIL>', 'Gudang utama untuk penyimpanan barang elektronik', 1, '2025-05-29 09:07:02', '2025-05-29 09:07:02'),
	(6, 'GDG0002', 'Gudang Cabang', 'Jl. Gatot Subroto No. 321, Jakarta Selatan', 'Dewi Lestari', '021-5432109', '<EMAIL>', 'Gudang khusus untuk spare parts dan aksesoris', 1, '2025-05-29 09:07:02', '2025-06-27 10:00:07'),
	(7, 'GDG0003', 'Gudang Retur', 'Jl. MT Haryono No. 654, Jakarta Timur', 'Rudi Hermawan', '021-2109876', '<EMAIL>', 'Gudang untuk barang return dan refurbish', 1, '2025-05-29 09:07:02', '2025-06-27 10:11:33');

-- Dumping structure for table toko_elektronik.jenis_pajak
CREATE TABLE IF NOT EXISTS `jenis_pajak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_pajak` varchar(50) NOT NULL,
  `nama_pajak` varchar(100) NOT NULL,
  `tarif_persen` decimal(5,2) NOT NULL COMMENT 'Persentase potongan pajak, contoh 10.00 untuk 10%',
  `tanggal_mulai` date NOT NULL,
  `tanggal_selesai` date DEFAULT NULL COMMENT 'Boleh NULL jika masa berakhir belum ditentukan',
  `keterangan` mediumtext DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_pajak` (`kode_pajak`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.jenis_pajak: ~4 rows (approximately)
INSERT INTO `jenis_pajak` (`id`, `kode_pajak`, `nama_pajak`, `tarif_persen`, `tanggal_mulai`, `tanggal_selesai`, `keterangan`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'PJK0001', 'PPN (Pajak Pertambahan Nilai)', 11.00, '2022-04-01', NULL, 'Pajak Pertambahan Nilai sesuai UU No. 7 Tahun 2021', 1, '2025-05-29 07:56:28', '2025-05-29 07:56:28'),
	(2, 'PJK0002', 'PPh 21', 5.00, '2021-01-01', NULL, 'Pajak Penghasilan Pasal 21 untuk karyawan', 1, '2025-05-29 07:56:28', '2025-05-29 07:56:28'),
	(3, 'PJK0003', 'PPh 23', 2.00, '2021-01-01', NULL, 'Pajak Penghasilan Pasal 23 untuk jasa', 1, '2025-05-29 07:56:28', '2025-05-29 07:56:28'),
	(4, 'PJK0004', 'PPN Lama', 10.00, '2020-01-01', '2022-03-31', 'PPN tarif lama sebelum kenaikan', 0, '2025-05-29 07:56:28', '2025-05-29 07:56:28');

-- Dumping structure for table toko_elektronik.pelanggan
CREATE TABLE IF NOT EXISTS `pelanggan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode` varchar(50) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` mediumtext NOT NULL,
  `alamat_kirim` mediumtext DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `nama_pic` varchar(100) DEFAULT NULL,
  `telepon_pic` varchar(20) DEFAULT NULL,
  `npwp` varchar(30) DEFAULT NULL,
  `no_ktp` varchar(30) DEFAULT NULL,
  `is_pkp` tinyint(1) DEFAULT 0,
  `status_aktif` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode` (`kode`),
  KEY `idx_pelanggan_nama` (`nama`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pelanggan: ~3 rows (approximately)
INSERT INTO `pelanggan` (`id`, `kode`, `nama`, `no_telepon`, `alamat`, `alamat_kirim`, `email`, `nama_pic`, `telepon_pic`, `npwp`, `no_ktp`, `is_pkp`, `status_aktif`, `created_at`, `updated_at`) VALUES
	(1, 'PLG0001', 'PT Elektronik Jaya', '08**********', 'Jl. Sudirman No. 123, Tanah Abang, Jakarta Pusat 10270', 'Jl. Sudirman No. 123, Tanah Abang, Jakarta Pusat 10270', '<EMAIL>', 'Budi Santoso', '08********91', '01.234.567.8-901.000', '31710**********1', 1, 1, '2025-05-29 13:38:00', '2025-05-31 03:21:22'),
	(2, 'PLG0002', 'PT Mitra Teknologi Nusantara', '081345678901', 'Jl. Gatot Subroto Kav. 45, Setiabudi, Jakarta Selatan 12930', 'Jl. Gatot Subroto Kav. 45, Setiabudi, Jakarta Selatan 12930', '<EMAIL>', 'Sari Dewi', '081345678902', '01.345.678.9-012.000', '3172023456789012', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(3, 'PLG0003', 'PT Digital Solutions Indonesia', '081456789012', 'Jl. HR Rasuna Said Blok X-5 Kav. 1-2, Kuningan, Jakarta Selatan 12950', 'Jl. HR Rasuna Said Blok X-5 Kav. 1-2, Kuningan, Jakarta Selatan 12950', '<EMAIL>', 'Ahmad Rizki', '081456789013', '01.456.789.0-123.000', '3173034567890123', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22');

-- Dumping structure for table toko_elektronik.pembelian
CREATE TABLE IF NOT EXISTS `pembelian` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_pembelian` varchar(50) NOT NULL,
  `tanggal_pembelian` date NOT NULL,
  `id_supplier` int(11) NOT NULL,
  `jenis_pembelian` enum('reguler','konsinyasi','kontrak') NOT NULL DEFAULT 'reguler',
  `status` enum('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `diskon_persen` decimal(5,2) DEFAULT 0.00,
  `diskon_nominal` decimal(15,2) DEFAULT 0.00,
  `total_sebelum_pajak` decimal(15,2) NOT NULL DEFAULT 0.00,
  `ppn_persen` decimal(5,2) DEFAULT 11.00,
  `ppn_nominal` decimal(15,2) DEFAULT 0.00,
  `total_setelah_pajak` decimal(15,2) NOT NULL DEFAULT 0.00,
  `biaya_pengiriman` decimal(15,2) DEFAULT 0.00,
  `total_akhir` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tanggal_jatuh_tempo` date DEFAULT NULL,
  `syarat_pembayaran` varchar(100) DEFAULT NULL,
  `metode_pembayaran` enum('tunai','transfer','kredit','cek','giro') DEFAULT 'transfer',
  `nomor_po_supplier` varchar(50) DEFAULT NULL,
  `alamat_pengiriman` mediumtext DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `approved_by` varchar(50) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pembelian` (`nomor_pembelian`),
  KEY `id_supplier` (`id_supplier`),
  KEY `idx_pembelian_tanggal` (`tanggal_pembelian`),
  KEY `idx_pembelian_status` (`status`),
  KEY `idx_pembelian_jenis` (`jenis_pembelian`),
  CONSTRAINT `pembelian_ibfk_1` FOREIGN KEY (`id_supplier`) REFERENCES `supplier` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pembelian: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.pembelian_detail
CREATE TABLE IF NOT EXISTS `pembelian_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pembelian` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty` decimal(10,2) NOT NULL,
  `harga_satuan` decimal(15,2) NOT NULL,
  `diskon_persen` decimal(5,2) DEFAULT 0.00,
  `diskon_nominal` decimal(15,2) DEFAULT 0.00,
  `subtotal_sebelum_diskon` decimal(15,2) NOT NULL,
  `subtotal_setelah_diskon` decimal(15,2) NOT NULL,
  `ppn_persen` decimal(5,2) DEFAULT 11.00,
  `ppn_nominal` decimal(15,2) DEFAULT 0.00,
  `total_akhir` decimal(15,2) NOT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pembelian` (`id_pembelian`),
  KEY `id_barang` (`id_barang`),
  KEY `id_gudang` (`id_gudang`),
  CONSTRAINT `pembelian_detail_ibfk_1` FOREIGN KEY (`id_pembelian`) REFERENCES `pembelian` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pembelian_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`),
  CONSTRAINT `pembelian_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pembelian_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.pembelian_pembayaran
CREATE TABLE IF NOT EXISTS `pembelian_pembayaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pembelian` int(11) NOT NULL,
  `nomor_pembayaran` varchar(50) NOT NULL,
  `tanggal_pembayaran` date NOT NULL,
  `jumlah_bayar` decimal(15,2) NOT NULL,
  `metode_pembayaran` enum('tunai','transfer','kredit','cek','giro') NOT NULL,
  `nomor_referensi` varchar(100) DEFAULT NULL,
  `bank_pengirim` varchar(100) DEFAULT NULL,
  `bank_penerima` varchar(100) DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `bukti_pembayaran` varchar(255) DEFAULT NULL,
  `status` enum('pending','verified','rejected') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `verified_by` varchar(50) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pembayaran` (`nomor_pembayaran`),
  KEY `id_pembelian` (`id_pembelian`),
  KEY `idx_pembayaran_tanggal` (`tanggal_pembayaran`),
  KEY `idx_pembayaran_status` (`status`),
  CONSTRAINT `pembelian_pembayaran_ibfk_1` FOREIGN KEY (`id_pembelian`) REFERENCES `pembelian` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pembelian_pembayaran: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.pembelian_tracking
CREATE TABLE IF NOT EXISTS `pembelian_tracking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pembelian` int(11) NOT NULL,
  `status` enum('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NOT NULL,
  `tanggal_status` datetime NOT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `created_by` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_pembelian` (`id_pembelian`),
  KEY `idx_tracking_tanggal` (`tanggal_status`),
  CONSTRAINT `pembelian_tracking_ibfk_1` FOREIGN KEY (`id_pembelian`) REFERENCES `pembelian` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pembelian_tracking: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.penerimaan_pembelian
CREATE TABLE IF NOT EXISTS `penerimaan_pembelian` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_penerimaan` varchar(50) NOT NULL,
  `tanggal_penerimaan` date NOT NULL,
  `id_pembelian` int(11) NOT NULL,
  `id_supplier` int(11) NOT NULL,
  `status` enum('draft','diterima','selesai','dibatalkan') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `keterangan` text DEFAULT NULL,
  `penerima` varchar(100) DEFAULT NULL,
  `tanggal_diterima` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_penerimaan` (`nomor_penerimaan`),
  KEY `id_pembelian` (`id_pembelian`),
  KEY `id_supplier` (`id_supplier`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `fk_penerimaan_pembelian_pembelian` FOREIGN KEY (`id_pembelian`) REFERENCES `pembelian` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_penerimaan_pembelian_supplier` FOREIGN KEY (`id_supplier`) REFERENCES `supplier` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.penerimaan_pembelian: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.penerimaan_pembelian_detail
CREATE TABLE IF NOT EXISTS `penerimaan_pembelian_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_penerimaan` int(11) NOT NULL,
  `id_pembelian_detail` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_pembelian` decimal(10,2) NOT NULL DEFAULT 0.00,
  `qty_diterima` decimal(10,2) NOT NULL DEFAULT 0.00,
  `qty_ditolak` decimal(10,2) NOT NULL DEFAULT 0.00,
  `alasan_penolakan` varchar(255) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_penerimaan` (`id_penerimaan`),
  KEY `id_pembelian_detail` (`id_pembelian_detail`),
  KEY `id_barang` (`id_barang`),
  KEY `id_gudang` (`id_gudang`),
  CONSTRAINT `fk_penerimaan_detail_barang` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_penerimaan_detail_gudang` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_penerimaan_detail_pembelian_detail` FOREIGN KEY (`id_pembelian_detail`) REFERENCES `pembelian_detail` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_penerimaan_detail_penerimaan` FOREIGN KEY (`id_penerimaan`) REFERENCES `penerimaan_pembelian` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.penerimaan_pembelian_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.pengiriman
CREATE TABLE IF NOT EXISTS `pengiriman` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_pengiriman` varchar(50) NOT NULL,
  `tanggal_pengiriman` date NOT NULL,
  `id_pesanan` int(11) NOT NULL,
  `id_pelanggan` int(11) NOT NULL,
  `alamat_pengiriman` mediumtext NOT NULL,
  `status` enum('draft','prepared','shipped','in_transit','delivered','returned','cancelled') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_berat` decimal(10,2) DEFAULT 0.00,
  `estimasi_tiba` date DEFAULT NULL,
  `tanggal_dikirim` datetime DEFAULT NULL,
  `tanggal_diterima` datetime DEFAULT NULL,
  `penerima` varchar(100) DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pengiriman` (`nomor_pengiriman`),
  KEY `id_pesanan` (`id_pesanan`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `idx_pengiriman_tanggal` (`tanggal_pengiriman`),
  KEY `idx_pengiriman_status` (`status`),
  KEY `idx_pengiriman_created_at` (`created_at`),
  KEY `idx_pengiriman_updated_at` (`updated_at`),
  CONSTRAINT `pengiriman_ibfk_1` FOREIGN KEY (`id_pesanan`) REFERENCES `pesanan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pengiriman_ibfk_2` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pengiriman: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.pengiriman_detail
CREATE TABLE IF NOT EXISTS `pengiriman_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pengiriman` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_dikirim` decimal(10,2) NOT NULL,
  `berat_satuan` decimal(10,2) DEFAULT 0.00,
  `total_berat` decimal(10,2) GENERATED ALWAYS AS (`qty_dikirim` * `berat_satuan`) STORED,
  `keterangan` mediumtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pengiriman` (`id_pengiriman`),
  KEY `id_barang` (`id_barang`),
  KEY `id_gudang` (`id_gudang`),
  KEY `idx_pengiriman_detail_qty` (`qty_dikirim`),
  KEY `idx_pengiriman_detail_berat` (`total_berat`),
  CONSTRAINT `pengiriman_detail_ibfk_1` FOREIGN KEY (`id_pengiriman`) REFERENCES `pengiriman` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pengiriman_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`),
  CONSTRAINT `pengiriman_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pengiriman_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.penyesuaian_stok
CREATE TABLE IF NOT EXISTS `penyesuaian_stok` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_penyesuaian` varchar(50) NOT NULL,
  `tanggal` date NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_awal` decimal(10,2) NOT NULL,
  `qty_baru` decimal(10,2) NOT NULL,
  `qty_selisih` decimal(10,2) NOT NULL,
  `jenis_penyesuaian` enum('PENAMBAHAN','PENGURANGAN') NOT NULL,
  `alasan` mediumtext DEFAULT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_penyesuaian` (`kode_penyesuaian`),
  KEY `idx_penyesuaian_stok_tanggal` (`tanggal`),
  KEY `idx_penyesuaian_stok_barang` (`id_barang`),
  KEY `idx_penyesuaian_stok_gudang` (`id_gudang`),
  KEY `idx_penyesuaian_stok_kode` (`kode_penyesuaian`),
  CONSTRAINT `penyesuaian_stok_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `penyesuaian_stok_ibfk_2` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.penyesuaian_stok: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.pesanan
CREATE TABLE IF NOT EXISTS `pesanan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_pesanan` varchar(50) NOT NULL,
  `tanggal_pesanan` date NOT NULL,
  `id_pelanggan` int(11) NOT NULL,
  `jenis_pesanan` enum('manual','android') NOT NULL,
  `status` enum('draft','diproses','dikirim','selesai','dibatalkan') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_harga` decimal(15,2) NOT NULL DEFAULT 0.00,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pesanan` (`nomor_pesanan`),
  KEY `id_pelanggan` (`id_pelanggan`),
  CONSTRAINT `pesanan_ibfk_1` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pesanan: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.pesanan_detail
CREATE TABLE IF NOT EXISTS `pesanan_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pesanan` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `qty` decimal(10,2) NOT NULL,
  `harga_satuan` decimal(15,2) NOT NULL,
  `subtotal` decimal(15,2) NOT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pesanan` (`id_pesanan`),
  KEY `id_barang` (`id_barang`),
  CONSTRAINT `pesanan_detail_ibfk_1` FOREIGN KEY (`id_pesanan`) REFERENCES `pesanan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pesanan_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.pesanan_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.retur_pembelian
CREATE TABLE IF NOT EXISTS `retur_pembelian` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_retur` varchar(50) NOT NULL,
  `tanggal_retur` date NOT NULL,
  `id_penerimaan` int(11) DEFAULT NULL,
  `id_pembelian` int(11) NOT NULL,
  `id_supplier` int(11) NOT NULL,
  `alasan_retur` mediumtext DEFAULT NULL,
  `status` enum('draft','diproses','selesai','dibatalkan') NOT NULL DEFAULT 'draft',
  `compensation_type` enum('belum_ditentukan','barang','uang','offset') NOT NULL DEFAULT 'belum_ditentukan' COMMENT 'Jenis kompensasi retur',
  `compensation_status` enum('menunggu','dalam_proses','selesai','gagal','ditolak') NOT NULL DEFAULT 'menunggu' COMMENT 'Status kompensasi',
  `reference_invoice` varchar(50) DEFAULT NULL COMMENT 'Nomor invoice untuk offset',
  `compensation_notes` mediumtext DEFAULT NULL COMMENT 'Catatan kompensasi',
  `compensation_date` date DEFAULT NULL COMMENT 'Tanggal kompensasi diselesaikan',
  `compensation_amount` decimal(15,2) DEFAULT 0.00 COMMENT 'Nilai kompensasi',
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `total_nilai` decimal(15,2) DEFAULT 0.00,
  `keterangan` mediumtext DEFAULT NULL,
  `ref_barang_keluar` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_retur` (`nomor_retur`),
  KEY `idx_compensation_type` (`compensation_type`),
  KEY `idx_compensation_status` (`compensation_status`),
  KEY `idx_reference_invoice` (`reference_invoice`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.retur_pembelian: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.retur_pembelian_detail
CREATE TABLE IF NOT EXISTS `retur_pembelian_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_retur` int(11) NOT NULL,
  `id_penerimaan_detail` int(11) DEFAULT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_retur` decimal(12,2) NOT NULL DEFAULT 0.00,
  `compensation_qty` decimal(12,2) DEFAULT 0.00 COMMENT 'Qty yang sudah dikompensasi',
  `compensation_type_detail` enum('belum_ditentukan','barang','uang','offset') DEFAULT NULL COMMENT 'Jenis kompensasi per item',
  `replacement_received` tinyint(1) DEFAULT 0 COMMENT 'Apakah barang pengganti sudah diterima',
  `kondisi_barang` enum('baik','rusak','cacat') NOT NULL DEFAULT 'baik',
  `harga_satuan` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total_harga` decimal(15,2) GENERATED ALWAYS AS (`qty_retur` * `harga_satuan`) STORED,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_retur` (`id_retur`),
  KEY `idx_compensation_type_detail` (`compensation_type_detail`),
  KEY `idx_replacement_received` (`replacement_received`),
  CONSTRAINT `retur_pembelian_detail_ibfk_1` FOREIGN KEY (`id_retur`) REFERENCES `retur_pembelian` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.retur_pembelian_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.retur_penjualan
CREATE TABLE IF NOT EXISTS `retur_penjualan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_retur` varchar(50) NOT NULL,
  `tanggal_retur` date NOT NULL,
  `id_faktur` int(11) DEFAULT NULL,
  `id_pengiriman` int(11) DEFAULT NULL,
  `id_pelanggan` int(11) NOT NULL,
  `alasan_retur` mediumtext DEFAULT NULL,
  `status` enum('draft','diproses','selesai','dibatalkan') NOT NULL DEFAULT 'draft',
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `total_nilai` decimal(15,2) DEFAULT 0.00,
  `keterangan` mediumtext DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_retur` (`nomor_retur`),
  KEY `id_faktur` (`id_faktur`),
  KEY `id_pengiriman` (`id_pengiriman`),
  KEY `id_pelanggan` (`id_pelanggan`),
  CONSTRAINT `fk_retur_faktur` FOREIGN KEY (`id_faktur`) REFERENCES `faktur_penjualan` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_retur_pelanggan` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_retur_pengiriman` FOREIGN KEY (`id_pengiriman`) REFERENCES `pengiriman` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.retur_penjualan: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.retur_penjualan_detail
CREATE TABLE IF NOT EXISTS `retur_penjualan_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_retur` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_retur` decimal(12,2) NOT NULL DEFAULT 0.00,
  `kondisi_barang` enum('baik','rusak','cacat') NOT NULL DEFAULT 'baik',
  `harga_satuan` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total_harga` decimal(15,2) GENERATED ALWAYS AS (`qty_retur` * `harga_satuan`) STORED,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_retur` (`id_retur`),
  KEY `id_barang` (`id_barang`),
  KEY `id_gudang` (`id_gudang`),
  CONSTRAINT `fk_retur_detail_barang` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_retur_detail_gudang` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_retur_detail_retur` FOREIGN KEY (`id_retur`) REFERENCES `retur_penjualan` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.retur_penjualan_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.satuan
CREATE TABLE IF NOT EXISTS `satuan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_satuan` varchar(20) NOT NULL,
  `nama_satuan` varchar(100) NOT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_satuan` (`kode_satuan`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.satuan: ~3 rows (approximately)
INSERT INTO `satuan` (`id`, `kode_satuan`, `nama_satuan`, `keterangan`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'PCS', 'Pieces', 'Satuan untuk barang per buah/unit', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(2, 'SET', 'Set', 'Satuan untuk barang dalam bentuk set/paket', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(3, 'UNIT', 'Unit', 'Satuan untuk barang per unit', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(4, 'BOX', 'Box', 'Satuan untuk barang dalam kemasan box', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47');

-- Dumping structure for procedure toko_elektronik.sp_update_pembelian_status_after_penerimaan
DELIMITER //
CREATE PROCEDURE `sp_update_pembelian_status_after_penerimaan`(IN p_id_pembelian INT)
BEGIN
    DECLARE total_items INT;
    DECLARE received_items INT;
    
    -- Hitung total item di pembelian
    SELECT COUNT(*) INTO total_items FROM pembelian_detail WHERE id_pembelian = p_id_pembelian;
    
    -- Hitung item yang sudah diterima
    SELECT COUNT(DISTINCT pd.id) INTO received_items
    FROM pembelian_detail pd
    JOIN penerimaan_pembelian_detail ppd ON pd.id = ppd.id_pembelian_detail
    JOIN penerimaan_pembelian pp ON ppd.id_penerimaan = pp.id
    WHERE pd.id_pembelian = p_id_pembelian AND pp.status = 'diterima';
    
    -- Update status pembelian
    IF received_items >= total_items THEN
        UPDATE pembelian SET status = 'diterima' WHERE id = p_id_pembelian;
    END IF;
END//
DELIMITER ;

-- Dumping structure for procedure toko_elektronik.sp_update_pembelian_totals
DELIMITER //
CREATE PROCEDURE `sp_update_pembelian_totals`(IN p_id_pembelian INT)
BEGIN
    DECLARE v_total_item INT DEFAULT 0;
    DECLARE v_total_qty DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_subtotal DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_setelah_diskon DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_ppn DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_akhir DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_diskon_nominal DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_biaya_pengiriman DECIMAL(15,2) DEFAULT 0.00;
    
    -- Hitung total dari detail
    SELECT 
        COUNT(*),
        COALESCE(SUM(qty), 0),
        COALESCE(SUM(subtotal_sebelum_diskon), 0),
        COALESCE(SUM(subtotal_setelah_diskon), 0),
        COALESCE(SUM(ppn_nominal), 0),
        COALESCE(SUM(total_akhir), 0)
    INTO 
        v_total_item,
        v_total_qty,
        v_subtotal,
        v_total_setelah_diskon,
        v_total_ppn,
        v_total_akhir
    FROM pembelian_detail 
    WHERE id_pembelian = p_id_pembelian;
    
    -- Ambil diskon dan biaya pengiriman dari header
    SELECT 
        COALESCE(diskon_nominal, 0),
        COALESCE(biaya_pengiriman, 0)
    INTO 
        v_diskon_nominal,
        v_biaya_pengiriman
    FROM pembelian 
    WHERE id = p_id_pembelian;
    
    -- Update header pembelian
    UPDATE pembelian SET
        total_item = v_total_item,
        total_qty = v_total_qty,
        subtotal = v_subtotal,
        total_sebelum_pajak = v_total_setelah_diskon,
        ppn_nominal = v_total_ppn,
        total_setelah_pajak = v_total_setelah_diskon + v_total_ppn,
        total_akhir = v_total_setelah_diskon + v_total_ppn + v_biaya_pengiriman,
        updated_at = NOW()
    WHERE id = p_id_pembelian;
END//
DELIMITER ;

-- Dumping structure for procedure toko_elektronik.sp_update_retur_totals
DELIMITER //
CREATE PROCEDURE `sp_update_retur_totals`(IN retur_id INT)
BEGIN
    DECLARE total_items INT DEFAULT 0;
    DECLARE total_quantity DECIMAL(10,2) DEFAULT 0.00;
    DECLARE subtotal_amount DECIMAL(15,2) DEFAULT 0.00;
    DECLARE total_discount DECIMAL(15,2) DEFAULT 0.00;
    DECLARE total_tax DECIMAL(15,2) DEFAULT 0.00;
    DECLARE total_amount DECIMAL(15,2) DEFAULT 0.00;
    
    SELECT 
        COUNT(*),
        COALESCE(SUM(qty_retur), 0),
        COALESCE(SUM(qty_retur * harga_satuan), 0),
        COALESCE(SUM(diskon), 0),
        COALESCE(SUM(pajak), 0),
        COALESCE(SUM(total), 0)
    INTO 
        total_items,
        total_quantity,
        subtotal_amount,
        total_discount,
        total_tax,
        total_amount
    FROM retur_penjualan_detail 
    WHERE id_retur_penjualan = retur_id;
    
    UPDATE retur_penjualan 
    SET 
        total_item = total_items,
        total_qty = total_quantity,
        subtotal = subtotal_amount,
        diskon = total_discount,
        pajak = total_tax,
        total_retur = total_amount,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = retur_id;
END//
DELIMITER ;

-- Dumping structure for table toko_elektronik.stok_barang
CREATE TABLE IF NOT EXISTS `stok_barang` (
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_terakhir` decimal(12,2) NOT NULL DEFAULT 0.00,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id_barang`,`id_gudang`),
  KEY `id_gudang` (`id_gudang`),
  CONSTRAINT `stok_barang_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `stok_barang_ibfk_2` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.stok_barang: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.stok_movement
CREATE TABLE IF NOT EXISTS `stok_movement` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tanggal` datetime NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `tipe_transaksi` enum('pembelian','penjualan','retur_beli','retur_jual','penyesuaian','opname','transfer_masuk','transfer_keluar','pengiriman') NOT NULL,
  `qty_in` decimal(12,2) DEFAULT 0.00,
  `qty_out` decimal(12,2) DEFAULT 0.00,
  `keterangan` varchar(255) DEFAULT NULL,
  `ref_transaksi` varchar(100) DEFAULT NULL,
  `user_input` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_stok_movement_tanggal` (`tanggal`),
  KEY `idx_stok_movement_barang` (`id_barang`),
  KEY `idx_stok_movement_gudang` (`id_gudang`),
  KEY `idx_stok_movement_tipe` (`tipe_transaksi`),
  KEY `idx_stok_movement_ref` (`ref_transaksi`),
  KEY `idx_stok_movement_laporan` (`tanggal`,`tipe_transaksi`,`id_barang`),
  CONSTRAINT `stok_movement_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `stok_movement_ibfk_2` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.stok_movement: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.stok_opname
CREATE TABLE IF NOT EXISTS `stok_opname` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_opname` varchar(50) NOT NULL,
  `tanggal_opname` date NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `keterangan` varchar(255) DEFAULT NULL,
  `status` enum('draft','final') DEFAULT 'draft',
  `total_item` int(11) DEFAULT 0,
  `total_selisih_positif` decimal(15,2) DEFAULT 0.00,
  `total_selisih_negatif` decimal(15,2) DEFAULT 0.00,
  `user_input` varchar(100) DEFAULT NULL,
  `user_final` varchar(100) DEFAULT NULL,
  `tanggal_final` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_opname` (`nomor_opname`),
  KEY `idx_stok_opname_tanggal` (`tanggal_opname`),
  KEY `idx_stok_opname_gudang` (`id_gudang`),
  KEY `idx_stok_opname_status` (`status`),
  KEY `idx_stok_opname_nomor` (`nomor_opname`),
  CONSTRAINT `stok_opname_ibfk_1` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.stok_opname: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.stok_opname_detail
CREATE TABLE IF NOT EXISTS `stok_opname_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `id_opname` bigint(20) unsigned NOT NULL,
  `id_barang` int(11) NOT NULL,
  `qty_sistem` decimal(12,2) NOT NULL DEFAULT 0.00,
  `qty_fisik` decimal(12,2) NOT NULL DEFAULT 0.00,
  `selisih` decimal(12,2) GENERATED ALWAYS AS (`qty_fisik` - `qty_sistem`) STORED,
  `keterangan` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_opname_barang` (`id_opname`,`id_barang`),
  KEY `idx_stok_opname_detail_opname` (`id_opname`),
  KEY `idx_stok_opname_detail_barang` (`id_barang`),
  KEY `idx_stok_opname_detail_selisih` (`selisih`),
  CONSTRAINT `stok_opname_detail_ibfk_1` FOREIGN KEY (`id_opname`) REFERENCES `stok_opname` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `stok_opname_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.stok_opname_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.supplier
CREATE TABLE IF NOT EXISTS `supplier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode` varchar(50) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` mediumtext DEFAULT NULL,
  `alamat_kirim` mediumtext DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `nama_pic` varchar(100) DEFAULT NULL,
  `telepon_pic` varchar(20) DEFAULT NULL,
  `npwp` varchar(30) DEFAULT NULL,
  `no_ktp` varchar(30) DEFAULT NULL,
  `is_pkp` tinyint(1) DEFAULT 0,
  `status_aktif` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode` (`kode`),
  KEY `idx_supplier_nama` (`nama`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.supplier: ~3 rows (approximately)
INSERT INTO `supplier` (`id`, `kode`, `nama`, `no_telepon`, `alamat`, `alamat_kirim`, `email`, `nama_pic`, `telepon_pic`, `npwp`, `no_ktp`, `is_pkp`, `status_aktif`, `created_at`, `updated_at`) VALUES
	(1, 'SUP0001', 'PT Elektronik Supplier Utama', '08**********', 'Jl. Gajah Mada No. 45, Petojo Utara, Jakarta Pusat 10130', 'Jl. Gajah Mada No. 45, Petojo Utara, Jakarta Pusat 10130', '<EMAIL>', 'Ahmad Fauzi', '08********91', '01.234.567.8-901.000', '31710**********1', 1, 1, '2025-05-29 13:55:52', '2025-05-31 03:21:22'),
	(2, 'SUP0002', 'PT Elektronik Distributor Indonesia', '08**********', 'Jl. Mangga Dua Raya No. 88, Sawah Besar, Jakarta Pusat 10730', 'Jl. Mangga Dua Raya No. 88, Sawah Besar, Jakarta Pusat 10730', '<EMAIL>', 'Bambang Sutrisno', '08********91', '01.234.567.8-901.000', '31710**********1', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(3, 'SUP0003', 'PT Sumber Elektronik Nusantara', '081345678901', 'Jl. Glodok Plaza Blok A No. 15, Taman Sari, Jakarta Barat 11120', 'Jl. Glodok Plaza Blok A No. 15, Taman Sari, Jakarta Barat 11120', '<EMAIL>', 'Siti Nurhaliza', '081345678902', '01.345.678.9-012.000', '3174023456789012', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22');

-- Dumping structure for table toko_elektronik.tbl_akses_menu
CREATE TABLE IF NOT EXISTS `tbl_akses_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_level` int(11) NOT NULL,
  `id_menu` int(11) NOT NULL,
  `view` enum('Y','N') NOT NULL DEFAULT 'N',
  `add` enum('Y','N') NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') NOT NULL DEFAULT 'N',
  `print` enum('Y','N') NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') NOT NULL DEFAULT 'N',
  `download` enum('Y','N') NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_menu` (`id_menu`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_akses_menu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_menu_ibfk_2` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=448 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.tbl_akses_menu: ~20 rows (approximately)
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES
	(1, 1, 1, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(69, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(94, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(207, 1, 4, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(427, 1, 6, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(428, 1, 7, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(429, 1, 8, 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N'),
	(430, 1, 9, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(431, 1, 10, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(434, 2, 1, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(435, 2, 2, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(436, 2, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(437, 2, 4, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(438, 2, 6, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(439, 2, 7, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(440, 2, 8, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(441, 2, 9, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(442, 2, 10, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(443, 2, 13, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(447, 1, 16, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');

-- Dumping structure for table toko_elektronik.tbl_akses_submenu
CREATE TABLE IF NOT EXISTS `tbl_akses_submenu` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `id_level` int(11) NOT NULL,
  `id_submenu` int(11) NOT NULL,
  `view` enum('Y','N') NOT NULL DEFAULT 'N',
  `add` enum('Y','N') NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') NOT NULL DEFAULT 'N',
  `print` enum('Y','N') NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') NOT NULL DEFAULT 'N',
  `download` enum('Y','N') NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_level` (`id_level`),
  KEY `id_submenu` (`id_submenu`),
  CONSTRAINT `tbl_akses_submenu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_submenu_ibfk_2` FOREIGN KEY (`id_submenu`) REFERENCES `tbl_submenu` (`id_submenu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=416 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.tbl_akses_submenu: ~67 rows (approximately)
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES
	(2, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(4, 1, 1, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(6, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(9, 1, 4, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(209, 1, 5, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(289, 1, 6, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(295, 1, 7, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(351, 1, 71, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(352, 1, 72, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(353, 1, 73, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(354, 1, 74, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(355, 1, 75, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(356, 1, 76, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(359, 1, 79, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(360, 1, 80, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(361, 1, 81, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(362, 1, 82, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(363, 1, 83, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(364, 2, 1, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(365, 2, 2, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(366, 2, 3, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(367, 2, 4, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(368, 2, 5, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(369, 2, 6, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(370, 2, 7, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(371, 2, 71, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(372, 2, 72, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(373, 2, 73, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(374, 2, 74, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(375, 2, 75, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(376, 2, 76, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(378, 2, 79, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(379, 2, 80, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(380, 2, 81, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(381, 2, 82, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(382, 2, 83, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(383, 1, 84, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(384, 2, 84, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(385, 1, 85, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(386, 2, 85, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(387, 1, 86, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(388, 2, 86, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(389, 1, 87, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(390, 2, 87, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(391, 1, 88, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(392, 2, 88, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(395, 1, 90, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(396, 2, 90, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(397, 1, 91, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(398, 2, 91, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(399, 1, 92, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(400, 2, 92, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(401, 1, 93, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(402, 2, 93, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(403, 1, 94, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(404, 2, 94, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(405, 1, 95, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(406, 2, 95, 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'Y'),
	(407, 1, 96, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(408, 1, 97, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(409, 1, 98, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(410, 1, 99, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(411, 1, 100, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(412, 1, 101, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(413, 1, 102, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(414, 1, 103, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(415, 1, 104, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');

-- Dumping structure for table toko_elektronik.tbl_menu
CREATE TABLE IF NOT EXISTS `tbl_menu` (
  `id_menu` int(11) NOT NULL AUTO_INCREMENT,
  `nama_menu` varchar(50) DEFAULT NULL,
  `link` varchar(100) DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `urutan` bigint(20) DEFAULT NULL,
  `is_active` enum('Y','N') DEFAULT 'Y',
  `parent` enum('Y') DEFAULT 'Y',
  PRIMARY KEY (`id_menu`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.tbl_menu: ~11 rows (approximately)
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES
	(1, 'Home', 'dashboard', 'fas fa-tachometer-alt', 0, 'Y', 'Y'),
	(2, 'Konfigurasi', '#', 'fas fa-users-cog', 15, 'Y', 'Y'),
	(3, 'Ganti Password', 'ganti_password', 'fas fa-key', 9, 'Y', 'Y'),
	(4, 'Master', '#', 'fas fa-database', 7, 'Y', 'Y'),
	(6, 'Developer', '#', 'fas fa-tools', 10, 'Y', 'Y'),
	(7, 'Purchase', '#', 'fas fa-credit-card', 3, 'Y', 'Y'),
	(8, 'Sales', '#', 'fas fa-store', 1, 'Y', 'Y'),
	(9, 'Warehouse', '#', 'fas fa-warehouse', 2, 'Y', 'Y'),
	(10, 'Akunting', '#', 'fas fa-receipt', 5, 'Y', 'Y'),
	(13, 'Laporan Stok', 'laporan_stok', 'fa-chart-bar', 90, 'Y', 'Y'),
	(16, 'Settlement', 'settlement', 'fas fa-balance-scale', 8, 'Y', 'Y');

-- Dumping structure for table toko_elektronik.tbl_submenu
CREATE TABLE IF NOT EXISTS `tbl_submenu` (
  `id_submenu` int(11) NOT NULL AUTO_INCREMENT,
  `nama_submenu` varchar(50) DEFAULT NULL,
  `link` varchar(100) DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `id_menu` int(11) DEFAULT NULL,
  `is_active` enum('Y','N') DEFAULT 'Y',
  `urutan` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id_submenu`),
  KEY `id_menu` (`id_menu`),
  CONSTRAINT `tbl_submenu_ibfk_1` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=105 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.tbl_submenu: ~38 rows (approximately)
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES
	(1, 'Menu', 'menu', 'far fa-circle', 2, 'Y', NULL),
	(2, 'Sub Menu', 'submenu', 'far fa-circle', 2, 'Y', NULL),
	(3, 'Aplikasi', 'aplikasi', 'far fa-circle', 2, 'Y', NULL),
	(4, 'User Level', 'userlevel', 'far fa-circle', 2, 'Y', NULL),
	(5, 'Data Pengguna', 'user', 'far fa-circle', 2, 'Y', NULL),
	(6, 'Barang', 'barang', 'far fa-circle', 4, 'Y', NULL),
	(7, 'Satuan', 'satuan', 'far fa-circle', 4, 'Y', NULL),
	(71, 'Create Tabel', 'TemplateController', 'far fa-circle', 6, 'Y', 1),
	(72, 'Gudang', 'gudang', 'far fa-circle', 4, 'Y', 4),
	(73, 'Pelanggan', 'pelanggan', 'far fa-circle', 4, 'Y', 5),
	(74, 'Supplier', 'supplier', 'far fa-circle', 4, 'Y', 3),
	(75, 'Barang Masuk', 'BarangMasuk', 'far fa-circle', 9, 'Y', 1),
	(76, 'Barang Keluar', 'BarangKeluar', 'far fa-circle', 9, 'Y', 2),
	(79, 'Jenis Pajak', 'jenis_pajak', 'far fa-circle', 4, 'Y', 4),
	(80, 'Penyesuaian Stok', 'Penyesuaian', 'far fa-circle', 9, 'Y', 3),
	(81, 'Stok Opname', 'StokOpname', 'far fa-circle', 9, 'Y', 4),
	(82, 'Transfer Stok', 'TransferStok', 'far fa-circle', 9, 'Y', 5),
	(83, 'Laporan Stok', 'laporan_stok', 'far fa-circle', 9, 'Y', 6),
	(84, 'Pesanan', 'Pesanan', 'far fa-circle', 8, 'Y', 1),
	(85, 'Pengiriman', 'Pengiriman', 'far fa-circle', 8, 'Y', 2),
	(86, 'Faktur Penjualan', 'FakturPenjualan', 'far fa-circle', 8, 'Y', 3),
	(87, 'Pembelian', 'Pembelian', 'far fa-circle', 7, 'Y', 1),
	(88, 'COA', 'coa', 'far fa-circle', 10, 'Y', 1),
	(90, 'Retur Penjualan', 'ReturPenjualan', 'far fa-circle', 8, 'Y', 4),
	(91, 'Penerimaan', 'PenerimaanPembelian', 'far fa-circle', 7, 'Y', 2),
	(92, 'Retur Pembelian', 'ReturPembelian', 'far fa-circle', 7, 'Y', 4),
	(93, 'Laporan Pembelian', 'Laporan_pembelian', 'far fa-circle', 7, 'Y', 4),
	(94, 'Laporan Penjualan', 'Laporan_penjualan', 'far fa-circle', 8, 'Y', 5),
	(95, 'Dashboard Kompensasi', 'ReturPembelian/compensation_dashboard', 'fas fa-chart-pie', 7, 'Y', 5),
	(96, 'Dashboard', 'settlement', 'fas fa-tachometer-alt', 16, 'Y', 1),
	(97, 'Penjualan', 'settlement/penjualan', 'fas fa-truck', 16, 'Y', 2),
	(98, 'Pembelian', 'settlement/pembelian', 'fas fa-shopping-cart', 16, 'Y', 3),
	(99, 'Pembayaran', 'settlement/pembayaran', 'fas fa-money-bill-wave', 16, 'Y', 4),
	(100, 'Stok', 'settlement/stok', 'fas fa-boxes', 16, 'Y', 5),
	(101, 'Kas dan Bank', 'settlement/kas_bank', 'fas fa-university', 16, 'Y', 6),
	(102, 'Pajak', 'settlement/pajak', 'fas fa-file-invoice-dollar', 16, 'Y', 7),
	(103, 'Komisi', 'settlement/komisi', 'fas fa-hand-holding-usd', 16, 'Y', 8),
	(104, 'Retur', 'settlement/retur', 'fas fa-exchange-alt', 16, 'Y', 9);

-- Dumping structure for table toko_elektronik.tbl_user
CREATE TABLE IF NOT EXISTS `tbl_user` (
  `id_user` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(20) DEFAULT NULL,
  `full_name` varchar(50) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `id_level` int(11) DEFAULT NULL,
  `image` varchar(500) DEFAULT NULL,
  `nohp` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `is_active` enum('Y','N') DEFAULT 'Y',
  PRIMARY KEY (`id_user`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_user_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.tbl_user: ~2 rows (approximately)
INSERT INTO `tbl_user` (`id_user`, `username`, `full_name`, `password`, `id_level`, `image`, `nohp`, `email`, `is_active`) VALUES
	(1, 'admin', 'Administrator', '$2y$05$Bl1UXpDrO8843SqKlnGkq.AjnPhDIGAbfKAoVUkqpUAp4um3LtrbW', 1, 'admin.jpg', '08129837323', '<EMAIL>', 'Y'),
	(2, 'demo', 'Demo', '$2y$05$BUWxpFfRoJIMZe7BEnCi0OeMrLKdbX0mIEts.sUkfKxDDDnBjyHoS', 2, NULL, NULL, NULL, 'Y');

-- Dumping structure for table toko_elektronik.tbl_userlevel
CREATE TABLE IF NOT EXISTS `tbl_userlevel` (
  `id_level` int(11) NOT NULL AUTO_INCREMENT,
  `nama_level` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id_level`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.tbl_userlevel: ~2 rows (approximately)
INSERT INTO `tbl_userlevel` (`id_level`, `nama_level`) VALUES
	(1, 'Administrator'),
	(2, 'user');

-- Dumping structure for table toko_elektronik.transaksi_kas
CREATE TABLE IF NOT EXISTS `transaksi_kas` (
  `id_transaksi` int(11) NOT NULL AUTO_INCREMENT,
  `tanggal_transaksi` date NOT NULL,
  `nomor_referensi` varchar(50) NOT NULL,
  `deskripsi` text NOT NULL,
  `kas_masuk` decimal(15,2) DEFAULT 0.00,
  `kas_keluar` decimal(15,2) DEFAULT 0.00,
  `id_bank` int(11) DEFAULT NULL,
  `jenis_transaksi` enum('PENJUALAN','PEMBELIAN','TRANSFER','LAINNYA') NOT NULL,
  `status` enum('PENDING','RECONCILED','CANCELLED') DEFAULT 'PENDING',
  `catatan` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id_transaksi`),
  KEY `idx_tanggal` (`tanggal_transaksi`),
  KEY `idx_referensi` (`nomor_referensi`),
  KEY `idx_bank` (`id_bank`),
  KEY `idx_settlement_search` (`tanggal_transaksi`,`status`,`jenis_transaksi`),
  CONSTRAINT `transaksi_kas_ibfk_1` FOREIGN KEY (`id_bank`) REFERENCES `bank` (`id_bank`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.transaksi_kas: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.transfer_stok
CREATE TABLE IF NOT EXISTS `transfer_stok` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_transfer` varchar(50) NOT NULL,
  `tanggal_transfer` date NOT NULL,
  `gudang_asal_id` int(11) NOT NULL,
  `gudang_tujuan_id` int(11) NOT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `status` enum('draft','dikirim','diterima','batal') DEFAULT 'draft',
  `tanggal_kirim` datetime DEFAULT NULL,
  `tanggal_terima` datetime DEFAULT NULL,
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `ref_barang_keluar` varchar(50) DEFAULT NULL,
  `ref_barang_masuk` varchar(50) DEFAULT NULL,
  `dibuat_oleh` varchar(100) DEFAULT NULL,
  `dikirim_oleh` varchar(100) DEFAULT NULL,
  `diterima_oleh` varchar(100) DEFAULT NULL,
  `dibuat_pada` datetime DEFAULT current_timestamp(),
  `diubah_pada` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_transfer` (`nomor_transfer`),
  KEY `idx_transfer_stok_tanggal` (`tanggal_transfer`),
  KEY `idx_transfer_stok_status` (`status`),
  KEY `idx_transfer_stok_gudang_asal` (`gudang_asal_id`),
  KEY `idx_transfer_stok_gudang_tujuan` (`gudang_tujuan_id`),
  KEY `idx_transfer_stok_nomor` (`nomor_transfer`),
  CONSTRAINT `fk_transfer_gudang_asal` FOREIGN KEY (`gudang_asal_id`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_transfer_gudang_tujuan` FOREIGN KEY (`gudang_tujuan_id`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.transfer_stok: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.transfer_stok_detail
CREATE TABLE IF NOT EXISTS `transfer_stok_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transfer_stok_id` bigint(20) unsigned NOT NULL,
  `barang_id` int(11) NOT NULL,
  `satuan_id` int(11) NOT NULL,
  `qty` decimal(18,2) NOT NULL,
  `keterangan` mediumtext DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_transfer_detail_header` (`transfer_stok_id`),
  KEY `fk_transfer_detail_satuan` (`satuan_id`),
  KEY `idx_transfer_detail_barang` (`barang_id`),
  CONSTRAINT `fk_transfer_detail_barang` FOREIGN KEY (`barang_id`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_transfer_detail_header` FOREIGN KEY (`transfer_stok_id`) REFERENCES `transfer_stok` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_transfer_detail_satuan` FOREIGN KEY (`satuan_id`) REFERENCES `satuan` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table toko_elektronik.transfer_stok_detail: ~0 rows (approximately)

-- Dumping structure for view toko_elektronik.v_aging_hutang_piutang
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_aging_hutang_piutang` (
	`jenis` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_general_ci',
	`id_transaksi` INT(11) NOT NULL,
	`nomor_transaksi` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`partner_nama` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`total_amount` DECIMAL(15,2) NOT NULL,
	`terbayar` DECIMAL(37,2) NULL,
	`outstanding` DECIMAL(38,2) NULL,
	`tanggal_jatuh_tempo` DATE NULL,
	`umur_hari` INT(7) NULL,
	`aging_category` VARCHAR(1) NULL COLLATE 'utf8mb4_general_ci'
);

-- Dumping structure for view toko_elektronik.v_barang_keluar_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_keluar_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`id_barang_keluar` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_pengeluaran` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal` DATE NOT NULL,
	`jenis` ENUM('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`qty_keluar` DECIMAL(12,2) NOT NULL,
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`harga_satuan` DECIMAL(15,2) NULL,
	`total_harga` DECIMAL(15,2) NULL,
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`created_at` TIMESTAMP NOT NULL
);

-- Dumping structure for view toko_elektronik.v_barang_keluar_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_keluar_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_pengeluaran` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal` DATE NOT NULL,
	`jenis` ENUM('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`kode_pelanggan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_pelanggan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL
);

-- Dumping structure for view toko_elektronik.v_barang_keluar_transfer
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_keluar_transfer` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_pengeluaran` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal` DATE NOT NULL,
	`id_pelanggan` INT(11) NULL,
	`jenis` ENUM('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL,
	`kode_pelanggan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_pelanggan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`gudang_asal_id` INT(11) NULL,
	`gudang_tujuan_id` INT(11) NULL,
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_transfer` DATE NULL,
	`status_transfer` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8mb4_unicode_ci'
);

-- Dumping structure for view toko_elektronik.v_barang_masuk_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_masuk_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`id_barang_masuk` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_penerimaan` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal` DATE NOT NULL,
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`jenis` ENUM('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`merk` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tipe` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`qty_diterima` DECIMAL(12,2) NOT NULL,
	`harga_satuan` DECIMAL(15,2) NULL,
	`total_harga` DECIMAL(15,2) NULL,
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`created_at` TIMESTAMP NOT NULL
);

-- Dumping structure for view toko_elektronik.v_barang_masuk_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_masuk_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_penerimaan` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal` DATE NOT NULL,
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`jenis` ENUM('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL
);

-- Dumping structure for view toko_elektronik.v_barang_masuk_transfer
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_masuk_transfer` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_penerimaan` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal` DATE NOT NULL,
	`id_supplier` INT(11) NULL,
	`jenis` ENUM('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL,
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`gudang_asal_id` INT(11) NULL,
	`gudang_tujuan_id` INT(11) NULL,
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_transfer` DATE NULL,
	`status_transfer` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8mb4_unicode_ci'
);

-- Dumping structure for view toko_elektronik.v_kas_bank_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_kas_bank_summary` (
	`tanggal` DATE NULL,
	`nama_bank` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`total_transaksi` BIGINT(21) NOT NULL,
	`total_kas_masuk` DECIMAL(37,2) NULL,
	`total_kas_keluar` DECIMAL(37,2) NULL,
	`total_bank_masuk` DECIMAL(37,2) NULL,
	`total_bank_keluar` DECIMAL(37,2) NULL,
	`selisih` DECIMAL(39,2) NULL,
	`matched_count` DECIMAL(22,0) NULL,
	`unmatched_count` DECIMAL(22,0) NULL
);

-- Dumping structure for view toko_elektronik.v_pembelian_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pembelian_detail` (
	`id` INT(11) NOT NULL,
	`id_pembelian` INT(11) NOT NULL,
	`nomor_pembelian` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_pembelian` DATE NULL,
	`status_pembelian` ENUM('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NULL COLLATE 'utf8mb4_unicode_ci',
	`jenis_pembelian` ENUM('reguler','konsinyasi','kontrak') NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`id_barang` INT(11) NOT NULL,
	`kode_barang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`merk` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tipe` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`id_gudang` INT(11) NOT NULL,
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`qty` DECIMAL(10,2) NOT NULL,
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`harga_satuan` DECIMAL(15,2) NOT NULL,
	`diskon_persen` DECIMAL(5,2) NULL,
	`diskon_nominal` DECIMAL(15,2) NULL,
	`subtotal_sebelum_diskon` DECIMAL(15,2) NOT NULL,
	`subtotal_setelah_diskon` DECIMAL(15,2) NOT NULL,
	`ppn_persen` DECIMAL(5,2) NULL,
	`ppn_nominal` DECIMAL(15,2) NULL,
	`total_akhir` DECIMAL(15,2) NOT NULL,
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci'
);

-- Dumping structure for view toko_elektronik.v_pembelian_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pembelian_summary` (
	`id` INT(11) NOT NULL,
	`nomor_pembelian` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_pembelian` DATE NOT NULL,
	`id_supplier` INT(11) NOT NULL,
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`alamat_supplier` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`telepon_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`email_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`jenis_pembelian` ENUM('reguler','konsinyasi','kontrak') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NOT NULL,
	`total_qty` DECIMAL(10,2) NOT NULL,
	`subtotal` DECIMAL(15,2) NOT NULL,
	`diskon_persen` DECIMAL(5,2) NULL,
	`diskon_nominal` DECIMAL(15,2) NULL,
	`total_sebelum_pajak` DECIMAL(15,2) NOT NULL,
	`ppn_persen` DECIMAL(5,2) NULL,
	`ppn_nominal` DECIMAL(15,2) NULL,
	`total_setelah_pajak` DECIMAL(15,2) NOT NULL,
	`biaya_pengiriman` DECIMAL(15,2) NULL,
	`total_akhir` DECIMAL(15,2) NOT NULL,
	`tanggal_jatuh_tempo` DATE NULL,
	`syarat_pembayaran` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`metode_pembayaran` ENUM('tunai','transfer','kredit','cek','giro') NULL COLLATE 'utf8mb4_unicode_ci',
	`nomor_po_supplier` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`alamat_pengiriman` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL,
	`created_by` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`updated_by` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`approved_by` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`approved_at` DATETIME NULL,
	`total_dibayar` DECIMAL(37,2) NULL,
	`sisa_pembayaran` DECIMAL(38,2) NULL,
	`status_pembayaran` VARCHAR(1) NULL COLLATE 'utf8mb4_general_ci'
);

-- Dumping structure for view toko_elektronik.v_pengiriman_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pengiriman_detail` (
	`id` INT(11) NOT NULL,
	`id_pengiriman` INT(11) NOT NULL,
	`id_barang` INT(11) NOT NULL,
	`id_gudang` INT(11) NOT NULL,
	`qty_dikirim` DECIMAL(10,2) NOT NULL,
	`berat_satuan` DECIMAL(10,2) NULL,
	`total_berat` DECIMAL(10,2) NULL,
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`nomor_pengiriman` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_pengiriman` DATE NULL,
	`status_pengiriman` ENUM('draft','prepared','shipped','in_transit','delivered','returned','cancelled') NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_barang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`merk` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tipe` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_pelanggan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nomor_pesanan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci'
);

-- Dumping structure for view toko_elektronik.v_pengiriman_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pengiriman_summary` (
	`id` INT(11) NOT NULL,
	`nomor_pengiriman` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_pengiriman` DATE NOT NULL,
	`id_pesanan` INT(11) NOT NULL,
	`id_pelanggan` INT(11) NOT NULL,
	`alamat_pengiriman` MEDIUMTEXT NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','prepared','shipped','in_transit','delivered','returned','cancelled') NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NOT NULL,
	`total_qty` DECIMAL(10,2) NOT NULL,
	`total_berat` DECIMAL(10,2) NULL,
	`estimasi_tiba` DATE NULL,
	`tanggal_dikirim` DATETIME NULL,
	`tanggal_diterima` DATETIME NULL,
	`penerima` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL,
	`created_by` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`updated_by` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_pelanggan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_pelanggan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`alamat_pelanggan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`no_telepon` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nomor_pesanan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_pesanan` DATE NULL,
	`total_pesanan` DECIMAL(15,2) NULL,
	`status_pesanan` ENUM('draft','diproses','dikirim','selesai','dibatalkan') NULL COLLATE 'utf8mb4_unicode_ci'
);

-- Dumping structure for view toko_elektronik.v_stok_movement_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_movement_detail` (
	`id` BIGINT(21) NOT NULL,
	`id_barang` INT(11) NOT NULL,
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`id_gudang` INT(11) NOT NULL,
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal` DATE NOT NULL,
	`jenis_transaksi` VARCHAR(1) NOT NULL COLLATE 'cp850_general_ci',
	`nomor_referensi` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`qty_masuk` DECIMAL(12,2) NULL,
	`qty_keluar` DECIMAL(12,2) NULL,
	`keterangan` LONGTEXT NULL COLLATE 'utf8mb4_unicode_ci'
);

-- Dumping structure for view toko_elektronik.v_stok_opname_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_opname_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`id_opname` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_opname` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_opname` DATE NOT NULL,
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`qty_sistem` DECIMAL(12,2) NOT NULL,
	`qty_fisik` DECIMAL(12,2) NOT NULL,
	`selisih` DECIMAL(12,2) NULL,
	`keterangan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`created_at` TIMESTAMP NOT NULL
);

-- Dumping structure for view toko_elektronik.v_stok_opname_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_opname_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_opname` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_opname` DATE NOT NULL,
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NULL,
	`total_selisih_positif` DECIMAL(15,2) NULL,
	`total_selisih_negatif` DECIMAL(15,2) NULL,
	`keterangan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`user_input` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`user_final` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_final` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL
);

-- Dumping structure for view toko_elektronik.v_stok_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_summary` (
	`id_barang` INT(11) NOT NULL,
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`id_gudang` INT(11) NOT NULL,
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`qty_terakhir` DECIMAL(12,2) NOT NULL,
	`updated_at` TIMESTAMP NOT NULL
);

-- Dumping structure for view toko_elektronik.v_transfer_stok_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_transfer_stok_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`transfer_stok_id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_transfer` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_transfer` DATE NULL,
	`status` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8mb4_unicode_ci',
	`barang_id` INT(11) NOT NULL,
	`kode_barang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_barang` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`qty` DECIMAL(18,2) NOT NULL,
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci'
);

-- Dumping structure for view toko_elektronik.v_transfer_stok_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_transfer_stok_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_transfer` VARCHAR(1) NOT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_transfer` DATE NOT NULL,
	`status` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8mb4_unicode_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`kode_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`kode_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`keterangan` MEDIUMTEXT NULL COLLATE 'utf8mb4_unicode_ci',
	`tanggal_kirim` DATETIME NULL,
	`tanggal_terima` DATETIME NULL,
	`dibuat_oleh` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`dikirim_oleh` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`diterima_oleh` VARCHAR(1) NULL COLLATE 'utf8mb4_unicode_ci',
	`dibuat_pada` DATETIME NULL
);

-- Dumping structure for trigger toko_elektronik.trg_after_delete_retur_detail
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER IF NOT EXISTS trg_after_delete_retur_detail
AFTER DELETE ON retur_penjualan_detail
FOR EACH ROW
BEGIN
  UPDATE retur_penjualan
  SET total_item = (SELECT COUNT(*) FROM retur_penjualan_detail WHERE id_retur = OLD.id_retur),
      total_qty = (SELECT COALESCE(SUM(qty_retur), 0) FROM retur_penjualan_detail WHERE id_retur = OLD.id_retur),
      total_nilai = (SELECT COALESCE(SUM(total_harga), 0) FROM retur_penjualan_detail WHERE id_retur = OLD.id_retur),
      updated_at = NOW()
  WHERE id = OLD.id_retur;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_after_insert_retur_detail
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER IF NOT EXISTS trg_after_insert_retur_detail
AFTER INSERT ON retur_penjualan_detail
FOR EACH ROW
BEGIN
  UPDATE retur_penjualan
  SET total_item = (SELECT COUNT(*) FROM retur_penjualan_detail WHERE id_retur = NEW.id_retur),
      total_qty = (SELECT COALESCE(SUM(qty_retur), 0) FROM retur_penjualan_detail WHERE id_retur = NEW.id_retur),
      total_nilai = (SELECT COALESCE(SUM(total_harga), 0) FROM retur_penjualan_detail WHERE id_retur = NEW.id_retur),
      updated_at = NOW()
  WHERE id = NEW.id_retur;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_after_update_retur_detail
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER IF NOT EXISTS trg_after_update_retur_detail
AFTER UPDATE ON retur_penjualan_detail
FOR EACH ROW
BEGIN
  UPDATE retur_penjualan
  SET total_item = (SELECT COUNT(*) FROM retur_penjualan_detail WHERE id_retur = NEW.id_retur),
      total_qty = (SELECT COALESCE(SUM(qty_retur), 0) FROM retur_penjualan_detail WHERE id_retur = NEW.id_retur),
      total_nilai = (SELECT COALESCE(SUM(total_harga), 0) FROM retur_penjualan_detail WHERE id_retur = NEW.id_retur),
      updated_at = NOW()
  WHERE id = NEW.id_retur;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_finalize_barang_keluar
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_finalize_barang_keluar` AFTER UPDATE ON `barang_keluar` FOR EACH ROW BEGIN
    
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT
            CONCAT(NEW.tanggal, ' ', TIME(NOW())),
            bkd.id_barang,
            bkd.id_gudang,
            CASE NEW.jenis
                WHEN 'penjualan' THEN 'penjualan'
                WHEN 'retur_pembelian' THEN 'retur_beli'
                WHEN 'transfer_keluar' THEN 'transfer_keluar'
                ELSE 'penyesuaian'
            END,
            0, 
            bkd.qty_keluar, 
            CONCAT('Barang Keluar: ', COALESCE(bkd.keterangan, NEW.keterangan, '')),
            NEW.nomor_pengeluaran,
            CONCAT('user_', COALESCE(NEW.finalized_by, NEW.created_by, 0))
        FROM barang_keluar_detail bkd
        WHERE bkd.id_barang_keluar = NEW.id;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_finalize_barang_masuk
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_finalize_barang_masuk` AFTER UPDATE ON `barang_masuk` FOR EACH ROW BEGIN
    -- Hanya jalankan jika status berubah dari draft ke final
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        -- Insert semua detail barang masuk ke stok_movement
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT 
            CONCAT(NEW.tanggal, ' ', TIME(NOW())),
            bmd.id_barang,
            bmd.id_gudang,
            CASE 
                WHEN NEW.jenis = 'pembelian' THEN 'pembelian'
                WHEN NEW.jenis = 'retur_penjualan' THEN 'retur_jual'
                WHEN NEW.jenis = 'transfer_masuk' THEN 'transfer_masuk'
                ELSE 'pembelian'
            END,
            bmd.qty_diterima,
            0,
            CONCAT('Barang Masuk - ', NEW.jenis, ': ', COALESCE(bmd.keterangan, NEW.keterangan)),
            NEW.nomor_penerimaan,
            CONCAT('user_', COALESCE(NEW.finalized_by, NEW.created_by))
        FROM barang_masuk_detail bmd
        WHERE bmd.id_barang_masuk = NEW.id
        AND bmd.qty_diterima > 0;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_finalize_opname
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_finalize_opname` AFTER UPDATE ON `stok_opname` FOR EACH ROW BEGIN
    
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT 
            CONCAT(NEW.tanggal_opname, ' ', TIME(NOW())),
            sod.id_barang,
            NEW.id_gudang,
            'opname',
            CASE WHEN sod.selisih > 0 THEN sod.selisih ELSE 0 END,
            CASE WHEN sod.selisih < 0 THEN ABS(sod.selisih) ELSE 0 END,
            CONCAT('Stock Opname: ', COALESCE(sod.keterangan, NEW.keterangan)),
            NEW.nomor_opname,
            NEW.user_final
        FROM stok_opname_detail sod
        WHERE sod.id_opname = NEW.id
        AND sod.selisih != 0; 
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_pembelian_status_tracking
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_pembelian_status_tracking` AFTER UPDATE ON `pembelian` FOR EACH ROW
BEGIN
    -- Jika status berubah, catat di tracking
    IF OLD.status != NEW.status THEN
        INSERT INTO pembelian_tracking (
            id_pembelian, 
            status, 
            tanggal_status, 
            keterangan, 
            created_by
        ) VALUES (
            NEW.id,
            NEW.status,
            NOW(),
            CONCAT('Status berubah dari ', OLD.status, ' menjadi ', NEW.status),
            NEW.updated_by
        );
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_penyesuaian_to_movement
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_penyesuaian_to_movement` AFTER INSERT ON `penyesuaian_stok` FOR EACH ROW BEGIN
    DECLARE qty_in_val DECIMAL(12,2) DEFAULT 0;
    DECLARE qty_out_val DECIMAL(12,2) DEFAULT 0;
    
    
    IF NEW.jenis_penyesuaian = 'PENAMBAHAN' THEN
        SET qty_in_val = NEW.qty_selisih;
        SET qty_out_val = 0;
    ELSE
        SET qty_in_val = 0;
        SET qty_out_val = ABS(NEW.qty_selisih);
    END IF;
    
    
    INSERT INTO stok_movement (
        tanggal, id_barang, id_gudang, tipe_transaksi,
        qty_in, qty_out, keterangan, ref_transaksi, user_input
    ) VALUES (
        CONCAT(NEW.tanggal, ' ', TIME(NOW())), 
        NEW.id_barang, 
        NEW.id_gudang, 
        'penyesuaian',
        qty_in_val,
        qty_out_val,
        NEW.alasan,
        NEW.kode_penyesuaian,
        CONCAT('user_', COALESCE(NEW.user_id, 0))
    );
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_transfer_kirim
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_transfer_kirim` AFTER UPDATE ON `transfer_stok` FOR EACH ROW 
BEGIN
    DECLARE nomor_keluar VARCHAR(50);
    DECLARE user_id INT DEFAULT 1;
    DECLARE detail_count INT DEFAULT 0;
    DECLARE barang_keluar_id BIGINT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- Log error jika terjadi masalah
        INSERT INTO error_log (table_name, error_message, created_at) 
        VALUES ('transfer_stok', CONCAT('Error in trg_transfer_kirim for ID: ', NEW.id), NOW())
        ON DUPLICATE KEY UPDATE error_message = VALUES(error_message);
        RESIGNAL;
    END;

    -- Hanya jalankan jika status berubah dari draft ke dikirim
    IF OLD.status = 'draft' AND NEW.status = 'dikirim' THEN
        -- Cek apakah ada detail transfer
        SELECT COUNT(*) INTO detail_count 
        FROM transfer_stok_detail 
        WHERE transfer_stok_id = NEW.id;
        
        IF detail_count > 0 THEN
            -- Generate nomor barang keluar
            SET nomor_keluar = CONCAT('TK-', DATE_FORMAT(NEW.tanggal_kirim, '%Y%m%d'), '-', LPAD(NEW.id, 4, '0'));

            -- Cari user ID berdasarkan nama yang mengirim
            SELECT id_user INTO user_id 
            FROM tbl_user 
            WHERE full_name = NEW.dikirim_oleh OR username = NEW.dikirim_oleh 
            LIMIT 1;
            
            IF user_id IS NULL THEN
                SET user_id = 1; 
            END IF;

            -- Insert barang keluar header dengan status final
            INSERT INTO barang_keluar (
                nomor_pengeluaran, tanggal, jenis, ref_nomor, keterangan,
                status, total_item, total_qty, created_by, finalized_by, finalized_at
            ) VALUES (
                nomor_keluar,
                DATE(NEW.tanggal_kirim),
                'transfer_keluar',
                NEW.nomor_transfer,
                CONCAT('Transfer ke ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_tujuan_id)),
                'final',
                NEW.total_item,
                NEW.total_qty,
                user_id,
                user_id,
                NEW.tanggal_kirim
            );

            -- Ambil ID barang keluar yang baru dibuat
            SET barang_keluar_id = LAST_INSERT_ID();

            -- Insert detail barang keluar
            INSERT INTO barang_keluar_detail (
                id_barang_keluar, id_barang, id_gudang, qty_keluar, id_satuan, keterangan
            )
            SELECT
                barang_keluar_id,
                tsd.barang_id,
                NEW.gudang_asal_id,
                tsd.qty,
                tsd.satuan_id,
                CONCAT('Transfer ke gudang ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_tujuan_id), ' - ', COALESCE(tsd.keterangan, ''))
            FROM transfer_stok_detail tsd
            WHERE tsd.transfer_stok_id = NEW.id
            AND tsd.qty > 0;
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_transfer_terima
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_transfer_terima` AFTER UPDATE ON `transfer_stok` FOR EACH ROW 
BEGIN
    DECLARE nomor_masuk VARCHAR(50);
    DECLARE user_id INT DEFAULT 1;
    DECLARE detail_count INT DEFAULT 0;
    DECLARE barang_masuk_id BIGINT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- Log error jika terjadi masalah
        INSERT INTO error_log (table_name, error_message, created_at) 
        VALUES ('transfer_stok', CONCAT('Error in trg_transfer_terima for ID: ', NEW.id), NOW())
        ON DUPLICATE KEY UPDATE error_message = VALUES(error_message);
        RESIGNAL;
    END;

    -- Hanya jalankan jika status berubah dari dikirim ke diterima
    IF OLD.status = 'dikirim' AND NEW.status = 'diterima' THEN
        -- Cek apakah ada detail transfer
        SELECT COUNT(*) INTO detail_count 
        FROM transfer_stok_detail 
        WHERE transfer_stok_id = NEW.id;
        
        IF detail_count > 0 THEN
            -- Generate nomor barang masuk
            SET nomor_masuk = CONCAT('TM-', DATE_FORMAT(NEW.tanggal_terima, '%Y%m%d'), '-', LPAD(NEW.id, 4, '0'));

            -- Cari user ID berdasarkan nama yang diterima
            SELECT id_user INTO user_id 
            FROM tbl_user 
            WHERE full_name = NEW.diterima_oleh OR username = NEW.diterima_oleh 
            LIMIT 1;
            
            IF user_id IS NULL THEN
                SET user_id = 1; 
            END IF;

            -- Insert barang masuk header dengan status final
            INSERT INTO barang_masuk (
                nomor_penerimaan, tanggal, jenis, ref_nomor, keterangan,
                status, total_item, total_qty, created_by, finalized_by, finalized_at
            ) VALUES (
                nomor_masuk,
                DATE(NEW.tanggal_terima),
                'transfer_masuk',
                NEW.nomor_transfer,
                CONCAT('Transfer dari ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_asal_id)),
                'final',
                NEW.total_item,
                NEW.total_qty,
                user_id,
                user_id,
                NEW.tanggal_terima
            );

            -- Ambil ID barang masuk yang baru dibuat
            SET barang_masuk_id = LAST_INSERT_ID();

            -- Insert detail barang masuk
            INSERT INTO barang_masuk_detail (
                id_barang_masuk, id_barang, id_gudang, qty_diterima, id_satuan, keterangan
            )
            SELECT
                barang_masuk_id,
                tsd.barang_id,
                NEW.gudang_tujuan_id,
                tsd.qty,
                tsd.satuan_id,
                CONCAT('Transfer dari gudang ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_asal_id), ' - ', COALESCE(tsd.keterangan, ''))
            FROM transfer_stok_detail tsd
            WHERE tsd.transfer_stok_id = NEW.id
            AND tsd.qty > 0;
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_keluar_summary_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_keluar_summary_delete` AFTER DELETE ON `barang_keluar_detail` FOR EACH ROW BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = OLD.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = OLD.id_barang_keluar
        )
    WHERE id = OLD.id_barang_keluar;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_keluar_summary_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_keluar_summary_insert` AFTER INSERT ON `barang_keluar_detail` FOR EACH ROW BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        )
    WHERE id = NEW.id_barang_keluar;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_keluar_summary_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_keluar_summary_update` AFTER UPDATE ON `barang_keluar_detail` FOR EACH ROW BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        )
    WHERE id = NEW.id_barang_keluar;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_masuk_summary_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_masuk_summary_delete` AFTER DELETE ON `barang_masuk_detail` FOR EACH ROW BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = OLD.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = OLD.id_barang_masuk
        )
    WHERE id = OLD.id_barang_masuk;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_masuk_summary_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_masuk_summary_insert` AFTER INSERT ON `barang_masuk_detail` FOR EACH ROW BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        )
    WHERE id = NEW.id_barang_masuk;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_masuk_summary_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_masuk_summary_update` AFTER UPDATE ON `barang_masuk_detail` FOR EACH ROW BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        )
    WHERE id = NEW.id_barang_masuk;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_opname_summary_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_opname_summary_delete` AFTER DELETE ON `stok_opname_detail` FOR EACH ROW BEGIN
    UPDATE stok_opname SET
        total_item = (
            SELECT COUNT(*) FROM stok_opname_detail 
            WHERE id_opname = OLD.id_opname
        ),
        total_selisih_positif = (
            SELECT COALESCE(SUM(selisih), 0) FROM stok_opname_detail 
            WHERE id_opname = OLD.id_opname AND selisih > 0
        ),
        total_selisih_negatif = (
            SELECT COALESCE(ABS(SUM(selisih)), 0) FROM stok_opname_detail 
            WHERE id_opname = OLD.id_opname AND selisih < 0
        )
    WHERE id = OLD.id_opname;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_opname_summary_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_opname_summary_insert` AFTER INSERT ON `stok_opname_detail` FOR EACH ROW BEGIN
    UPDATE stok_opname SET
        total_item = (
            SELECT COUNT(*) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname
        ),
        total_selisih_positif = (
            SELECT COALESCE(SUM(selisih), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih > 0
        ),
        total_selisih_negatif = (
            SELECT COALESCE(ABS(SUM(selisih)), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih < 0
        )
    WHERE id = NEW.id_opname;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_opname_summary_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_opname_summary_update` AFTER UPDATE ON `stok_opname_detail` FOR EACH ROW BEGIN
    UPDATE stok_opname SET
        total_item = (
            SELECT COUNT(*) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname
        ),
        total_selisih_positif = (
            SELECT COALESCE(SUM(selisih), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih > 0
        ),
        total_selisih_negatif = (
            SELECT COALESCE(ABS(SUM(selisih)), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih < 0
        )
    WHERE id = NEW.id_opname;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pembelian_totals_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pembelian_totals_delete` AFTER DELETE ON `pembelian_detail` FOR EACH ROW
BEGIN
    CALL sp_update_pembelian_totals(OLD.id_pembelian);
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pembelian_totals_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pembelian_totals_insert` AFTER INSERT ON `pembelian_detail` FOR EACH ROW
BEGIN
    CALL sp_update_pembelian_totals(NEW.id_pembelian);
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pembelian_totals_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pembelian_totals_update` AFTER UPDATE ON `pembelian_detail` FOR EACH ROW
BEGIN
    CALL sp_update_pembelian_totals(NEW.id_pembelian);
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pengiriman_totals_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pengiriman_totals_delete` 
AFTER DELETE ON `pengiriman_detail` 
FOR EACH ROW 
BEGIN
    UPDATE pengiriman 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = OLD.id_pengiriman
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_dikirim), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = OLD.id_pengiriman
        ),
        total_berat = (
            SELECT COALESCE(SUM(total_berat), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = OLD.id_pengiriman
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.id_pengiriman;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pengiriman_totals_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pengiriman_totals_insert` 
AFTER INSERT ON `pengiriman_detail` 
FOR EACH ROW 
BEGIN
    UPDATE pengiriman 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_dikirim), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_berat = (
            SELECT COALESCE(SUM(total_berat), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id_pengiriman;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pengiriman_totals_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pengiriman_totals_update` 
AFTER UPDATE ON `pengiriman_detail` 
FOR EACH ROW 
BEGIN
    UPDATE pengiriman 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_dikirim), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        total_berat = (
            SELECT COALESCE(SUM(total_berat), 0) 
            FROM pengiriman_detail 
            WHERE id_pengiriman = NEW.id_pengiriman
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id_pengiriman;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_stok_barang
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_stok_barang` AFTER INSERT ON `stok_movement` FOR EACH ROW BEGIN
    DECLARE total_qty DECIMAL(12,2);

    -- Hitung total_qty_in dan total_qty_out untuk id_barang dan id_gudang yang bersangkutan
    SELECT SUM(qty_in), SUM(qty_out)
    INTO @total_in, @total_out
    FROM stok_movement
    WHERE id_barang = NEW.id_barang AND id_gudang = NEW.id_gudang;

    -- Hitung stok terakhir
    SET total_qty = @total_in - @total_out;

    -- Perbarui atau masukkan data ke tabel stok_barang
    IF EXISTS (
        SELECT 1
        FROM stok_barang
        WHERE id_barang = NEW.id_barang AND id_gudang = NEW.id_gudang
    ) THEN
        UPDATE stok_barang
        SET qty_terakhir = total_qty
        WHERE id_barang = NEW.id_barang AND id_gudang = NEW.id_gudang;
    ELSE
        INSERT INTO stok_barang (id_barang, id_gudang, qty_terakhir)
        VALUES (NEW.id_barang, NEW.id_gudang, total_qty);
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_transfer_totals_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_transfer_totals_delete` AFTER DELETE ON `transfer_stok_detail` FOR EACH ROW BEGIN
    UPDATE transfer_stok
    SET
        total_item = (
            SELECT COUNT(*)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = OLD.transfer_stok_id
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty), 0)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = OLD.transfer_stok_id
        )
    WHERE id = OLD.transfer_stok_id;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_transfer_totals_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_transfer_totals_insert` AFTER INSERT ON `transfer_stok_detail` FOR EACH ROW BEGIN
    UPDATE transfer_stok
    SET
        total_item = (
            SELECT COUNT(*)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty), 0)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        )
    WHERE id = NEW.transfer_stok_id;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_transfer_totals_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_transfer_totals_update` AFTER UPDATE ON `transfer_stok_detail` FOR EACH ROW BEGIN
    UPDATE transfer_stok
    SET
        total_item = (
            SELECT COUNT(*)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty), 0)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        )
    WHERE id = NEW.transfer_stok_id;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_validate_transfer_keluar_ref
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_validate_transfer_keluar_ref` BEFORE INSERT ON `barang_keluar` FOR EACH ROW BEGIN
    
    IF NEW.jenis = 'transfer_keluar' THEN
        IF NEW.ref_nomor IS NULL OR NEW.ref_nomor NOT LIKE 'TR-%' THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Transfer keluar hanya bisa dibuat melalui modul Transfer Stok';
        END IF;
        
        
        
        IF NOT EXISTS (
            SELECT 1 FROM transfer_stok 
            WHERE nomor_transfer = NEW.ref_nomor 
            AND status IN ('draft', 'dikirim')
        ) THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Nomor transfer tidak valid atau tidak dalam status yang sesuai';
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_validate_transfer_masuk_ref
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_validate_transfer_masuk_ref` BEFORE INSERT ON `barang_masuk` FOR EACH ROW BEGIN
    -- Jika jenis adalah transfer_masuk, pastikan ref_nomor dimulai dengan 'TR-'
    IF NEW.jenis = 'transfer_masuk' THEN
        IF NEW.ref_nomor IS NULL OR NEW.ref_nomor NOT LIKE 'TR-%' THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Transfer masuk hanya bisa dibuat melalui modul Transfer Stok';
        END IF;
        
        -- Pastikan nomor transfer ada di tabel transfer_stok
        IF NOT EXISTS (
            SELECT 1 FROM transfer_stok 
            WHERE nomor_transfer = NEW.ref_nomor 
            -- AND status = 'dikirim'
            AND status = 'diterima'
        ) THEN
            SIGNAL SQLSTATE '45000' 
            -- SET MESSAGE_TEXT = 'Nomor transfer tidak valid atau belum dalam status dikirim';
            SET MESSAGE_TEXT = 'Nomor transfer tidak valid atau statusnya bukan ''diterima''.'; -- Updated message
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.update_penerimaan_pembelian_totals
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `update_penerimaan_pembelian_totals` AFTER INSERT ON `penerimaan_pembelian_detail` FOR EACH ROW
BEGIN
    UPDATE penerimaan_pembelian
    SET total_item = (
            SELECT COUNT(DISTINCT id_barang)
            FROM penerimaan_pembelian_detail
            WHERE id_penerimaan = NEW.id_penerimaan
        ),
        total_qty = (
            SELECT SUM(qty_diterima)
            FROM penerimaan_pembelian_detail
            WHERE id_penerimaan = NEW.id_penerimaan
        )
    WHERE id = NEW.id_penerimaan;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.update_penerimaan_pembelian_totals_after_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `update_penerimaan_pembelian_totals_after_delete` AFTER DELETE ON `penerimaan_pembelian_detail` FOR EACH ROW
BEGIN
    UPDATE penerimaan_pembelian
    SET total_item = (
            SELECT COUNT(DISTINCT id_barang)
            FROM penerimaan_pembelian_detail
            WHERE id_penerimaan = OLD.id_penerimaan
        ),
        total_qty = (
            SELECT SUM(qty_diterima)
            FROM penerimaan_pembelian_detail
            WHERE id_penerimaan = OLD.id_penerimaan
        )
    WHERE id = OLD.id_penerimaan;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.update_penerimaan_pembelian_totals_after_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `update_penerimaan_pembelian_totals_after_update` AFTER UPDATE ON `penerimaan_pembelian_detail` FOR EACH ROW
BEGIN
    UPDATE penerimaan_pembelian
    SET total_item = (
            SELECT COUNT(DISTINCT id_barang)
            FROM penerimaan_pembelian_detail
            WHERE id_penerimaan = NEW.id_penerimaan
        ),
        total_qty = (
            SELECT SUM(qty_diterima)
            FROM penerimaan_pembelian_detail
            WHERE id_penerimaan = NEW.id_penerimaan
        )
    WHERE id = NEW.id_penerimaan;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_aging_hutang_piutang`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_aging_hutang_piutang` AS SELECT 
    'PIUTANG' as jenis,
    fp.id as id_transaksi,
    fp.nomor_faktur as nomor_transaksi,
    p.nama as partner_nama,
    fp.total_faktur as total_amount,
    COALESCE(pembayaran.total_bayar, 0) as terbayar,
    (fp.total_faktur - COALESCE(pembayaran.total_bayar, 0)) as outstanding,
    fp.tanggal_jatuh_tempo,
    DATEDIFF(CURDATE(), fp.tanggal_jatuh_tempo) as umur_hari,
    CASE 
        WHEN DATEDIFF(CURDATE(), fp.tanggal_jatuh_tempo) <= 0 THEN 'CURRENT'
        WHEN DATEDIFF(CURDATE(), fp.tanggal_jatuh_tempo) BETWEEN 1 AND 30 THEN '1-30 HARI'
        WHEN DATEDIFF(CURDATE(), fp.tanggal_jatuh_tempo) BETWEEN 31 AND 60 THEN '31-60 HARI'
        WHEN DATEDIFF(CURDATE(), fp.tanggal_jatuh_tempo) BETWEEN 61 AND 90 THEN '61-90 HARI'
        ELSE '>90 HARI'
    END as aging_category
FROM faktur_penjualan fp
LEFT JOIN pelanggan p ON fp.id_pelanggan = p.id
LEFT JOIN (
    SELECT id_faktur_penjualan, SUM(jumlah_pembayaran) as total_bayar
    FROM faktur_penjualan_pembayaran
    GROUP BY id_faktur_penjualan
) pembayaran ON fp.id = pembayaran.id_faktur_penjualan
WHERE (fp.total_faktur - COALESCE(pembayaran.total_bayar, 0)) > 0

UNION ALL

SELECT 
    'HUTANG' as jenis,
    pb.id as id_transaksi,
    pb.nomor_pembelian as nomor_transaksi,
    s.nama as partner_nama,
    pb.total_akhir as total_amount,
    COALESCE(pembayaran_beli.total_bayar, 0) as terbayar,
    (pb.total_akhir - COALESCE(pembayaran_beli.total_bayar, 0)) as outstanding,
    pb.tanggal_jatuh_tempo,
    DATEDIFF(CURDATE(), pb.tanggal_jatuh_tempo) as umur_hari,
    CASE 
        WHEN DATEDIFF(CURDATE(), pb.tanggal_jatuh_tempo) <= 0 THEN 'CURRENT'
        WHEN DATEDIFF(CURDATE(), pb.tanggal_jatuh_tempo) BETWEEN 1 AND 30 THEN '1-30 HARI'
        WHEN DATEDIFF(CURDATE(), pb.tanggal_jatuh_tempo) BETWEEN 31 AND 60 THEN '31-60 HARI'
        WHEN DATEDIFF(CURDATE(), pb.tanggal_jatuh_tempo) BETWEEN 61 AND 90 THEN '61-90 HARI'
        ELSE '>90 HARI'
    END as aging_category
FROM pembelian pb
LEFT JOIN supplier s ON pb.id_supplier = s.id
LEFT JOIN (
    SELECT id_pembelian, SUM(jumlah_bayar) as total_bayar
    FROM pembelian_pembayaran
    GROUP BY id_pembelian
) pembayaran_beli ON pb.id = pembayaran_beli.id_pembelian
WHERE (pb.total_akhir - COALESCE(pembayaran_beli.total_bayar, 0)) > 0 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_keluar_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_keluar_detail` AS SELECT `bkd`.`id` AS `id`, `bkd`.`id_barang_keluar` AS `id_barang_keluar`, `bk`.`nomor_pengeluaran` AS `nomor_pengeluaran`, `bk`.`tanggal` AS `tanggal`, `bk`.`jenis` AS `jenis`, `bk`.`status` AS `status`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `bkd`.`qty_keluar` AS `qty_keluar`, `s`.`kode_satuan` AS `kode_satuan`, `s`.`nama_satuan` AS `nama_satuan`, `bkd`.`harga_satuan` AS `harga_satuan`, `bkd`.`total_harga` AS `total_harga`, `bkd`.`keterangan` AS `keterangan`, `bkd`.`created_at` AS `created_at` FROM ((((`barang_keluar_detail` `bkd` join `barang_keluar` `bk` on(`bkd`.`id_barang_keluar` = `bk`.`id`)) join `barang` `b` on(`bkd`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`bkd`.`id_gudang` = `g`.`id`)) left join `satuan` `s` on(`bkd`.`id_satuan` = `s`.`id`)) ORDER BY `bk`.`tanggal` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_keluar_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_keluar_summary` AS SELECT `bk`.`id` AS `id`, `bk`.`nomor_pengeluaran` AS `nomor_pengeluaran`, `bk`.`tanggal` AS `tanggal`, `bk`.`jenis` AS `jenis`, `bk`.`ref_nomor` AS `ref_nomor`, `bk`.`status` AS `status`, `bk`.`total_item` AS `total_item`, `bk`.`total_qty` AS `total_qty`, `p`.`kode` AS `kode_pelanggan`, `p`.`nama` AS `nama_pelanggan`, `bk`.`keterangan` AS `keterangan`, `bk`.`created_by` AS `created_by`, `bk`.`finalized_by` AS `finalized_by`, `bk`.`finalized_at` AS `finalized_at`, `bk`.`created_at` AS `created_at`, `bk`.`updated_at` AS `updated_at` FROM (`barang_keluar` `bk` left join `pelanggan` `p` on(`bk`.`id_pelanggan` = `p`.`id`)) ORDER BY `bk`.`tanggal` DESC, `bk`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_keluar_transfer`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_keluar_transfer` AS SELECT `bk`.`id` AS `id`, `bk`.`nomor_pengeluaran` AS `nomor_pengeluaran`, `bk`.`tanggal` AS `tanggal`, `bk`.`id_pelanggan` AS `id_pelanggan`, `bk`.`jenis` AS `jenis`, `bk`.`ref_nomor` AS `ref_nomor`, `bk`.`keterangan` AS `keterangan`, `bk`.`status` AS `status`, `bk`.`total_item` AS `total_item`, `bk`.`total_qty` AS `total_qty`, `bk`.`created_by` AS `created_by`, `bk`.`finalized_by` AS `finalized_by`, `bk`.`finalized_at` AS `finalized_at`, `bk`.`created_at` AS `created_at`, `bk`.`updated_at` AS `updated_at`, `p`.`kode` AS `kode_pelanggan`, `p`.`nama` AS `nama_pelanggan`, `ts`.`gudang_asal_id` AS `gudang_asal_id`, `ts`.`gudang_tujuan_id` AS `gudang_tujuan_id`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status_transfer` FROM ((((`barang_keluar` `bk` left join `pelanggan` `p` on(`bk`.`id_pelanggan` = `p`.`id`)) left join `transfer_stok` `ts` on(`bk`.`ref_nomor` = `ts`.`nomor_transfer`)) left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) WHERE `bk`.`jenis` = 'transfer_keluar' ORDER BY `bk`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_masuk_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_masuk_detail` AS SELECT `bmd`.`id` AS `id`, `bmd`.`id_barang_masuk` AS `id_barang_masuk`, `bm`.`nomor_penerimaan` AS `nomor_penerimaan`, `bm`.`tanggal` AS `tanggal`, `bm`.`status` AS `status`, `bm`.`jenis` AS `jenis`, `s`.`kode` AS `kode_supplier`, `s`.`nama` AS `nama_supplier`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `b`.`merk` AS `merk`, `b`.`tipe` AS `tipe`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `sat`.`kode_satuan` AS `kode_satuan`, `sat`.`nama_satuan` AS `nama_satuan`, `bmd`.`qty_diterima` AS `qty_diterima`, `bmd`.`harga_satuan` AS `harga_satuan`, `bmd`.`total_harga` AS `total_harga`, `bmd`.`keterangan` AS `keterangan`, `bmd`.`created_at` AS `created_at` FROM (((((`barang_masuk_detail` `bmd` join `barang_masuk` `bm` on(`bmd`.`id_barang_masuk` = `bm`.`id`)) left join `supplier` `s` on(`bm`.`id_supplier` = `s`.`id`)) join `barang` `b` on(`bmd`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`bmd`.`id_gudang` = `g`.`id`)) left join `satuan` `sat` on(`bmd`.`id_satuan` = `sat`.`id`)) ORDER BY `bm`.`tanggal` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_masuk_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_masuk_summary` AS SELECT `bm`.`id` AS `id`, `bm`.`nomor_penerimaan` AS `nomor_penerimaan`, `bm`.`tanggal` AS `tanggal`, `s`.`kode` AS `kode_supplier`, `s`.`nama` AS `nama_supplier`, `bm`.`jenis` AS `jenis`, `bm`.`ref_nomor` AS `ref_nomor`, `bm`.`status` AS `status`, `bm`.`total_item` AS `total_item`, `bm`.`total_qty` AS `total_qty`, `bm`.`keterangan` AS `keterangan`, `bm`.`created_by` AS `created_by`, `bm`.`finalized_by` AS `finalized_by`, `bm`.`finalized_at` AS `finalized_at`, `bm`.`created_at` AS `created_at` FROM (`barang_masuk` `bm` left join `supplier` `s` on(`bm`.`id_supplier` = `s`.`id`)) ORDER BY `bm`.`tanggal` DESC, `bm`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_masuk_transfer`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_masuk_transfer` AS SELECT `bm`.`id` AS `id`, `bm`.`nomor_penerimaan` AS `nomor_penerimaan`, `bm`.`tanggal` AS `tanggal`, `bm`.`id_supplier` AS `id_supplier`, `bm`.`jenis` AS `jenis`, `bm`.`ref_nomor` AS `ref_nomor`, `bm`.`keterangan` AS `keterangan`, `bm`.`status` AS `status`, `bm`.`total_item` AS `total_item`, `bm`.`total_qty` AS `total_qty`, `bm`.`created_by` AS `created_by`, `bm`.`finalized_by` AS `finalized_by`, `bm`.`finalized_at` AS `finalized_at`, `bm`.`created_at` AS `created_at`, `bm`.`updated_at` AS `updated_at`, `s`.`kode` AS `kode_supplier`, `s`.`nama` AS `nama_supplier`, `ts`.`gudang_asal_id` AS `gudang_asal_id`, `ts`.`gudang_tujuan_id` AS `gudang_tujuan_id`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status_transfer` FROM ((((`barang_masuk` `bm` left join `supplier` `s` on(`bm`.`id_supplier` = `s`.`id`)) left join `transfer_stok` `ts` on(`bm`.`ref_nomor` = `ts`.`nomor_transfer`)) left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) WHERE `bm`.`jenis` = 'transfer_masuk' ORDER BY `bm`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_kas_bank_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_kas_bank_summary` AS SELECT 
    DATE(tk.tanggal_transaksi) as tanggal,
    b.nama_bank,
    COUNT(*) as total_transaksi,
    SUM(tk.kas_masuk) as total_kas_masuk,
    SUM(tk.kas_keluar) as total_kas_keluar,
    SUM(COALESCE(bs.bank_masuk, 0)) as total_bank_masuk,
    SUM(COALESCE(bs.bank_keluar, 0)) as total_bank_keluar,
    SUM((tk.kas_masuk - tk.kas_keluar) - (COALESCE(bs.bank_masuk, 0) - COALESCE(bs.bank_keluar, 0))) as selisih,
    SUM(CASE WHEN bs.status_reconcile = 'MATCHED' THEN 1 ELSE 0 END) as matched_count,
    SUM(CASE WHEN bs.status_reconcile = 'UNMATCHED' THEN 1 ELSE 0 END) as unmatched_count
FROM transaksi_kas tk
LEFT JOIN bank_statement bs ON tk.nomor_referensi = bs.nomor_referensi AND tk.tanggal_transaksi = bs.tanggal_transaksi
LEFT JOIN bank b ON tk.id_bank = b.id_bank
GROUP BY DATE(tk.tanggal_transaksi), b.id_bank 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pembelian_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pembelian_detail` AS SELECT 
    pd.id,
    pd.id_pembelian,
    p.nomor_pembelian,
    p.tanggal_pembelian,
    p.status as status_pembelian,
    p.jenis_pembelian,
    s.kode as kode_supplier,
    s.nama as nama_supplier,
    pd.id_barang,
    b.kode_barang,
    b.nama_barang,
    b.merk,
    b.tipe,
    pd.id_gudang,
    g.kode_gudang,
    g.nama_gudang,
    pd.qty,
    sat.kode_satuan,
    sat.nama_satuan,
    pd.harga_satuan,
    pd.diskon_persen,
    pd.diskon_nominal,
    pd.subtotal_sebelum_diskon,
    pd.subtotal_setelah_diskon,
    pd.ppn_persen,
    pd.ppn_nominal,
    pd.total_akhir,
    pd.keterangan
FROM pembelian_detail pd
LEFT JOIN pembelian p ON pd.id_pembelian = p.id
LEFT JOIN supplier s ON p.id_supplier = s.id
LEFT JOIN barang b ON pd.id_barang = b.id
LEFT JOIN gudang g ON pd.id_gudang = g.id
LEFT JOIN satuan sat ON b.satuan_id = sat.id
ORDER BY p.tanggal_pembelian DESC, b.nama_barang ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pembelian_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pembelian_summary` AS SELECT 
    p.id,
    p.nomor_pembelian,
    p.tanggal_pembelian,
    p.id_supplier,
    s.kode as kode_supplier,
    s.nama as nama_supplier,
    s.alamat as alamat_supplier,
    s.no_telepon as telepon_supplier,
    s.email as email_supplier,
    p.jenis_pembelian,
    p.status,
    p.total_item,
    p.total_qty,
    p.subtotal,
    p.diskon_persen,
    p.diskon_nominal,
    p.total_sebelum_pajak,
    p.ppn_persen,
    p.ppn_nominal,
    p.total_setelah_pajak,
    p.biaya_pengiriman,
    p.total_akhir,
    p.tanggal_jatuh_tempo,
    p.syarat_pembayaran,
    p.metode_pembayaran,
    p.nomor_po_supplier,
    p.alamat_pengiriman,
    p.keterangan,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by,
    p.approved_by,
    p.approved_at,
    -- Hitung total yang sudah dibayar
    COALESCE(SUM(pp.jumlah_bayar), 0) as total_dibayar,
    -- Hitung sisa yang belum dibayar
    (p.total_akhir - COALESCE(SUM(pp.jumlah_bayar), 0)) as sisa_pembayaran,
    -- Status pembayaran
    CASE 
        WHEN COALESCE(SUM(pp.jumlah_bayar), 0) = 0 THEN 'belum_bayar'
        WHEN COALESCE(SUM(pp.jumlah_bayar), 0) < p.total_akhir THEN 'sebagian'
        WHEN COALESCE(SUM(pp.jumlah_bayar), 0) >= p.total_akhir THEN 'lunas'
        ELSE 'belum_bayar'
    END as status_pembayaran
FROM pembelian p
LEFT JOIN supplier s ON p.id_supplier = s.id
LEFT JOIN pembelian_pembayaran pp ON p.id = pp.id_pembelian AND pp.status = 'verified'
GROUP BY p.id
ORDER BY p.tanggal_pembelian DESC, p.id DESC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pengiriman_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pengiriman_detail` AS SELECT 
    pd.id,
    pd.id_pengiriman,
    pd.id_barang,
    pd.id_gudang,
    pd.qty_dikirim,
    pd.berat_satuan,
    pd.total_berat,
    pd.keterangan,
    p.nomor_pengiriman,
    p.tanggal_pengiriman,
    p.status as status_pengiriman,
    b.kode_barang,
    b.nama_barang,
    b.merk,
    b.tipe,
    s.nama_satuan,
    g.kode_gudang,
    g.nama_gudang,
    pel.nama as nama_pelanggan,
    pes.nomor_pesanan
FROM pengiriman_detail pd
LEFT JOIN pengiriman p ON pd.id_pengiriman = p.id
LEFT JOIN barang b ON pd.id_barang = b.id
LEFT JOIN satuan s ON b.satuan_id = s.id
LEFT JOIN gudang g ON pd.id_gudang = g.id
LEFT JOIN pelanggan pel ON p.id_pelanggan = pel.id
LEFT JOIN pesanan pes ON p.id_pesanan = pes.id
ORDER BY p.tanggal_pengiriman DESC, b.nama_barang ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pengiriman_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pengiriman_summary` AS SELECT 
    p.id,
    p.nomor_pengiriman,
    p.tanggal_pengiriman,
    p.id_pesanan,
    p.id_pelanggan,
    p.alamat_pengiriman,
    p.status,
    p.total_item,
    p.total_qty,
    p.total_berat,
    p.estimasi_tiba,
    p.tanggal_dikirim,
    p.tanggal_diterima,
    p.penerima,
    p.keterangan,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by,
    pel.kode as kode_pelanggan,
    pel.nama as nama_pelanggan,
    pel.alamat as alamat_pelanggan,
    pel.no_telepon,
    pes.nomor_pesanan,
    pes.tanggal_pesanan,
    pes.total_harga as total_pesanan,
    pes.status as status_pesanan
FROM pengiriman p
LEFT JOIN pelanggan pel ON p.id_pelanggan = pel.id
LEFT JOIN pesanan pes ON p.id_pesanan = pes.id
ORDER BY p.id DESC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_movement_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_movement_detail` AS SELECT ROW_NUMBER() OVER (ORDER BY sm.tanggal) AS id, sm.id_barang, b.kode_barang, b.nama_barang, sm.id_gudang, g.kode_gudang, g.nama_gudang, sm.tanggal, sm.jenis_transaksi, sm.nomor_referensi, sm.qty_masuk, sm.qty_keluar, sm.keterangan FROM (SELECT id_barang, id_gudang, tanggal, 'Barang Masuk' AS jenis_transaksi, nomor_penerimaan AS nomor_referensi, qty_diterima AS qty_masuk, 0 AS qty_keluar, bm.keterangan FROM barang_masuk_detail bmd JOIN barang_masuk bm ON bm.id = bmd.id_barang_masuk UNION ALL SELECT id_barang, id_gudang, tanggal, 'Barang Keluar' AS jenis_transaksi, nomor_pengeluaran AS nomor_referensi, 0 AS qty_masuk, qty_keluar AS qty_keluar, bk.keterangan FROM barang_keluar_detail bkd JOIN barang_keluar bk ON bk.id = bkd.id_barang_keluar UNION ALL SELECT id_barang, id_gudang, tanggal_opname AS tanggal, 'Stok Opname' AS jenis_transaksi, nomor_opname AS nomor_referensi, CASE WHEN selisih > 0 THEN selisih ELSE 0 END AS qty_masuk, CASE WHEN selisih < 0 THEN ABS(selisih) ELSE 0 END AS qty_keluar, so.keterangan FROM stok_opname_detail sod JOIN stok_opname so ON so.id = sod.id_opname WHERE so.status = 'final' AND sod.selisih != 0) sm JOIN barang b ON b.id = sm.id_barang JOIN gudang g ON g.id = sm.id_gudang ORDER BY sm.tanggal DESC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_opname_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_opname_detail` AS SELECT `sod`.`id` AS `id`, `sod`.`id_opname` AS `id_opname`, `so`.`nomor_opname` AS `nomor_opname`, `so`.`tanggal_opname` AS `tanggal_opname`, `so`.`status` AS `status`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `sod`.`qty_sistem` AS `qty_sistem`, `sod`.`qty_fisik` AS `qty_fisik`, `sod`.`selisih` AS `selisih`, `sod`.`keterangan` AS `keterangan`, `sod`.`created_at` AS `created_at` FROM (((`stok_opname_detail` `sod` join `stok_opname` `so` on(`sod`.`id_opname` = `so`.`id`)) join `barang` `b` on(`sod`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`so`.`id_gudang` = `g`.`id`)) ORDER BY `so`.`tanggal_opname` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_opname_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_opname_summary` AS SELECT `so`.`id` AS `id`, `so`.`nomor_opname` AS `nomor_opname`, `so`.`tanggal_opname` AS `tanggal_opname`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `so`.`status` AS `status`, `so`.`total_item` AS `total_item`, `so`.`total_selisih_positif` AS `total_selisih_positif`, `so`.`total_selisih_negatif` AS `total_selisih_negatif`, `so`.`keterangan` AS `keterangan`, `so`.`user_input` AS `user_input`, `so`.`user_final` AS `user_final`, `so`.`tanggal_final` AS `tanggal_final`, `so`.`created_at` AS `created_at` FROM (`stok_opname` `so` join `gudang` `g` on(`so`.`id_gudang` = `g`.`id`)) ORDER BY `so`.`tanggal_opname` DESC, `so`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_summary` AS SELECT `sb`.`id_barang` AS `id_barang`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `sb`.`id_gudang` AS `id_gudang`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `sb`.`qty_terakhir` AS `qty_terakhir`, `sb`.`updated_at` AS `updated_at` FROM ((`stok_barang` `sb` join `barang` `b` on(`sb`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`sb`.`id_gudang` = `g`.`id`)) ORDER BY `b`.`nama_barang` ASC, `g`.`nama_gudang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_transfer_stok_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_transfer_stok_detail` AS SELECT `tsd`.`id` AS `id`, `tsd`.`transfer_stok_id` AS `transfer_stok_id`, `ts`.`nomor_transfer` AS `nomor_transfer`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status`, `tsd`.`barang_id` AS `barang_id`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `tsd`.`qty` AS `qty`, `s`.`kode_satuan` AS `kode_satuan`, `s`.`nama_satuan` AS `nama_satuan`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `tsd`.`keterangan` AS `keterangan` FROM (((((`transfer_stok_detail` `tsd` left join `transfer_stok` `ts` on(`tsd`.`transfer_stok_id` = `ts`.`id`)) left join `barang` `b` on(`tsd`.`barang_id` = `b`.`id`)) left join `satuan` `s` on(`tsd`.`satuan_id` = `s`.`id`)) left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) ORDER BY `ts`.`tanggal_transfer` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_transfer_stok_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_transfer_stok_summary` AS SELECT `ts`.`id` AS `id`, `ts`.`nomor_transfer` AS `nomor_transfer`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status`, `ts`.`total_item` AS `total_item`, `ts`.`total_qty` AS `total_qty`, `ga`.`kode_gudang` AS `kode_gudang_asal`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`kode_gudang` AS `kode_gudang_tujuan`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `ts`.`keterangan` AS `keterangan`, `ts`.`tanggal_kirim` AS `tanggal_kirim`, `ts`.`tanggal_terima` AS `tanggal_terima`, `ts`.`dibuat_oleh` AS `dibuat_oleh`, `ts`.`dikirim_oleh` AS `dikirim_oleh`, `ts`.`diterima_oleh` AS `diterima_oleh`, `ts`.`dibuat_pada` AS `dibuat_pada` FROM ((`transfer_stok` `ts` left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) ORDER BY `ts`.`id` ASC 
;

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
