<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Pengiriman Barang
 * Mengatur data pengiriman barang dan detailnya
 */
class Mod_pengiriman extends CI_Model
{
    var $table = 'pengiriman';
    var $table_detail = 'pengiriman_detail';
    var $column_search = array(
        'p.nomor_pengiriman', 
        'p.tanggal_pengiriman', 
        'pel.nama as nama_pelanggan', 
        'p.status', 
        'p.keterangan'
    );
    var $column_order = array(
        'p.id', 
        'p.nomor_pengiriman', 
        'p.tanggal_pengiriman', 
        'pel.nama', 
        'p.status', 
        'p.total_item',
        'p.total_qty',
        'p.total_berat',
        'p.estimasi_tiba'
    );
    var $order = array('p.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            p.id,
            p.nomor_pengiriman,
            p.tanggal_pengiri<PERSON>,
            p.id_p<PERSON>an,
            p.id_pelanggan,
            p.alamat_pengiriman,
            p.status,
            p.total_item,
            p.total_qty,
            p.total_berat,
            p.estimasi_tiba,
            p.tanggal_dikirim,
            p.tanggal_diterima,
            p.penerima,
            p.keterangan,
            p.created_by,
            p.updated_by,
            p.created_at,
            p.updated_at,
            pel.nama as nama_pelanggan,
            pel.kode as kode_pelanggan,
            pel.alamat as alamat_pelanggan,
            pel.no_telepon,
            ps.nomor_pesanan
        ');
        $this->db->from($this->table . ' p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->join('pesanan ps', 'p.id_pesanan = ps.id', 'left');
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->select('
            p.*,
            pel.nama as nama_pelanggan,
            pel.kode as kode_pelanggan,
            pel.alamat as alamat_pelanggan,
            pel.no_telepon,
            pel.email,
            ps.nomor_pesanan,
            ps.tanggal_pesanan,
            ps.jenis_pesanan
        ');
        $this->db->from($this->table . ' p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->join('pesanan ps', 'p.id_pesanan = ps.id', 'left');
        $this->db->where('p.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    // Method konsisten dengan modul lain
    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $result = $this->db->update($this->table, $data);
        
        // Return TRUE if query executed successfully, regardless of affected rows
        // affected_rows() can be 0 if no changes were made, which is still valid
        return $result;
    }

    public function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }

    // Detail pengiriman functions
    public function get_detail_by_pengiriman_id($id_pengiriman)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.kode_satuan,
            g.nama_gudang
        ');
        $this->db->from($this->table_detail . ' pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'pd.id_gudang = g.id', 'left');
        $this->db->where('pd.id_pengiriman', $id_pengiriman);
        $this->db->order_by('pd.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    public function update_detail($where, $data)
    {
        $result = $this->db->update($this->table_detail, $data, $where);
        
        // Return TRUE if query executed successfully, regardless of affected rows
        return $result;
    }

    public function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    public function delete_detail_by_pengiriman($id_pengiriman)
    {
        $this->db->where('id_pengiriman', $id_pengiriman);
        $this->db->delete($this->table_detail);
    }

    // Generate nomor pengiriman - Format: KRM-YYYYMMDD-XXXX (Kirim)
    public function generate_nomor_pengiriman()
    {
        $prefix = 'KRM';
        $date = date('Ymd');
        
        $this->db->select('nomor_pengiriman');
        $this->db->from($this->table);
        $this->db->like('nomor_pengiriman', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_pengiriman', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_pengiriman;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Update total pengiriman
    public function update_total_pengiriman($id_pengiriman)
    {
        $this->db->select('COUNT(*) as total_item, SUM(qty_dikirim) as total_qty, SUM(total_berat) as total_berat');
        $this->db->from($this->table_detail);
        $this->db->where('id_pengiriman', $id_pengiriman);
        $query = $this->db->get();
        $result = $query->row();
        
        $data = array(
            'total_item' => $result->total_item,
            'total_qty' => $result->total_qty ? $result->total_qty : 0,
            'total_berat' => $result->total_berat ? $result->total_berat : 0,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id_pengiriman);
        $this->db->update($this->table, $data);
    }

    // Method untuk mendapatkan data dropdown
    public function get_pelanggan_aktif()
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('pelanggan');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pesanan_aktif()
    {
        $this->db->select('p.id, p.nomor_pesanan, p.tanggal_pesanan, p.id_pelanggan, p.status, pel.nama as nama_pelanggan');
        $this->db->from('pesanan p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        //$this->db->where_in('p.status', ['diproses', 'dikirim']);
        $this->db->where('p.status', 'diproses');
        $this->db->order_by('p.tanggal_pesanan', 'desc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_barang_aktif()
    {
        $this->db->select('b.id, b.kode_barang, b.nama_barang, b.merk, b.tipe, b.harga_jual, s.nama_satuan');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('b.aktif', 1);
        $this->db->order_by('b.nama_barang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_gudang_aktif()
    {
        $this->db->select('id, kode_gudang, nama_gudang, alamat');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_ekspedisi_aktif()
    {
        $this->db->select('id, kode_ekspedisi, nama_ekspedisi, telepon, tarif_per_kg');
        $this->db->from('ekspedisi');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_ekspedisi', 'asc');
        $query = $this->db->get();
        return $query->result();
    }
    
    public function get_detail_by_id($id)
    {
        $this->db->select('*');
        $this->db->from($this->table_detail);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }
    
    public function get_detail_item_by_id($id)
    {
        $this->db->select('*');
        $this->db->from($this->table_detail);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pesanan_by_id($id)
    {
        $this->db->select('p.*, pel.nama as nama_pelanggan, pel.alamat as alamat_pelanggan');
        $this->db->from('pesanan p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->where('p.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pesanan_detail($id_pesanan)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan
        ');
        $this->db->from('pesanan_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('pd.id_pesanan', $id_pesanan);
        $query = $this->db->get();
        return $query->result();
    }
    
    // Get ordered items with remaining quantities to be shipped
    public function get_ordered_items_for_shipping($id_pesanan, $id_pengiriman = null)
    {
        // First, get all items from the order
        $this->db->select('
            pd.id,
            pd.id_pesanan,
            pd.id_barang,
            pd.qty as qty_pesanan,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.id as id_satuan
        ');
        $this->db->from('pesanan_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('pd.id_pesanan', $id_pesanan);
        $order_items = $this->db->get()->result();
        
        // If no items in order, return empty array
        if (empty($order_items)) {
            return [];
        }
        
        // Untuk setiap item, hitung berapa banyak yang sudah dikirim
        foreach ($order_items as $item) {
            // Get already shipped quantity (excluding current shipment if editing)
            // Hanya hitung yang benar-benar sudah ada detail pengirimannya
            $this->db->select('COALESCE(SUM(pd.qty_dikirim), 0) as shipped_qty');
            $this->db->from('pengiriman p');
            $this->db->join('pengiriman_detail pd', 'p.id = pd.id_pengiriman');
            $this->db->where('p.id_pesanan', $id_pesanan);
            $this->db->where('pd.id_barang', $item->id_barang);
            
            if ($id_pengiriman) {
                $this->db->where('p.id !=', $id_pengiriman);
            }
            
            $shipped = $this->db->get()->row();
            $item->qty_sudah_dikirim = $shipped ? $shipped->shipped_qty : 0;
            
            // Get quantity already in the current shipment
            if ($id_pengiriman) {
                $this->db->select('COALESCE(SUM(qty_dikirim), 0) as current_qty');
                $this->db->from('pengiriman_detail');
                $this->db->where('id_pengiriman', $id_pengiriman);
                $this->db->where('id_barang', $item->id_barang);
                $current_result = $this->db->get()->row();
                $item->qty_dalam_pengiriman_ini = $current_result ? $current_result->current_qty : 0;
            } else {
                $item->qty_dalam_pengiriman_ini = 0;
            }
            
            // Calculate true remaining quantity - TIDAK mengurangi qty_dalam_pengiriman_ini
            // karena itu adalah quantity yang sedang dalam proses input
            $item->sisa_sebenarnya = $item->qty_pesanan - $item->qty_sudah_dikirim;
        }
        
        // Filter out items that have been fully shipped
        $result = [];
        foreach ($order_items as $item) {
            // Include item if there's still quantity available to ship or if it's already in this shipment
            if ($item->sisa_sebenarnya > 0 || ($id_pengiriman && $item->qty_dalam_pengiriman_ini > 0)) {
                $result[] = $item;
            }
        }
        
        return $result;
    }
    
    // Get warehouses with available stock for a specific product
    public function get_warehouses_with_stock($id_barang)
    {
        if (empty($id_barang)) {
            return [];
        }
        
        // First check if the product exists
        $this->db->select('id');
        $this->db->from('barang');
        $this->db->where('id', $id_barang);
        $product_exists = $this->db->get()->num_rows() > 0;
        
        if (!$product_exists) {
            return [];
        }
        
        // Get warehouses with stock
        $this->db->select('
            g.id,
            g.kode_gudang,
            g.nama_gudang,
            g.alamat,
            COALESCE(sb.qty_terakhir, 0) as stok_tersedia
        ');
        $this->db->from('gudang g');
        $this->db->join('stok_barang sb', 'g.id = sb.id_gudang AND sb.id_barang = ' . $id_barang, 'left');
        $this->db->where('g.aktif', 1);
        $this->db->where('COALESCE(sb.qty_terakhir, 0) >', 0);
        $this->db->order_by('g.nama_gudang', 'asc');
        
        $query = $this->db->get();
        return $query->result();
    }
    
    // Get available stock for a specific product in a specific warehouse
    public function get_stok_barang($id_barang, $id_gudang)
    {
        $this->db->select('qty_terakhir');
        $this->db->from('stok_barang');
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $result = $this->db->get()->row();
        
        return $result ? $result->qty_terakhir : 0;
    }
    
    // Get remaining quantity to be shipped for an order item
    public function get_remaining_qty_to_ship($id_pesanan, $id_barang, $id_pengiriman = null)
    {
        // Get ordered quantity
        $this->db->select('qty');
        $this->db->from('pesanan_detail');
        $this->db->where('id_pesanan', $id_pesanan);
        $this->db->where('id_barang', $id_barang);
        $result = $this->db->get()->row();
        
        if (!$result) {
            return 0; // Item not found in order
        }
        
        $ordered_qty = $result->qty;
        
        // Get already shipped quantity (excluding current shipment if editing)
        // Hanya hitung yang benar-benar sudah ada detail pengirimannya
        $this->db->select('COALESCE(SUM(pd.qty_dikirim), 0) as shipped_qty');
        $this->db->from('pengiriman p');
        $this->db->join('pengiriman_detail pd', 'p.id = pd.id_pengiriman');
        $this->db->where('p.id_pesanan', $id_pesanan);
        $this->db->where('pd.id_barang', $id_barang);
        
        if ($id_pengiriman) {
            $this->db->where('p.id !=', $id_pengiriman);
        }
        
        $shipped_result = $this->db->get()->row();
        $shipped_qty = $shipped_result ? $shipped_result->shipped_qty : 0;
        
        // Jangan kurangi current_shipment_qty karena itu adalah quantity yang sedang dalam proses input
        // Sisa pesanan harus tetap menunjukkan berapa yang masih perlu dikirim dari pesanan asli
        
        return $ordered_qty - $shipped_qty;
    }

    public function update_status_pesanan($id_pesanan, $status)
    {
        $data = array(
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->userdata('nama_user')
        );
        
        $this->db->where('id', $id_pesanan);
        $result = $this->db->update('pesanan', $data);
        
        // Return TRUE if query executed successfully, regardless of affected rows
        return $result;
    }
    
    public function get_berat_barang_by_id($id_barang)
    {
        $this->db->select('berat');
        $this->db->from('barang');
        $this->db->where('id', $id_barang);
        $this->db->where('aktif', 1);
        $result = $this->db->get()->row();
        
        return $result ? $result->berat : null;
    }
}