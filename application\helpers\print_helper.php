<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Print Helper
 * Helper untuk memudahkan pembuatan dokumen print yang seragam
 */

if (!function_exists('load_print_template')) {
    /**
     * Load print template dengan data yang diberikan
     * 
     * @param array $data Data untuk template
     * @param string $content_view View content yang akan dimuat
     * @param int $template_type Override template type (opsional)
     * @return string HTML output
     */
    function load_print_template($data = [], $content_view = '', $template_type = null) {
        $CI =& get_instance();
        
        // Load print configuration
        $CI->load->config('print_config');
        
        // Tentukan template yang akan digunakan
        if ($template_type === null) {
            $template_type = $CI->config->item('print_template_type') ?: 1;
        }
        
        // Validasi template type
        $available_templates = $CI->config->item('print_template_files');
        if (!isset($available_templates[$template_type])) {
            $template_type = 1; // Fallback ke template default
        }
        
        // Get company info dari config
        $company_info = $CI->config->item('company_info');
        
        // Default data untuk template
        $default_data = [
            'title' => 'Print Document',
            'document_number' => '',
            'document_title' => 'DOKUMEN',
            'company_name' => $company_info['name'] ?? 'TOKO ELEKTRONIK',
            'company_address' => $company_info['address'] ?? 'Jl. Contoh No. 123, Kota Contoh',
            'company_phone' => $company_info['phone'] ?? '(021) 1234567',
            'company_email' => $company_info['email'] ?? '<EMAIL>',
            'company_website' => $company_info['website'] ?? '',
            'template_type' => $template_type,
            'content' => ''
        ];
        
        // Merge data
        $template_data = array_merge($default_data, $data);
        
        // Load content view jika ada
        if (!empty($content_view)) {
            $template_data['content'] = $CI->load->view($content_view, $data, TRUE);
        }
        
        // Get template file berdasarkan type
        $template_file = $available_templates[$template_type];
        
        // Debug info jika diperlukan
        if ($CI->config->item('print_debug')) {
            $template_descriptions = $CI->config->item('print_template_descriptions');
            log_message('debug', 'Print Template: ' . $template_file . ' - ' . ($template_descriptions[$template_type] ?? 'Unknown'));
        }
        
        // Load template
        return $CI->load->view($template_file, $template_data, TRUE);
    }
}

if (!function_exists('format_currency')) {
    /**
     * Format currency untuk Indonesia
     * 
     * @param float $amount
     * @param bool $show_symbol
     * @return string
     */
    function format_currency($amount, $show_symbol = true) {
        $formatted = number_format($amount, 0, ',', '.');
        return $show_symbol ? 'Rp ' . $formatted : $formatted;
    }
}

if (!function_exists('format_date_indonesia')) {
    /**
     * Format tanggal ke format Indonesia
     * 
     * @param string $date
     * @param bool $show_time
     * @return string
     */
    function format_date_indonesia($date, $show_time = false) {
        if (empty($date) || $date == '0000-00-00' || $date == '0000-00-00 00:00:00') {
            return '-';
        }
        
        $months = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];
        
        $timestamp = strtotime($date);
        $day = date('d', $timestamp);
        $month = $months[(int)date('m', $timestamp)];
        $year = date('Y', $timestamp);
        
        $formatted = $day . ' ' . $month . ' ' . $year;
        
        if ($show_time) {
            $formatted .= ' ' . date('H:i', $timestamp);
        }
        
        return $formatted;
    }
}

if (!function_exists('get_status_badge')) {
    /**
     * Generate status badge HTML
     * 
     * @param string $status
     * @param array $custom_labels
     * @return string
     */
    function get_status_badge($status, $custom_labels = []) {
        $default_labels = [
            'draft' => 'Draft',
            'diproses' => 'Diproses',
            'dikirim' => 'Dikirim',
            'diterima' => 'Diterima',
            'selesai' => 'Selesai',
            'dibatalkan' => 'Dibatalkan',
            'lunas' => 'Lunas',
            'belum_bayar' => 'Belum Bayar',
            'sebagian' => 'Bayar Sebagian'
        ];
        
        $labels = array_merge($default_labels, $custom_labels);
        $label = isset($labels[$status]) ? $labels[$status] : ucfirst($status);
        
        return '<span class="status-badge status-' . $status . '">' . $label . '</span>';
    }
}

if (!function_exists('create_info_section')) {
    /**
     * Create info section HTML
     * 
     * @param array $sections Array of sections with title and items
     * @return string
     */
    function create_info_section($sections) {
        $html = '<div class="info-section">';
        
        foreach ($sections as $section) {
            $html .= '<div class="info-box">';
            $html .= '<div class="info-box-title">' . $section['title'] . '</div>';
            
            foreach ($section['items'] as $item) {
                $html .= '<div class="info-item">';
                $html .= '<span class="info-label">' . $item['label'] . '</span>';
                $html .= '<span class="info-separator">:</span>';
                $html .= '<span class="info-value">' . $item['value'] . '</span>';
                $html .= '</div>';
            }
            
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}

if (!function_exists('create_data_table')) {
    /**
     * Create data table HTML
     * 
     * @param string $title Table title
     * @param array $headers Table headers
     * @param array $data Table data
     * @param array $options Additional options
     * @return string
     */
    function create_data_table($title, $headers, $data, $options = []) {
        $html = '<div class="table-container">';
        $html .= '<div class="table-title">' . $title . '</div>';
        $html .= '<table class="data-table">';
        
        // Headers
        $html .= '<thead><tr>';
        foreach ($headers as $header) {
            $width = isset($header['width']) ? ' style="width: ' . $header['width'] . '"' : '';
            $html .= '<th' . $width . '>' . $header['label'] . '</th>';
        }
        $html .= '</tr></thead>';
        
        // Body
        $html .= '<tbody>';
        
        if (empty($data)) {
            $html .= '<tr><td colspan="' . count($headers) . '" class="empty-state">';
            $html .= '<i>📋</i><br>Tidak ada data untuk ditampilkan';
            $html .= '</td></tr>';
        } else {
            foreach ($data as $row) {
                $html .= '<tr>';
                foreach ($headers as $key => $header) {
                    $align = isset($header['align']) ? ' class="text-' . $header['align'] . '"' : '';
                    $value = isset($row[$key]) ? $row[$key] : '';
                    $html .= '<td' . $align . '>' . $value . '</td>';
                }
                $html .= '</tr>';
            }
            
            // Total row if provided
            if (isset($options['total_row'])) {
                $html .= '<tr class="total-row">';
                foreach ($options['total_row'] as $cell) {
                    $colspan = isset($cell['colspan']) ? ' colspan="' . $cell['colspan'] . '"' : '';
                    $align = isset($cell['align']) ? ' class="text-' . $cell['align'] . '"' : '';
                    $html .= '<td' . $colspan . $align . '>' . $cell['value'] . '</td>';
                }
                $html .= '</tr>';
            }
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';
        
        return $html;
    }
}

if (!function_exists('create_signature_section')) {
    /**
     * Create signature section HTML
     * 
     * @param array $signatures Array of signature boxes
     * @return string
     */
    function create_signature_section($signatures) {
        $html = '<div class="signature-section">';
        
        foreach ($signatures as $signature) {
            $html .= '<div class="signature-box">';
            $html .= '<div class="signature-title">' . $signature['title'] . '</div>';
            $html .= '<div class="signature-line">' . ($signature['name'] ?? '(............................)') . '</div>';
            if (isset($signature['position'])) {
                $html .= '<div class="text-small color-muted mt-10">' . $signature['position'] . '</div>';
            }
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}

if (!function_exists('create_summary_table')) {
    /**
     * Create summary table HTML
     * 
     * @param array $items Summary items
     * @param array $options Additional options
     * @return string
     */
    function create_summary_table($items, $options = []) {
        $html = '<table class="summary-table">';
        
        foreach ($items as $item) {
            $class = isset($item['class']) ? ' class="' . $item['class'] . '"' : '';
            $html .= '<tr' . $class . '>';
            $html .= '<th>' . $item['label'] . '</th>';
            $html .= '<td>' . $item['value'] . '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</table>';
        
        return $html;
    }
}

if (!function_exists('create_notes_section')) {
    /**
     * Create notes section HTML
     * 
     * @param string $title
     * @param string $content
     * @return string
     */
    function create_notes_section($title, $content) {
        if (empty($content)) {
            return '';
        }
        
        $html = '<div class="notes-section">';
        $html .= '<div class="notes-title">' . $title . '</div>';
        $html .= '<div class="notes-content">' . nl2br(htmlspecialchars($content)) . '</div>';
        $html .= '</div>';
        
        return $html;
    }
}

if (!function_exists('get_print_template_info')) {
    /**
     * Mendapatkan informasi template print yang tersedia
     * 
     * @return array
     */
    function get_print_template_info() {
        $CI =& get_instance();
        $CI->load->config('print_config');
        
        return [
            'current_template' => $CI->config->item('print_template_type'),
            'available_templates' => $CI->config->item('print_template_files'),
            'descriptions' => $CI->config->item('print_template_descriptions'),
            'features' => $CI->config->item('template_features'),
            'quality_settings' => $CI->config->item('print_quality')
        ];
    }
}

if (!function_exists('set_print_template_type')) {
    /**
     * Set template type untuk session saat ini (temporary override)
     * 
     * @param int $template_type
     * @return bool
     */
    function set_print_template_type($template_type) {
        $CI =& get_instance();
        $CI->load->config('print_config');
        
        $available_templates = $CI->config->item('print_template_files');
        if (isset($available_templates[$template_type])) {
            $CI->session->set_userdata('temp_print_template_type', $template_type);
            return true;
        }
        
        return false;
    }
}

if (!function_exists('get_current_template_type')) {
    /**
     * Mendapatkan template type yang sedang aktif
     * 
     * @return int
     */
    function get_current_template_type() {
        $CI =& get_instance();
        $CI->load->config('print_config');
        
        // Cek jika ada temporary override dari session
        $temp_type = $CI->session->userdata('temp_print_template_type');
        if ($temp_type !== null) {
            return (int)$temp_type;
        }
        
        // Return default dari config
        return (int)($CI->config->item('print_template_type') ?: 1);
    }
}

if (!function_exists('clear_temp_template_type')) {
    /**
     * Hapus temporary template type override
     * 
     * @return void
     */
    function clear_temp_template_type() {
        $CI =& get_instance();
        $CI->session->unset_userdata('temp_print_template_type');
    }
}

if (!function_exists('create_template_selector')) {
    /**
     * Create template selector untuk development/testing
     * (Hanya untuk pengembang, tidak untuk user)
     * 
     * @param string $current_url
     * @return string
     */
    function create_template_selector($current_url = '') {
        if (ENVIRONMENT !== 'development') {
            return ''; // Hanya tampil di development mode
        }
        
        $CI =& get_instance();
        $CI->load->config('print_config');
        
        $current_type = get_current_template_type();
        $descriptions = $CI->config->item('print_template_descriptions');
        
        $html = '<div class="template-selector no-print" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">';
        $html .= '<strong>Template Selector (Development Only):</strong><br>';
        
        foreach ($descriptions as $type => $desc) {
            $selected = ($type == $current_type) ? ' checked' : '';
            $url = $current_url . (strpos($current_url, '?') !== false ? '&' : '?') . 'template_type=' . $type;
            
            $html .= '<label style="margin-right: 15px;">';
            $html .= '<input type="radio" name="template_type" value="' . $type . '"' . $selected . ' onchange="window.location.href=\'' . $url . '\'"> ';
            $html .= 'Template ' . $type . ' - ' . $desc;
            $html .= '</label><br>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}