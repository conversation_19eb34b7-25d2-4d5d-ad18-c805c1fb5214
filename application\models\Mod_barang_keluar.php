<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Barang Keluar
 * Mengatur data barang keluar dan detailnya
 */
class Mod_barang_keluar extends CI_Model
{
    var $table = 'barang_keluar';
    var $table_detail = 'barang_keluar_detail';
    var $column_search = array(
        'bk.nomor_pengeluaran', 
        'bk.tanggal', 
        'p.nama', 
        'bk.jenis', 
        'bk.status', 
        'bk.ref_nomor'
    );
    var $column_order = array(
        'bk.id', 
        'bk.nomor_pengeluaran', 
        'bk.tanggal', 
        'p.nama', 
        'bk.jenis', 
        'bk.status',
        'bk.total_item',
        'bk.total_qty'
    );
    var $order = array('bk.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    // ===== HEADER BARANG KELUAR METHODS =====

    private function _get_datatables_query()
    {
        $this->db->select('
            bk.id,
            bk.nomor_pengeluaran,
            bk.tanggal,
            bk.jenis,
            bk.ref_nomor,
            bk.status,
            bk.total_item,
            bk.total_qty,
            bk.keterangan,
            p.kode as kode_pelanggan,
            p.nama as nama_pelanggan
        ');
        $this->db->from('barang_keluar bk');
        $this->db->join('pelanggan p', 'bk.id_pelanggan = p.id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // Insert header barang keluar
    function insert($data)
    {
        $insert = $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // Update header barang keluar
    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    // Get header barang keluar
    function get($id)
    {
        $this->db->select('
            bk.*,
            p.kode as kode_pelanggan,
            p.nama as nama_pelanggan,
            u1.full_name as nama_created_by,
            u2.full_name as nama_finalized_by
        ');
        $this->db->from('barang_keluar bk');
        $this->db->join('pelanggan p', 'bk.id_pelanggan = p.id', 'left');
        $this->db->join('tbl_user u1', 'bk.created_by = u1.id_user', 'left');
        $this->db->join('tbl_user u2', 'bk.finalized_by = u2.id_user', 'left');
        $this->db->where('bk.id', $id);
        return $this->db->get()->row();
    }

    // Delete header barang keluar (cascade delete detail)
    function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Generate nomor pengeluaran otomatis - Format: BK-YYYYMMDD-XXXX
    function generate_nomor()
    {
        $prefix = 'BK';
        $date = date('Ymd');
        
        $this->db->select('nomor_pengeluaran');
        $this->db->from($this->table);
        $this->db->like('nomor_pengeluaran', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_pengeluaran', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_nomor = $query->row()->nomor_pengeluaran;
            $last_sequence = (int)substr($last_nomor, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Check nomor exists
    function check_nomor_exists($nomor, $id = null)
    {
        $this->db->where('nomor_pengeluaran', $nomor);
        if ($id) {
            $this->db->where('id !=', $id);
        }
        $query = $this->db->get($this->table);
        return $query->num_rows() > 0;
    }

    // Finalisasi barang keluar
    function finalize_barang_keluar($id, $user_final)
    {
        // Cek apakah masih draft
        $barang_keluar = $this->get($id);
        if (!$barang_keluar || $barang_keluar->status != 'draft') {
            return false;
        }

        // Cek apakah ada detail
        $detail_count = $this->db->where('id_barang_keluar', $id)->count_all_results($this->table_detail);
        if ($detail_count == 0) {
            return false;
        }

        // Validasi stok tersedia untuk semua item
        $validation_result = $this->validate_stock_availability($id);
        if (!$validation_result['valid']) {
            return $validation_result;
        }

        // Update status ke final
        $data = array(
            'status' => 'final',
            'finalized_by' => $user_final,
            'finalized_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
        
        return array('valid' => true, 'message' => 'Barang keluar berhasil difinalisasi');
    }

    // Validasi ketersediaan stok
    function validate_stock_availability($id_barang_keluar)
    {
        $this->db->select('
            bkd.id_barang,
            bkd.id_gudang,
            bkd.qty_keluar,
            b.nama_barang,
            g.nama_gudang,
            COALESCE(sb.qty_terakhir, 0) as stok_tersedia
        ');
        $this->db->from('barang_keluar_detail bkd');
        $this->db->join('barang b', 'bkd.id_barang = b.id');
        $this->db->join('gudang g', 'bkd.id_gudang = g.id');
        $this->db->join('stok_barang sb', 'bkd.id_barang = sb.id_barang AND bkd.id_gudang = sb.id_gudang', 'left');
        $this->db->where('bkd.id_barang_keluar', $id_barang_keluar);
        
        $details = $this->db->get()->result();
        
        $insufficient_items = array();
        
        foreach ($details as $detail) {
            if ($detail->stok_tersedia < $detail->qty_keluar) {
                $insufficient_items[] = array(
                    'nama_barang' => $detail->nama_barang,
                    'nama_gudang' => $detail->nama_gudang,
                    'qty_keluar' => $detail->qty_keluar,
                    'stok_tersedia' => $detail->stok_tersedia
                );
            }
        }
        
        if (!empty($insufficient_items)) {
            return array(
                'valid' => false,
                'message' => 'Stok tidak mencukupi',
                'insufficient_items' => $insufficient_items
            );
        }
        
        return array('valid' => true);
    }

    // ===== DROPDOWN DATA METHODS =====

    function get_pelanggan_dropdown()
    {
        $this->db->select('id, kode, nama');
        $this->db->from('pelanggan');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'ASC');
        return $this->db->get()->result();
    }

    function get_barang_dropdown()
    {
        $this->db->select('id, kode_barang, nama_barang, satuan_id');
        $this->db->from('barang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    function get_gudang_dropdown()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'ASC');
        return $this->db->get()->result();
    }

    function get_satuan_dropdown()
    {
        $this->db->select('id, kode_satuan, nama_satuan');
        $this->db->from('satuan');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_satuan', 'ASC');
        return $this->db->get()->result();
    }

    // ===== DETAIL BARANG KELUAR METHODS =====

    // Get detail barang keluar
    function get_detail($id_barang_keluar)
    {
        $this->db->select('
            bkd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            g.kode_gudang,
            g.nama_gudang,
            s.kode_satuan,
            s.nama_satuan,
            COALESCE(sb.qty_terakhir, 0) as stok_tersedia
        ');
        $this->db->from('barang_keluar_detail bkd');
        $this->db->join('barang b', 'bkd.id_barang = b.id');
        $this->db->join('gudang g', 'bkd.id_gudang = g.id');
        $this->db->join('satuan s', 'bkd.id_satuan = s.id', 'left');
        $this->db->join('stok_barang sb', 'bkd.id_barang = sb.id_barang AND bkd.id_gudang = sb.id_gudang', 'left');
        $this->db->where('bkd.id_barang_keluar', $id_barang_keluar);
        $this->db->order_by('bkd.id', 'ASC');
        return $this->db->get()->result();
    }

    // Get detail by ID
    function get_detail_by_id($id)
    {
        $this->db->select('
            bkd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            g.kode_gudang,
            g.nama_gudang,
            s.kode_satuan,
            s.nama_satuan
        ');
        $this->db->from('barang_keluar_detail bkd');
        $this->db->join('barang b', 'bkd.id_barang = b.id');
        $this->db->join('gudang g', 'bkd.id_gudang = g.id');
        $this->db->join('satuan s', 'bkd.id_satuan = s.id', 'left');
        $this->db->where('bkd.id', $id);
        return $this->db->get()->row();
    }

    // Insert detail barang keluar
    function insert_detail($data)
    {
        $insert = $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    // Update detail barang keluar
    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    // Delete detail barang keluar
    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    // Get barang detail untuk form
    function get_barang_detail($id_barang)
    {
        $this->db->select('id, kode_barang, nama_barang, satuan_id');
        $this->db->from('barang');
        $this->db->where('id', $id_barang);
        $this->db->where('aktif', 1);
        return $this->db->get()->row();
    }

    // Get stok barang per gudang
    function get_stok_barang($id_barang, $id_gudang)
    {
        $this->db->select('qty_terakhir');
        $this->db->from('stok_barang');
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $result = $this->db->get()->row();

        return $result ? $result->qty_terakhir : 0;
    }

    // Check duplicate detail (barang + gudang yang sama dalam satu transaksi)
    function check_duplicate_detail($id_barang_keluar, $id_barang, $id_gudang, $id_detail = null)
    {
        $this->db->where('id_barang_keluar', $id_barang_keluar);
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);

        if ($id_detail) {
            $this->db->where('id !=', $id_detail);
        }

        $query = $this->db->get($this->table_detail);
        return $query->num_rows() > 0;
    }
}
