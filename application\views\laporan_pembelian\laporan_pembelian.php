<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        
        <!-- Summary Cards -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= number_format($summary->total_transaksi ?? 0) ?></h3>
                        <p>Total Transaksi</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($summary->total_supplier ?? 0) ?></h3>
                        <p>Total Supplier</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>Rp <?= number_format($summary->total_nilai_pembelian ?? 0, 0) ?></h3>
                        <p>Total Nilai Pembelian</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>Rp <?= number_format($summary->total_outstanding ?? 0, 0) ?></h3>
                        <p>Outstanding Payment</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> Filter Laporan
                </h3>
            </div>
            <div class="card-body">
                <form id="form-filter">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Jenis Laporan</label>
                                <select class="form-control" id="jenis_laporan" name="jenis_laporan" required>
                                    <option value="">Pilih Jenis Laporan</option>
                                    <option value="pembelian_periode">Pembelian Periode</option>
                                    <option value="detail_pembelian">Detail Pembelian</option>
                                    <option value="pembelian_supplier">Pembelian per Supplier</option>
                                    <option value="pembelian_barang">Pembelian per Barang</option>
                                    <option value="outstanding_po">Outstanding Purchase Order</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Supplier</label>
                                <select class="form-control select2" id="id_supplier" name="id_supplier">
                                    <option value="">Semua Supplier</option>
                                    <?php foreach ($supplier_list->result() as $supplier): ?>
                                        <option value="<?= $supplier->id ?>"><?= $supplier->kode ?> - <?= $supplier->nama ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Barang</label>
                                <select class="form-control select2" id="id_barang" name="id_barang">
                                    <option value="">Semua Barang</option>
                                    <?php foreach ($barang_list->result() as $barang): ?>
                                        <option value="<?= $barang->id ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3" id="filter-status">
                            <div class="form-group">
                                <label>Status</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">Semua Status</option>
                                    <option value="draft">Draft</option>
                                    <option value="disetujui">Disetujui</option>
                                    <option value="dipesan">Dipesan</option>
                                    <option value="diterima">Diterima</option>
                                    <option value="selesai">Selesai</option>
                                    <option value="dibatalkan">Dibatalkan</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="filter-tanggal">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Tanggal Dari</label>
                                <input type="date" class="form-control" id="tanggal_dari" name="tanggal_dari">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Tanggal Sampai</label>
                                <input type="date" class="form-control" id="tanggal_sampai" name="tanggal_sampai">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary" id="btn-generate">
                                <i class="fas fa-search"></i> Generate Laporan
                            </button>
                            <button type="button" class="btn btn-success" id="btn-excel" style="display: none;">
                                <i class="fas fa-file-excel"></i> Export Excel
                            </button>
                            <button type="button" class="btn btn-info" id="btn-cetak" style="display: none;">
                                <i class="fas fa-print"></i> Cetak
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Result Card -->
        <div class="card" id="card-result" style="display: none;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-table"></i> Hasil Laporan
                </h3>
            </div>
            <div class="card-body">
                <div id="result-content">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>

    </div>
</section>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Handle jenis laporan change
    $('#jenis_laporan').change(function() {
        var jenis = $(this).val();
        
        // Show/hide filters based on report type
        if (jenis === 'outstanding_po') {
            $('#filter-tanggal').hide();
            $('#filter-status').hide();
        } else if (jenis === 'pembelian_supplier' || jenis === 'pembelian_barang') {
            $('#filter-tanggal').show();
            $('#filter-status').hide();
        } else {
            $('#filter-tanggal').show();
            $('#filter-status').show();
        }
        
        // Hide result and buttons
        $('#card-result').hide();
        $('#btn-excel, #btn-cetak').hide();
    });

    // Generate laporan
    $('#btn-generate').click(function() {
        var jenis_laporan = $('#jenis_laporan').val();
        
        if (!jenis_laporan) {
            Swal.fire({
                icon: 'warning',
                title: 'Peringatan',
                text: 'Silakan pilih jenis laporan terlebih dahulu!'
            });
            return;
        }

        // Validate date range
        var tanggal_dari = $('#tanggal_dari').val();
        var tanggal_sampai = $('#tanggal_sampai').val();
        
        if (tanggal_dari && tanggal_sampai && tanggal_dari > tanggal_sampai) {
            Swal.fire({
                icon: 'warning',
                title: 'Peringatan',
                text: 'Tanggal dari tidak boleh lebih besar dari tanggal sampai!'
            });
            return;
        }

        // Show loading
        Swal.fire({
            title: 'Memproses...',
            text: 'Sedang menggenerate laporan',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Submit form
        $.ajax({
            url: '<?= base_url('Laporan_pembelian/generate_laporan') ?>',
            type: 'POST',
            data: $('#form-filter').serialize(),
            success: function(response) {
                Swal.close();
                $('#result-content').html(response);
                $('#card-result').show();
                $('#btn-excel, #btn-cetak').show();
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Terjadi kesalahan saat menggenerate laporan!'
                });
            }
        });
    });

    // Export Excel
    $('#btn-excel').click(function() {
        var form = $('<form>', {
            'method': 'POST',
            'action': '<?= base_url('Laporan_pembelian/export_excel') ?>'
        });

        // Add form data
        $('#form-filter').serializeArray().forEach(function(field) {
            form.append($('<input>', {
                'type': 'hidden',
                'name': field.name,
                'value': field.value
            }));
        });

        $('body').append(form);
        form.submit();
        form.remove();
    });

    // Cetak
    $('#btn-cetak').click(function() {
        var form = $('<form>', {
            'method': 'POST',
            'action': '<?= base_url('Laporan_pembelian/cetak') ?>',
            'target': '_blank'
        });

        // Add form data
        $('#form-filter').serializeArray().forEach(function(field) {
            form.append($('<input>', {
                'type': 'hidden',
                'name': field.name,
                'value': field.value
            }));
        });

        $('body').append(form);
        form.submit();
        form.remove();
    });
});
</script>