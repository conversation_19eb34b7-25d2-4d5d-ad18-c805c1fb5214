<form id="form" class="form-horizontal">
    <input type="hidden" name="id" value="">
    <input type="hidden" name="id_supplier_hidden" id="id_supplier_hidden" value="">
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-md-4 col-form-label">Nomor Penerimaan</label>
                <div class="col-md-8">
                    <input type="text" class="form-control" name="nomor_penerimaan" id="nomor_penerimaan" value="<?= $nomor_penerimaan ?>" readonly>
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-md-4 col-form-label">Tanggal Penerimaan</label>
                <div class="col-md-8">
                    <input type="date" class="form-control" name="tanggal_penerimaan" id="tanggal_penerimaan" value="<?= date('Y-m-d') ?>">
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-md-4 col-form-label">Pembelian</label>
                <div class="col-md-8">
                    <select class="form-control select2" name="id_pembelian" id="id_pembelian" style="width: 100%;">
                        <option value="">-- Pilih Pembelian --</option>
                        <?php foreach ($pembelian_list as $pembelian) : ?>
                            <option value="<?= $pembelian->id ?>" data-supplier="<?= $pembelian->id_supplier ?>">
                                <?= $pembelian->nomor_pembelian ?> - <?= date('d/m/Y', strtotime($pembelian->tanggal_pembelian)) ?> - <?= $pembelian->nama_supplier ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group row">
                <label class="col-md-4 col-form-label">Supplier</label>
                <div class="col-md-8">
                    <select class="form-control select2" id="id_supplier_display" style="width: 100%;" disabled>
                        <option value="">-- Pilih Supplier --</option>
                        <?php foreach ($supplier_list as $supplier) : ?>
                            <option value="<?= $supplier->id ?>">
                                <?= $supplier->nama ?> (<?= $supplier->kode ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <input type="hidden" name="id_supplier" id="id_supplier" value="">
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="form-group row">
                <label class="col-md-2 col-form-label">Keterangan</label>
                <div class="col-md-10">
                    <textarea class="form-control" name="keterangan" id="keterangan" rows="2"></textarea>
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Detail Item Penerimaan</h3>
                </div>
                <div class="card-body p-0" id="detail-items-container">
                    <div class="text-center py-3">
                        <p class="text-muted">Pilih pembelian terlebih dahulu untuk menampilkan item</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('.select2').select2();
        
        // Ketika pembelian dipilih
        $('#id_pembelian').change(function() {
            var id_pembelian = $(this).val();
            var id_supplier = $(this).find(':selected').data('supplier');
            
            if (id_pembelian) {
                // Set supplier
                $('#id_supplier_display').val(id_supplier).trigger('change');
                $('#id_supplier').val(id_supplier); // Set hidden input value
                $('#id_supplier_hidden').val(id_supplier);
                
                // Load detail items
                loadDetailItems(id_pembelian);
            } else {
                $('#id_supplier_display').val('').trigger('change');
                $('#id_supplier').val(''); // Clear hidden input value
                $('#id_supplier_hidden').val('');
                $('#detail-items-container').html('<div class="text-center py-3"><p class="text-muted">Pilih pembelian terlebih dahulu untuk menampilkan item</p></div>');
            }
        });
        
        // Jika dalam mode edit, load detail items
        if ($('[name="id"]').val()) {
            var id_penerimaan = $('[name="id"]').val();
            var id_pembelian = $('#id_pembelian').val();
            
            if (id_pembelian) {
                loadDetailItems(id_pembelian, id_penerimaan);
            }
        }
    });
    
    function loadDetailItems(id_pembelian, id_penerimaan = null) {
        $('#detail-items-container').html('<div class="text-center py-3"><i class="fa fa-spinner fa-spin"></i> Loading items...</div>');
        
        // Validasi id_pembelian
        if (!id_pembelian) {
            $('#detail-items-container').html('<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> Silakan pilih pembelian terlebih dahulu.</div>');
            return;
        }
        
        $.ajax({
            url: "<?php echo site_url('penerimaanpembelian/form_detail_item') ?>",
            type: "POST",
            data: {
                id_pembelian: id_pembelian,
                id_penerimaan: id_penerimaan
            },
            dataType: "HTML",
            timeout: 30000, // 30 detik timeout
            success: function(data) {
                if (data.trim() === '') {
                    $('#detail-items-container').html('<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> Tidak ada data item yang ditemukan.</div>');
                } else if (data.includes('Tidak ada item pembelian yang tersedia untuk diterima')) {
                    // Tampilkan pesan yang lebih informatif
                    $('#detail-items-container').html(
                        '<div class="alert alert-info">' +
                        '<h5><i class="fa fa-info-circle"></i> Tidak ada item yang tersedia untuk diterima</h5>' +
                        '<p>Kemungkinan penyebab:</p>' +
                        '<ol>' +
                        '<li>Semua item dalam pembelian ini sudah diterima sepenuhnya.</li>' +
                        '<li>Pembelian ini belum memiliki item yang ditambahkan.</li>' +
                        '<li>Status pembelian tidak memungkinkan untuk penerimaan.</li>' +
                        '</ol>' +
                        '<p>Silakan pilih pembelian lain atau periksa status pembelian ini.</p>' +
                        '</div>'
                    );
                } else {
                    $('#detail-items-container').html(data);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Error loading items:', textStatus, errorThrown);
                $('#detail-items-container').html(
                    '<div class="alert alert-danger text-center">' +
                    '<i class="fa fa-exclamation-triangle"></i> ' +
                    'Terjadi kesalahan saat memuat data item. ' +
                    '<button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="loadDetailItems(' + id_pembelian + (id_penerimaan ? ', ' + id_penerimaan : '') + ')">' +
                    '<i class="fa fa-sync"></i> Coba Lagi' +
                    '</button>' +
                    '</div>'
                );
            }
        });
    }
</script>