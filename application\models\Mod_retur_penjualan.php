<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Retur Penjualan
 * Mengatur data retur penjualan dan detailnya
 */
class Mod_retur_penjualan extends CI_Model
{
    var $table = 'retur_penjualan';
    var $table_detail = 'retur_penjualan_detail';
    var $column_search = array(
        'r.nomor_retur', 
        'r.tanggal_retur', 
        'pel.nama as nama_pelanggan', 
        'r.status', 
        'r.alasan_retur',
        'r.keterangan'
    );
    var $column_order = array(
        'r.id', 
        'r.nomor_retur', 
        'r.tanggal_retur', 
        'fp.nomor_faktur',
        'p.nomor_pengiriman',
        'pel.nama', 
        'r.status', 
        'r.total_item',
        'r.total_qty',
        'r.total_nilai'
    );
    var $order = array('r.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            r.id,
            r.nomor_retur,
            r.tanggal_retur,
            r.id_faktur,
            r.id_pengiriman,
            r.id_pelanggan,
            r.alasan_retur,
            r.status,
            r.total_item,
            r.total_qty,
            r.total_nilai,
            r.keterangan,
            r.created_by,
            r.updated_by,
            r.created_at,
            r.updated_at,
            pel.nama as nama_pelanggan,
            pel.kode as kode_pelanggan,
            fp.nomor_faktur,
            p.nomor_pengiriman
        ');
        $this->db->from($this->table . ' r');
        $this->db->join('pelanggan pel', 'r.id_pelanggan = pel.id', 'left');
        $this->db->join('faktur_penjualan fp', 'r.id_faktur = fp.id', 'left');
        $this->db->join('pengiriman p', 'r.id_pengiriman = p.id', 'left');
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->select('
            r.*,
            pel.nama as nama_pelanggan,
            pel.kode as kode_pelanggan,
            pel.alamat as alamat_pelanggan,
            pel.no_telepon,
            pel.email,
            fp.nomor_faktur,
            fp.tanggal_faktur,
            p.nomor_pengiriman,
            p.tanggal_pengiriman
        ');
        $this->db->from($this->table . ' r');
        $this->db->join('pelanggan pel', 'r.id_pelanggan = pel.id', 'left');
        $this->db->join('faktur_penjualan fp', 'r.id_faktur = fp.id', 'left');
        $this->db->join('pengiriman p', 'r.id_pengiriman = p.id', 'left');
        $this->db->where('r.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    // Method konsisten dengan modul lain
    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
        return $this->db->affected_rows();
    }

    public function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }

    // Detail retur functions
    public function get_detail_by_retur_id($id_retur)
    {
        $this->db->select('
            rd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.kode_satuan,
            g.nama_gudang
        ');
        $this->db->from($this->table_detail . ' rd');
        $this->db->join('barang b', 'rd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'rd.id_gudang = g.id', 'left');
        $this->db->where('rd.id_retur', $id_retur);
        $this->db->order_by('rd.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    public function update_detail($where, $data)
    {
        $this->db->update($this->table_detail, $data, $where);
        return $this->db->affected_rows();
    }

    public function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    public function delete_detail_by_retur($id_retur)
    {
        $this->db->where('id_retur', $id_retur);
        $this->db->delete($this->table_detail);
    }

    // Generate nomor retur - Format: RPJ-YYYYMMDD-XXXX (Retur Penjualan)
    public function generate_nomor_retur()
    {
        $prefix = 'RPJ';
        $date = date('Ymd');
        
        $this->db->select('nomor_retur');
        $this->db->from($this->table);
        $this->db->like('nomor_retur', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_retur', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_retur;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Method untuk mendapatkan data dropdown
    public function get_pelanggan_aktif()
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('pelanggan');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_faktur_aktif()
    {
        $this->db->select('fp.id, fp.nomor_faktur, fp.tanggal_faktur, fp.id_pelanggan, fp.status, fp.status_pembayaran, pel.nama as nama_pelanggan');
        $this->db->from('faktur_penjualan fp');
        $this->db->join('pelanggan pel', 'fp.id_pelanggan = pel.id', 'left');
        $this->db->where_in('fp.status_pembayaran', ['lunas', 'sebagian']);
        $this->db->where('fp.status', 'final');
        $this->db->order_by('fp.tanggal_faktur', 'desc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pengiriman_aktif()
    {
        $this->db->select('p.id, p.nomor_pengiriman, p.tanggal_pengiriman, p.id_pelanggan, p.status, pel.nama as nama_pelanggan');
        $this->db->from('pengiriman p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->where_in('p.status', ['delivered']);
        $this->db->order_by('p.tanggal_pengiriman', 'desc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_barang_aktif()
    {
        $this->db->select('b.id, b.kode_barang, b.nama_barang, b.merk, b.tipe, b.harga_jual, s.nama_satuan');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('b.aktif', 1);
        $this->db->order_by('b.nama_barang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_gudang_aktif()
    {
        $this->db->select('id, kode_gudang, nama_gudang, alamat');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_detail_by_id($id)
    {
        $this->db->select('*');
        $this->db->from($this->table_detail);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_faktur_by_id($id)
    {
        $this->db->select('fp.*, pel.nama as nama_pelanggan, pel.alamat as alamat_pelanggan');
        $this->db->from('faktur_penjualan fp');
        $this->db->join('pelanggan pel', 'fp.id_pelanggan = pel.id', 'left');
        $this->db->where('fp.id', $id);
        $query = $this->db->get();
        
        // Log query untuk debugging
        log_message('debug', 'Query get_faktur_by_id: ' . $this->db->last_query());
        
        return $query->row();
    }

    public function get_pengiriman_by_id($id)
    {
        $this->db->select('p.*, pel.nama as nama_pelanggan, pel.alamat as alamat_pelanggan');
        $this->db->from('pengiriman p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->where('p.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_faktur_detail($id_faktur)
    {
        $this->db->select('
            fd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan
        ');
        $this->db->from('faktur_penjualan_detail fd');
        $this->db->join('barang b', 'fd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('fd.id_faktur', $id_faktur);
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pengiriman_detail($id_pengiriman)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            g.nama_gudang
        ');
        $this->db->from('pengiriman_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'pd.id_gudang = g.id', 'left');
        $this->db->where('pd.id_pengiriman', $id_pengiriman);
        $query = $this->db->get();
        return $query->result();
    }

    // Mendapatkan barang yang sudah dikirim dan belum diretur
    public function get_items_for_return($id_pengiriman, $id_retur = null)
    {
        $this->db->select('
            pd.id,
            pd.id_pengiriman,
            pd.id_barang,
            pd.id_gudang,
            pd.qty_dikirim,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.id as id_satuan,
            g.nama_gudang,
            COALESCE(SUM(rd.qty_retur), 0) as qty_sudah_diretur
        ');
        $this->db->from('pengiriman_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'pd.id_gudang = g.id', 'left');
        $this->db->join('retur_penjualan_detail rd', 'pd.id_barang = rd.id_barang AND pd.id_gudang = rd.id_gudang', 'left');
        $this->db->join('retur_penjualan r', 'rd.id_retur = r.id AND r.id_pengiriman = pd.id_pengiriman', 'left');
        
        // Exclude items from current return if editing
        if ($id_retur) {
            $this->db->where('(rd.id_retur IS NULL OR rd.id_retur = ' . $id_retur . ')');
        }
        
        $this->db->where('pd.id_pengiriman', $id_pengiriman);
        $this->db->group_by('pd.id, pd.id_pengiriman, pd.id_barang, pd.id_gudang, pd.qty_dikirim, b.kode_barang, b.nama_barang, b.merk, b.tipe, s.nama_satuan, s.id, g.nama_gudang');
        $this->db->having('pd.qty_dikirim > qty_sudah_diretur');
        
        $query = $this->db->get();
        return $query->result();
    }

    // Mendapatkan barang yang sudah difaktur dan belum diretur
    public function get_items_from_faktur($id_faktur, $id_retur = null)
    {
        // Versi sederhana tanpa pengecekan retur sebelumnya
        $this->db->select('
            fd.id,
            fd.id_faktur_penjualan as id_faktur,
            fd.id_barang,
            fd.qty as qty_faktur,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.id as id_satuan,
            fd.harga_satuan,
            0 as qty_sudah_diretur
        ');
        $this->db->from('faktur_penjualan_detail fd');
        $this->db->join('barang b', 'fd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        
        $this->db->where('fd.id_faktur_penjualan', $id_faktur);
        
        $query = $this->db->get();
        
        // Log query untuk debugging
        log_message('debug', 'Query get_items_from_faktur: ' . $this->db->last_query());
        
        return $query->result();
    }

    // Mendapatkan gudang dengan stok untuk barang tertentu
    public function get_warehouses_with_stock($id_barang)
    {
        $this->db->select('
            g.id,
            g.kode_gudang,
            g.nama_gudang,
            COALESCE(s.qty_terakhir, 0) as stok_tersedia
        ');
        $this->db->from('gudang g');
        $this->db->join('stok s', 'g.id = s.id_gudang AND s.id_barang = ' . $id_barang, 'left');
        $this->db->where('g.aktif', 1);
        $this->db->having('stok_tersedia > 0');
        $this->db->order_by('g.nama_gudang', 'asc');
        
        $query = $this->db->get();
        return $query->result();
    }
}