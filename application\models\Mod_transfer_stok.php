<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Transfer Stok
 * Mengatur data transfer stok dan detailnya
 */
class Mod_transfer_stok extends CI_Model
{
    var $table = 'transfer_stok';
    var $table_detail = 'transfer_stok_detail';
    var $column_search = array(
        'ts.nomor_transfer', 
        'ts.tanggal_transfer', 
        'ga.nama_gudang', 
        'gt.nama_gudang', 
        'ts.status', 
        'ts.keterangan'
    );
    var $column_order = array(
        'ts.id', 
        'ts.nomor_transfer', 
        'ts.tanggal_transfer', 
        'ga.nama_gudang', 
        'gt.nama_gudang', 
        'ts.status', 
        'ts.total_item',
        'ts.total_qty'
    );
    var $order = array('ts.id' => 'desc');

    public $Mod_penyesuaian_stok;

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            ts.id,
            ts.nomor_transfer,
            ts.tanggal_transfer,
            ts.gudang_asal_id,
            ts.gudang_tujuan_id,
            ts.status,
            ts.total_item,
            ts.total_qty,
            ts.keterangan,
            ts.tanggal_kirim,
            ts.tanggal_terima,
            ts.dibuat_oleh,
            ts.dikirim_oleh,
            ts.diterima_oleh,
            ts.dibuat_pada,
            ga.kode_gudang as kode_gudang_asal,
            ga.nama_gudang as nama_gudang_asal,
            gt.kode_gudang as kode_gudang_tujuan,
            gt.nama_gudang as nama_gudang_tujuan
        ');
        $this->db->from('transfer_stok ts');
        $this->db->join('gudang ga', 'ts.gudang_asal_id = ga.id', 'left');
        $this->db->join('gudang gt', 'ts.gudang_tujuan_id = gt.id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from('transfer_stok ts');
        $this->db->join('gudang ga', 'ts.gudang_asal_id = ga.id', 'left');
        $this->db->join('gudang gt', 'ts.gudang_tujuan_id = gt.id', 'left');
        return $this->db->count_all_results();
    }

    // Insert header transfer
    function insert($data)
    {
        $insert = $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // Update header transfer
    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    // Get header transfer
    function get($id)
    {
        $this->db->select('
            ts.*,
            ga.kode_gudang as kode_gudang_asal,
            ga.nama_gudang as nama_gudang_asal,
            gt.kode_gudang as kode_gudang_tujuan,
            gt.nama_gudang as nama_gudang_tujuan
        ');
        $this->db->from('transfer_stok ts');
        $this->db->join('gudang ga', 'ts.gudang_asal_id = ga.id', 'left');
        $this->db->join('gudang gt', 'ts.gudang_tujuan_id = gt.id', 'left');
        $this->db->where('ts.id', $id);
        return $this->db->get()->row();
    }

    // Delete header transfer (cascade delete detail)
    function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Generate nomor transfer otomatis - Format: TSK-YYYYMMDD-XXXX (Transfer Stok)
    function generate_nomor()
    {
        $prefix = 'TSK';
        $date = date('Ymd');
        
        $this->db->select('nomor_transfer');
        $this->db->from($this->table);
        $this->db->like('nomor_transfer', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_transfer', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_nomor = $query->row()->nomor_transfer;
            $last_sequence = (int)substr($last_nomor, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Cek apakah nomor sudah ada
    function check_nomor_exists($nomor, $id = null)
    {
        $this->db->where('nomor_transfer', $nomor);
        if ($id) {
            $this->db->where('id !=', $id);
        }
        $query = $this->db->get($this->table);
        return $query->num_rows() > 0;
    }

    // Get dropdown gudang
    function get_gudang_dropdown()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // ===== DETAIL TRANSFER METHODS =====

    // Get detail transfer
    function get_detail($id_transfer)
    {
        $this->db->select('
            tsd.*,
            b.kode_barang,
            b.nama_barang,
            s.kode_satuan,
            s.nama_satuan
        ');
        $this->db->from('transfer_stok_detail tsd');
        $this->db->join('barang b', 'tsd.barang_id = b.id', 'left');
        $this->db->join('satuan s', 'tsd.satuan_id = s.id', 'left');
        $this->db->where('tsd.transfer_stok_id', $id_transfer);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Insert detail transfer
    function insert_detail($data)
    {
        return $this->db->insert($this->table_detail, $data);
    }

    // Update detail transfer
    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    // Delete detail transfer
    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    // Delete all detail by transfer_stok_id
    function delete_detail_by_transfer($transfer_stok_id)
    {
        $this->db->where('transfer_stok_id', $transfer_stok_id);
        $this->db->delete($this->table_detail);
    }

    // Get single detail
    function get_detail_by_id($id)
    {
        $this->db->select('
            tsd.*,
            b.kode_barang,
            b.nama_barang,
            s.kode_satuan,
            s.nama_satuan
        ');
        $this->db->from('transfer_stok_detail tsd');
        $this->db->join('barang b', 'tsd.barang_id = b.id', 'left');
        $this->db->join('satuan s', 'tsd.satuan_id = s.id', 'left');
        $this->db->where('tsd.id', $id);
        return $this->db->get()->row();
    }

    // Get stok barang di gudang
    function get_stok_barang($id_barang, $id_gudang)
    {
        $this->db->select('qty_terakhir');
        $this->db->from('stok_barang');
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $result = $this->db->get()->row();
        
        return $result ? $result->qty_terakhir : 0;
    }

    // Get barang yang tersedia di gudang asal
    function get_barang_available($id_transfer)
    {
        // Get gudang asal dari transfer
        $transfer = $this->get($id_transfer);
        if (!$transfer) {
            return array();
        }

        // Get barang yang memiliki stok di gudang asal
        $this->db->select('
            b.id,
            b.kode_barang,
            b.nama_barang,
            b.satuan_id,
            s.nama_satuan,
            COALESCE(SUM(sb.qty_terakhir), 0) as stok
        ');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('stok_barang sb', 'b.id = sb.id_barang AND sb.id_gudang = ' . $transfer->gudang_asal_id, 'left');
        $this->db->where('b.aktif', 1);
        $this->db->group_by('b.id');
        $this->db->having('stok > 0');
        $this->db->order_by('b.nama_barang', 'ASC');
        
        return $this->db->get()->result();
    }

    // Update status transfer
    function update_status($id, $status, $user = null)
    {
        $data = array('status' => $status);
        
        if ($status == 'dikirim') {
            $data['tanggal_kirim'] = date('Y-m-d H:i:s');
            $data['dikirim_oleh'] = $user;
        } else if ($status == 'diterima') {
            $data['tanggal_terima'] = date('Y-m-d H:i:s');
            $data['diterima_oleh'] = $user;
        }
        
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
        
        // Tambahan: Catat ke stok_movement
        if ($this->db->affected_rows() > 0) {
            // Load model penyesuaian stok jika belum
            if (!isset($this->Mod_penyesuaian_stok)) {
                $CI =& get_instance();
                $CI->load->model('Mod_penyesuaian_stok');
            }
            $transfer = $this->get($id);
            $detail_list = $this->get_detail($id);
            if ($status == 'dikirim') {
                // Kurangi stok di gudang asal
                foreach ($detail_list as $detail) {
                    $data_movement = array(
                        'id_barang' => $detail->barang_id,
                        'id_gudang' => $transfer->gudang_asal_id,
                        'tanggal' => date('Y-m-d H:i:s'),
                        'tipe_transaksi' => 'transfer_keluar',
                        'qty_in' => 0,
                        'qty_out' => $detail->qty,
                        'keterangan' => 'Transfer Stok Dikirim',
                        'ref_transaksi' => $transfer->nomor_transfer,
                        'user_input' => $user,
                        'created_at' => date('Y-m-d H:i:s'),
                    );
                    $CI->Mod_penyesuaian_stok->insert_stok_movement($data_movement);
                }
            } else if ($status == 'diterima') {
                // Tambah stok di gudang tujuan
                foreach ($detail_list as $detail) {
                    $data_movement = array(
                        'id_barang' => $detail->barang_id,
                        'id_gudang' => $transfer->gudang_tujuan_id,
                        'tanggal' => date('Y-m-d H:i:s'),
                        'tipe_transaksi' => 'transfer_masuk',
                        'qty_in' => $detail->qty,
                        'qty_out' => 0,
                        'keterangan' => 'Transfer Stok Diterima',
                        'ref_transaksi' => $transfer->nomor_transfer,
                        'user_input' => $user,
                        'created_at' => date('Y-m-d H:i:s'),
                    );
                    $CI->Mod_penyesuaian_stok->insert_stok_movement($data_movement);
                }
            }
        }
        return $this->db->affected_rows() > 0;
    }

    // Cek apakah transfer bisa diupdate statusnya
    function can_update_status($id, $new_status)
    {
        $this->db->select('status');
        $this->db->from($this->table);
        $this->db->where('id', $id);
        $transfer = $this->db->get()->row();
        
        if (!$transfer) return false;
        
        // Validasi alur status
        switch ($transfer->status) {
            case 'draft':
                return $new_status == 'dikirim' || $new_status == 'batal';
            case 'dikirim':
                return $new_status == 'diterima' || $new_status == 'batal';
            default:
                return false;
        }
    }
}