# COA Integration Guide

## Overview
Panduan lengkap untuk integrasi otomatis Chart of Accounts (COA) dengan modul operasional pada aplikasi toko elektronik.

## Features Implemented

### 1. Auto Journal untuk Penjualan
**Trigger Point:** Ketika status faktur penjualan berubah menjadi 'final'
**Location:** `application/controllers/FakturPenjualan.php` - method `update_status()`

**Journal Entries Created:**
- **Penjualan Kredit:**
  - Dr. <PERSON> (1-1200) 
  - <PERSON>r. <PERSON> (4-1100)
- **Penjualan Tunai:**
  - Dr. <PERSON> (1-1101)
  - <PERSON>r. <PERSON> (4-1100)
- **Cost of Goods Sold:**
  - Dr. <PERSON>/HPP (5-1100)
  - <PERSON>r. <PERSON><PERSON><PERSON> (1-1500)

### 2. Auto Journal untuk Pembelian
**Trigger Point:** Ketika status penerimaan pembelian berubah menjadi 'diterima'
**Location:** `application/controllers/PenerimaanPembelian.php` - method `update_status()`

**Journal Entries Created:**
- **Pembelian Kredit:**
  - Dr<PERSON> (1-1500)
  - <PERSON>r. <PERSON> (2-1100)
- **Pembelian Tunai:**
  - Dr. Persediaan (1-1500)
  - Cr. Kas (1-1101)

### 3. Auto Journal untuk Pembayaran Penjualan
**Trigger Point:** Ketika pembayaran faktur penjualan ditambahkan
**Location:** `application/controllers/FakturPenjualan.php` - method `add_payment()`

**Journal Entries Created:**
- Dr. Kas (1-1101)
- Cr. Piutang Usaha (1-1200)

### 4. Auto Journal untuk Pembayaran Pembelian
**Trigger Point:** Ketika pembayaran pembelian ditambahkan
**Location:** `application/controllers/Pembelian.php` - method `save_pembayaran()`

**Journal Entries Created:**
- Dr. Hutang Usaha (2-1100)
- Cr. Kas (1-1101)

### 5. Auto Journal untuk Penyesuaian Inventory
**Trigger Point:** Ketika stok opname difinalisasi
**Location:** `application/controllers/StokOpname.php` - method `finalize()`

**Journal Entries Created:**
- **Penyesuaian Positif (Kelebihan Stok):**
  - Dr. Persediaan (1-1500)
  - Cr. Pendapatan Lainnya (4-2400)
- **Penyesuaian Negatif (Kekurangan Stok):**
  - Dr. Beban Lainnya (5-4500)
  - Cr. Persediaan (1-1500)

## Files Structure

### Core Integration Files
```
application/
├── libraries/
│   └── Coa_integration.php          # Main integration library
├── models/
│   └── Mod_coa.php                  # Enhanced COA model with auto journal methods
├── controllers/
│   ├── Coa_setup.php               # Setup and testing controller
│   ├── FakturPenjualan.php         # Enhanced with sales auto journal
│   ├── PenerimaanPembelian.php     # Enhanced with purchase auto journal
│   ├── Pembelian.php               # Enhanced with payment auto journal
│   └── StokOpname.php              # Enhanced with inventory adjustment
└── views/
    └── coa/
        └── setup.php               # Setup interface
```

### Database Files
```
DB/
├── coa_additional_accounts.sql     # Additional COA accounts
└── toko_elektronik.sql            # Main database with COA tables
```

## Setup Instructions

### 1. Database Setup
1. Pastikan database sudah memiliki tabel COA yang lengkap
2. Jalankan script additional accounts:
   ```sql
   -- Run this file
   DB/coa_additional_accounts.sql
   ```

### 2. Application Setup
1. Akses halaman setup: `/coa_setup`
2. Klik "Setup Additional Accounts" untuk menambah akun COA tambahan
3. Klik "Test Integration" untuk test integrasi
4. Klik "Check Account Balances" untuk cek saldo akun

### 3. Configuration
Library `Coa_integration` akan otomatis dimuat ketika diperlukan. Tidak perlu konfigurasi tambahan.

## Additional COA Accounts

### Detailed Inventory Accounts
- `1-1501` - Persediaan Handphone
- `1-1502` - Persediaan Laptop  
- `1-1503` - Persediaan Aksesoris
- `1-1504` - Persediaan Spare Part

### Detailed Receivable Accounts
- `1-1201` - Piutang Usaha - Retail
- `1-1202` - Piutang Usaha - Grosir
- `1-1203` - Piutang Usaha - Online

### Detailed Payable Accounts
- `2-1101` - Hutang Usaha - Supplier Utama
- `2-1102` - Hutang Usaha - Supplier Lokal
- `2-1103` - Hutang Usaha - Importir

### Additional Revenue Accounts
- `4-1400` - Pendapatan Service
- `4-1500` - Pendapatan Instalasi
- `4-1600` - Pendapatan Konsultasi

### Additional Expense Accounts
- `5-2950` - Beban Pengiriman
- `5-2960` - Beban Garansi
- `5-2970` - Beban Retur
- `5-2980` - Beban E-commerce
- `5-2990` - Beban Payment Gateway

### E-Wallet Accounts
- `1-1108` - E-Wallet OVO
- `1-1109` - E-Wallet GoPay
- `1-1110` - E-Wallet DANA

## Testing

### Manual Testing
1. **Test Sales Integration:**
   - Buat faktur penjualan baru
   - Ubah status menjadi 'final'
   - Cek jurnal otomatis di modul COA

2. **Test Purchase Integration:**
   - Buat pembelian baru
   - Buat penerimaan pembelian
   - Ubah status penerimaan menjadi 'diterima'
   - Cek jurnal otomatis di modul COA

3. **Test Payment Integration:**
   - Tambah pembayaran pada faktur penjualan
   - Tambah pembayaran pada pembelian
   - Cek jurnal otomatis di modul COA

4. **Test Inventory Adjustment:**
   - Buat stok opname baru
   - Tambah detail dengan selisih qty
   - Finalisasi stok opname
   - Cek jurnal otomatis di modul COA

### Automated Testing
Gunakan controller `Coa_setup` method `test_integration()` untuk test otomatis.

## Troubleshooting

### Common Issues

1. **Journal tidak terbuat otomatis**
   - Cek log aplikasi di `application/logs/`
   - Pastikan library `Coa_integration` dapat dimuat
   - Pastikan akun COA yang diperlukan sudah ada

2. **Error saat setup additional accounts**
   - Pastikan user memiliki akses admin (level 1)
   - Cek apakah file SQL dapat diakses
   - Pastikan tidak ada duplikasi kode akun

3. **Saldo tidak seimbang**
   - Cek validasi debit = kredit di method `validate_journal_balance()`
   - Pastikan semua entry jurnal memiliki pasangan yang benar

### Log Monitoring
Semua aktivitas auto journal dicatat di log dengan format:
```
INFO - Auto journal created for sales: FAKTUR-001 - Sales Journal ID: 123 - COGS Journal ID: 124
ERROR - Failed to create auto journal for sales: FAKTUR-001 - Error: Account not found
```

## Best Practices

1. **Backup Database** sebelum menjalankan setup
2. **Monitor Log** secara berkala untuk memastikan integrasi berjalan lancar
3. **Validasi Saldo** secara berkala menggunakan laporan neraca saldo
4. **Test Integration** setelah update aplikasi
5. **Review Journal Entries** yang dibuat otomatis untuk memastikan akurasi

## Future Enhancements

1. **Multi-currency Support** untuk transaksi USD
2. **Advanced COGS Calculation** dengan FIFO/LIFO
3. **Automatic Period Closing** untuk tutup buku bulanan
4. **Integration with Tax Calculation** untuk PPN otomatis
5. **Cash Flow Statement** generation otomatis
