<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Buku Besar',
    'document_number' => 'BB-' . date('Ymd-His'),
    'document_title' => 'BUKU BESAR'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Laporan',
        'items' => [
            ['label' => 'Akun', 'value' => '<strong>' . ($nama_akun ?? 'Semua Akun') . '</strong>'],
            ['label' => 'Kode Akun', 'value' => $kode_akun ?? '-'],
            ['label' => 'Periode', 'value' => format_date_indonesia($tanggal_awal) . ' s/d ' . format_date_indonesia($tanggal_akhir)],
            ['label' => 'Tanggal Cetak', 'value' => format_date_indonesia(date('Y-m-d'), true)]
        ]
    ]
];

// Prepare table headers
$table_headers = [
    ['label' => 'Tanggal', 'width' => '10%'],
    ['label' => 'No. Transaksi', 'width' => '15%'],
    ['label' => 'Keterangan', 'width' => '35%'],
    ['label' => 'Referensi', 'width' => '15%'],
    ['label' => 'Debit', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Kredit', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Saldo', 'width' => '12%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$saldo_berjalan = $saldo_awal ?? 0;
$total_debit = 0;
$total_kredit = 0;

// Add opening balance row if exists
if (isset($saldo_awal) && $saldo_awal != 0) {
    $table_data[] = [
        format_date_indonesia($tanggal_awal),
        '-',
        '<strong>SALDO AWAL</strong>',
        '-',
        '-',
        '-',
        '<strong>' . format_currency($saldo_awal) . '</strong>'
    ];
}

if (!empty($transaksi_list)) {
    foreach ($transaksi_list as $transaksi) {
        $saldo_berjalan += ($transaksi->debit - $transaksi->kredit);
        
        $debit_display = $transaksi->debit > 0 ? format_currency($transaksi->debit) : '-';
        $kredit_display = $transaksi->kredit > 0 ? format_currency($transaksi->kredit) : '-';
        
        $table_data[] = [
            format_date_indonesia($transaksi->tanggal_transaksi),
            $transaksi->nomor_transaksi,
            $transaksi->keterangan ?: '-',
            $transaksi->referensi ?: '-',
            $debit_display,
            $kredit_display,
            format_currency($saldo_berjalan)
        ];
        
        $total_debit += $transaksi->debit;
        $total_kredit += $transaksi->kredit;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL MUTASI:</strong>', 'colspan' => '4', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_debit) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_kredit) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($saldo_berjalan) . '</strong>', 'align' => 'right']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-money"></span>Mutasi Buku Besar', $table_headers, $table_data, $table_options);

// Add summary section
$content .= '<div class="summary-section">';
$content .= '<div class="summary-left">';

$analysis_content = 'Ringkasan Mutasi:' . "\n";
$analysis_content .= '• Saldo Awal: ' . format_currency($saldo_awal ?? 0) . "\n";
$analysis_content .= '• Total Debit: ' . format_currency($total_debit) . "\n";
$analysis_content .= '• Total Kredit: ' . format_currency($total_kredit) . "\n";
$analysis_content .= '• Mutasi Bersih: ' . format_currency($total_debit - $total_kredit) . "\n";
$analysis_content .= '• Saldo Akhir: ' . format_currency($saldo_berjalan);

$content .= create_notes_section('Analisis Mutasi', $analysis_content);

$content .= '</div>';
$content .= '<div class="summary-right">';

// Summary table
$summary_items = [
    ['label' => 'Saldo Awal', 'value' => format_currency($saldo_awal ?? 0)],
    ['label' => 'Total Debit', 'value' => format_currency($total_debit)],
    ['label' => 'Total Kredit', 'value' => format_currency($total_kredit)],
    ['label' => 'Mutasi Bersih', 'value' => format_currency($total_debit - $total_kredit)],
    ['label' => 'Saldo Akhir', 'value' => format_currency($saldo_berjalan), 'class' => 'total-final']
];

$content .= create_summary_table($summary_items);

$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => '(............................)',
        'position' => 'Staff Accounting'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Accounting'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Manager Finance'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>