<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-file-invoice text-blue"></i> Data Faktur Penjualan</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <?= $this->session->flashdata('pesan') ?>
                        <table id="tbl_faktur" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Faktur</th>
                                    <th>Tanggal</th>
                                    <th>No. Pengiriman</th>
                                    <th><PERSON><PERSON>nggan</th>
                                    <th>Status</th>
                                    <th>Pembayaran</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Total Faktur</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Header -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Faktur Penjualan</h4>
                <button type="button" class="close" onclick="closePaymentModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="modal_detail" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div class="modal fade" id="modal-hapus" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Hapus</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus faktur penjualan ini?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closePaymentModal()">Batal</button>
                <button type="button" class="btn btn-danger" id="btn-hapus">Hapus</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Pembayaran -->
<div class="modal fade" id="modal_payment" tabindex="-1" role="dialog" aria-labelledby="modal_payment_label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal_payment_label">Tambah Pembayaran</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form_payment">
                    <input type="hidden" name="id_faktur_penjualan" id="payment_faktur_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Total Faktur:</label>
                                <div class="font-weight-bold" id="total_faktur_display">Rp 0</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Sisa Pembayaran:</label>
                                <div class="font-weight-bold" id="remaining_display">Rp 0</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="tanggal_pembayaran">Tanggal Pembayaran</label>
                        <input type="date" class="form-control" id="tanggal_pembayaran" name="tanggal_pembayaran" value="<?= date('Y-m-d') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="jumlah_pembayaran">Jumlah Pembayaran</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">Rp</span>
                            </div>
                            <input type="number" class="form-control" id="jumlah_pembayaran" name="jumlah_pembayaran" min="0" step="1" required>
                        </div>
                        <div class="btn-group btn-group-sm mt-2 w-100">
                            <button type="button" class="btn btn-outline-secondary" onclick="setPaymentAmount('50percent')">50%</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setPaymentAmount('remaining')">Sisa</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setPaymentAmount('all')">Semua</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="metode_pembayaran">Metode Pembayaran</label>
                        <select class="form-control" id="metode_pembayaran" name="metode_pembayaran" required>
                            <option value="tunai">Tunai</option>
                            <option value="transfer">Transfer Bank</option>
                            <option value="kartu_debit">Kartu Debit</option>
                            <option value="kartu_kredit">Kartu Kredit</option>
                            <option value="cek">Cek</option>
                            <option value="lainnya">Lainnya</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="keterangan">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closePaymentModal()">Batal</button>
                <button type="button" class="btn btn-primary" id="btnSavePayment" onclick="savePayment()">Simpan</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Fix untuk modal nested dan scroll behavior */
.modal.show {
    overflow-y: auto !important;
}

.modal-dialog {
    margin: 30px auto;
}

/* Pastikan modal detail memiliki z-index yang tepat */
#modal_detail {
    z-index: 1050;
}

#modal_payment {
    z-index: 1060;
}

#modal_form_detail_item {
    z-index: 1070;
}

/* Fix backdrop untuk nested modals */
.modal-backdrop.modal-stack {
    z-index: 1040;
}

/* Pastikan body tetap scrollable saat modal terbuka */
body.modal-open {
    overflow: hidden;
    padding-right: 0 !important;
}

/* Fix untuk modal yang overlap */
.modal.fade.show {
    display: block !important;
}
</style>

<script>
    var table;
    var save_method;
    var id_faktur;
    var current_faktur_id; // Global variable untuk menyimpan ID faktur yang sedang dibuka di modal

    $(document).ready(function() {
        $('.select2').select2();
        
        // Fix scroll behavior untuk modal detail
        $('#modal_detail').on('shown.bs.modal', function() {
            $(this).css('overflow-y', 'auto');
            $('body').addClass('modal-open');
        });
        
        // Pastikan scroll tetap berfungsi saat modal detail dibuka kembali
         $('#modal_detail').on('hidden.bs.modal', function() {
             // Reset any lingering modal states
             $('.modal-backdrop').remove();
             $('body').removeClass('modal-open');
         });
        
        //datatables
        table = $("#tbl_faktur").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Faktur Penjualan Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('FakturPenjualan/ajax_list') ?>",
                "type": "POST"
            },

            //Set column definition initialisation properties.
            "columnDefs": [{
                "targets": [-1], //last column
                "orderable": false, //set not orderable
            }],
        });
    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Tambah Faktur Penjualan'); // set a title to modal header

        // Load form add
        $.ajax({
            url: "<?php echo site_url('FakturPenjualan/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show'); // show bootstrap modal after loading form
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Edit Faktur Penjualan'); // set a title to modal header

        // Load form edit
        $.ajax({
            url: "<?php echo site_url('FakturPenjualan/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);

                // Load data untuk edit
                $.ajax({
                    url: "<?php echo site_url('FakturPenjualan/edit') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_faktur"]').val(data.nomor_faktur);
                        $('[name="tanggal_faktur"]').val(data.tanggal_faktur);
                        $('[name="jatuh_tempo"]').val(data.jatuh_tempo);
                        $('[name="keterangan"]').val(data.keterangan);
                        
                        // Disable pengiriman selection on edit
                        $('#id_pengiriman').prop('disabled', true);
                        
                        // Show pengiriman info
                        $('#pengiriman_info').html(`
                            <div class="alert alert-info">
                                <strong>Pengiriman:</strong> ${data.nomor_pengiriman} <br>
                                <strong>Tanggal:</strong> ${formatDate(data.tanggal_pengiriman)} <br>
                                <strong>Pelanggan:</strong> ${data.nama_pelanggan} (${data.kode_pelanggan})
                            </div>
                        `);

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat mengambil data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function detail(id) {
        current_faktur_id = id;
        
        // Close any existing modals first
        $('.modal').modal('hide');
        
        // Small delay to ensure modal is properly closed
        setTimeout(function() {
            $('#modal_detail').modal({
                backdrop: 'static',
                keyboard: false
            });
            $('#modal_detail').modal('show');
            $('#modal_detail .modal-content').html('<div class="modal-body text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

            $.ajax({
                url: "<?php echo site_url('FakturPenjualan/detail_modal') ?>/" + id,
                type: "GET",
                dataType: "HTML",
                timeout: 30000, // 30 second timeout
                success: function(data) {
                    $('#modal_detail .modal-content').html(data);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log('Detail Modal Error:', textStatus, errorThrown);
                    var errorMsg = '<div class="modal-body text-center text-danger">';
                    errorMsg += '<i class="fa fa-exclamation-triangle"></i> ';
                    errorMsg += 'Error loading detail: ' + (errorThrown || textStatus);
                    errorMsg += '<br><button class="btn btn-secondary mt-2" onclick="$(\"#modal_detail\").modal(\"hide\")">Tutup</button>';
                    errorMsg += '</div>';
                    $('#modal_detail .modal-content').html(errorMsg);
                }
            });
        }, 100);
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('FakturPenjualan/insert') ?>";
        } else {
            url = "<?php echo site_url('FakturPenjualan/update') ?>";
        }

        // ajax adding data to database
        var formData = new FormData($('#form')[0]);
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload();
                    
                    Swal.fire({
                        title: 'Data faktur berhasil disimpan!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                    
                    // Reinisialisasi Select2 setelah validasi gagal
                    $('.select2').select2();
                    
                    // Show SweetAlert notification with detailed errors
                    var errorMessages = data.error_string.join('<br>');
                    Swal.fire({
                        title: 'Gagal Menyimpan!',
                        html: 'Terjadi kesalahan:<br>' + errorMessages,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            }
        });
    }

    function hapus(id) {
        id_faktur = id;
        $('#modal-hapus').modal('show');
    }

    $('#btn-hapus').click(function() {
        $.ajax({
            url: '<?= site_url('FakturPenjualan/delete') ?>',
            type: 'POST',
            data: {
                id: id_faktur
            },
            dataType: 'json',
            success: function(response) {
                $('#modal-hapus').modal('hide');
                if (response.status == 'success') {
                    Swal.fire({
                        title: 'Terhapus!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    table.ajax.reload();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function() {
                $('#modal-hapus').modal('hide');
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan sistem!',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    });

    function updateStatus(id, status) {
        var statusText = '';
        switch(status) {
            case 'final': statusText = 'difinalisasi'; break;
            case 'batal': statusText = 'dibatalkan'; break;
            default: statusText = status;
        }

        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin mengubah status faktur menjadi ' + statusText + '?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Ubah Status',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: '<?= site_url('FakturPenjualan/update_status') ?>',
                    type: 'POST',
                    data: {
                        id: id,
                        status: status
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status == 'success') {
                            Swal.fire({
                                title: 'Status faktur berhasil diubah!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                            table.ajax.reload();
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan sistem!',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function addPayment(id) {
        $('#payment_faktur_id').val(id);
        $('#jumlah_pembayaran').val('');
        $('#keterangan').val('');
        
        // Reset form validation states
        $('#form_payment input, #form_payment select, #form_payment textarea').removeClass('is-invalid');
        $('#form_payment .invalid-feedback').remove();
        
        // Get faktur details to display total and remaining amount
        $.ajax({
            url: "<?php echo site_url('FakturPenjualan/get_faktur_payment_info') ?>",
            type: "POST",
            data: {id: id},
            dataType: "JSON",
            timeout: 10000, // 10 second timeout
            success: function(data) {
                var totalFaktur = parseFloat(data.total_faktur);
                var totalPaid = parseFloat(data.total_paid);
                var remaining = totalFaktur - totalPaid;
                
                // Format currency
                $('#total_faktur_display').text('Rp ' + formatNumber(totalFaktur));
                $('#remaining_display').text('Rp ' + formatNumber(remaining));
                
                // Show modal after data is loaded
                $('#modal_payment').modal({
                    backdrop: 'static',
                    keyboard: false
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Error loading payment info:', textStatus, errorThrown);
                
                // Show modal even if data loading fails
                $('#total_faktur_display').text('Rp 0');
                $('#remaining_display').text('Rp 0');
                
                $('#modal_payment').modal({
                    backdrop: 'static',
                    keyboard: false
                });
                
                // Show error message
                Swal.fire({
                    title: 'Peringatan!',
                    text: 'Gagal memuat informasi faktur. Silakan masukkan jumlah pembayaran secara manual.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
    
    // Format number function untuk currency display
    function formatNumber(num) {
        if (isNaN(num) || num === null || num === undefined) {
            return '0';
        }
        return parseFloat(num).toLocaleString('id-ID', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
    }
    
    function setPaymentAmount(type) {
        var fakturId = $('#payment_faktur_id').val();
        
        // Get faktur details via AJAX
        $.ajax({
            url: "<?php echo site_url('FakturPenjualan/get_faktur_payment_info') ?>",
            type: "POST",
            data: {id: fakturId},
            dataType: "JSON",
            success: function(data) {
                var totalFaktur = parseFloat(data.total_faktur);
                var totalPaid = parseFloat(data.total_paid);
                var remaining = totalFaktur - totalPaid;
                
                // Format currency for display
                $('#total_faktur_display').text('Rp ' + formatNumber(totalFaktur));
                $('#remaining_display').text('Rp ' + formatNumber(remaining));
                
                if (type === "50percent") {
                    // Set to 50% of total
                    $('#jumlah_pembayaran').val((totalFaktur * 0.5).toFixed(2));
                } else if (type === "remaining") {
                    // Set to remaining amount
                    $('#jumlah_pembayaran').val(remaining.toFixed(2));
                } else if (type === "all") {
                    // Set to total amount
                    $('#jumlah_pembayaran').val(totalFaktur.toFixed(2));
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat mengambil data faktur.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            }
        });
    }
    
    function savePayment() {
        $('#btnSavePayment').text('saving...'); //change button text
        $('#btnSavePayment').attr('disabled', true); //set button disable 
        
        // ajax adding data to database
        var formData = new FormData($('#form_payment')[0]);
        $.ajax({
            url: "<?php echo site_url('FakturPenjualan/add_payment') ?>",
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(data) {
                if (data.status == 'success') {
                    $('#modal_payment').modal('hide');
                    table.ajax.reload();
                    
                    Swal.fire({
                        title: 'Pembayaran berhasil disimpan!',
                        text: data.message,
                        icon: 'success',
                        timer: 3000,
                        showConfirmButton: false
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="'+data.inputerror[i]+'"]').addClass('is-invalid');
                        $('[name="'+data.inputerror[i]+'"]').next().text(data.error_string[i]);
                    }
                }
                $('#btnSavePayment').text('Simpan'); //change button text
                $('#btnSavePayment').attr('disabled', false); //set button enable
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menyimpan data.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
                $('#btnSavePayment').text('Simpan'); //change button text
                $('#btnSavePayment').attr('disabled', false); //set button enable
            }
        });
    }
    
    // Reset form validation errors when modal is closed and fix scroll behavior
    $('#modal_payment').on('hidden.bs.modal', function (e) {
        // Reset form validation
        $('#form_payment input, #form_payment select, #form_payment textarea').removeClass('is-invalid');
        $('#form_payment .invalid-feedback').remove();
        
        // Prevent event propagation to parent modal
        e.stopPropagation();
        
        // Keep parent modal visible and scrollable if detail modal is open
        if ($('#modal_detail').hasClass('show')) {
            $('body').addClass('modal-open');
            
            // Ensure parent modal is still visible
            $('#modal_detail').css('display', 'block');
            
            // Fix backdrop if needed
            if ($('.modal-backdrop').length === 0) {
                $('body').append('<div class="modal-backdrop fade show"></div>');
                $('.modal-backdrop').css('z-index', parseInt($('#modal_detail').css('z-index')) - 1);
            }
            
            // Restore scroll capability to detail modal
             $('#modal_detail').css('overflow-y', 'auto');
         }
     });

    // Add validation feedback elements
    $('#form_payment input, #form_payment select, #form_payment textarea').each(function() {
        if (!$(this).next().hasClass('invalid-feedback')) {
            $(this).after('<div class="invalid-feedback"></div>');
        }
    });
    
    // Function to print faktur
    function printFaktur(id) {
        // Open print page in new window
        var printWindow = window.open('<?php echo site_url('FakturPenjualan/print/') ?>' + id, '_blank');
        printWindow.focus();
    }
    
    // Function to close payment modal with proper scroll behavior fix
    function closePaymentModal() {
        // Reset form pembayaran
        $('#form_payment')[0].reset();
        
        // Reset validation states
        $('#form_payment input, #form_payment select, #form_payment textarea').removeClass('is-invalid');
        $('#form_payment .invalid-feedback').remove();
        
        // Hide the modal - the hidden.bs.modal event will handle the rest
        $('#modal_payment').modal('hide');
    }
    
    // Function to refresh detail modal content without closing it
    function refreshDetailModal(id_faktur) {
        $.ajax({
            url: "<?= site_url('FakturPenjualan/detail_modal') ?>/" + id_faktur,
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                // Keep the modal open but update its content
                var $modalContent = $('#modal_detail .modal-content');
                $modalContent.html(data);
                
                // Update the main table in the background
                if (typeof table !== 'undefined') {
                    table.ajax.reload(null, false);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memperbarui data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
    
    // Function to delete payment - moved here to be globally accessible
    function deletePayment(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: "Apakah Anda yakin ingin menghapus pembayaran ini?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('FakturPenjualan/delete_payment') ?>",
                    type: "POST",
                    data: {
                        id: id
                    },
                    dataType: "JSON",
                    timeout: 30000, // 30 second timeout
                    success: function(data) {
                        if (data.status == 'success') {
                            Swal.fire({
                                title: 'Terhapus!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                // Update the detail modal content without closing it
                                if (typeof refreshDetailModal === 'function' && typeof current_faktur_id !== 'undefined') {
                                    refreshDetailModal(current_faktur_id);
                                }

                                // Refresh main table
                                if (typeof table !== 'undefined') {
                                    table.ajax.reload(null, false);
                                }
                            });
                        } else {
                            Swal.fire(
                                'Error!',
                                data.message,
                                'error'
                            );
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log('Delete Payment Error:', textStatus, errorThrown);
                        Swal.fire(
                            'Error!',
                            'Terjadi kesalahan saat menghapus data: ' + errorThrown,
                            'error'
                        );
                    }
                });
            }
        });
    }

    // Fungsi untuk menyimpan pembayaran dari detail modal
    function savePaymentFromDetail() {
        var btn = $('#btnSavePayment');
        btn.text('Menyimpan...');
        btn.attr('disabled', true);

        var formData = new FormData($('#form_payment')[0]);

        $.ajax({
            url: "<?= site_url('FakturPenjualan/add_payment') ?>",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            dataType: "json",
            timeout: 30000, // 30 second timeout
            success: function(response) {
                console.log('AJAX Success Response:', response);
                if (response.status == 'success') {
                    closePaymentModal();

                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        // Update the detail modal content without closing it
                        if (typeof refreshDetailModal === 'function') {
                            refreshDetailModal(current_faktur_id);
                        }

                        // Refresh main table
                        if (typeof table !== 'undefined') {
                            table.ajax.reload(null, false);
                        }
                    });
                } else {
                    console.log('Payment failed:', response);
                    var errorMessage = response.message || 'Terjadi kesalahan saat menyimpan data.';
                    Swal.fire({
                        title: 'Error!',
                        text: errorMessage,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', status, error);
                console.log('XHR Response:', xhr.responseText);
                console.log('XHR Status:', xhr.status);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengirim data: ' + error + ' (Status: ' + xhr.status + ')',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            },
            complete: function() {
                btn.text('Simpan');
                btn.attr('disabled', false);
            }
        });
    }
</script>











