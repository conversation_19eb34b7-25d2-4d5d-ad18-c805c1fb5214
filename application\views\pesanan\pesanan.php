<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-shopping-cart text-blue"></i> Data Pesanan <PERSON></h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <?= $this->session->flashdata('pesan') ?>
                        <table id="tbl_pesanan" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Pesan<PERSON></th>
                                    <th>Tanggal</th>
                                    <th>Pelanggan</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Total Harga</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Header -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Pesanan Pelanggan</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden-true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>



<!-- Modal Konfirmasi Hapus -->
<div class="modal fade" id="modal-hapus" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Hapus</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus pesanan ini?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="btn-hapus">Hapus</button>
            </div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;
    var id_pesanan;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_pesanan").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Pesanan Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('pesanan/ajax_list') ?>",
                "type": "POST"
            },

            //Set column definition initialisation properties.
            "columnDefs": [{
                "targets": [-1], //last column
                "orderable": false, //set not orderable
            }],
        });
    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Tambah Pesanan'); // set a title to modal header

        // Load form add
        $.ajax({
            url: "<?php echo site_url('pesanan/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show'); // show bootstrap modal after loading form
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Edit Pesanan'); // set a title to modal header

        // Load form edit
        $.ajax({
            url: "<?php echo site_url('pesanan/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);

                // Load data untuk edit
                $.ajax({
                    url: "<?php echo site_url('pesanan/edit') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_pesanan"]').val(data.nomor_pesanan);
                        $('[name="tanggal_pesanan"]').val(data.tanggal_pesanan);
                        $('[name="id_pelanggan"]').val(data.id_pelanggan).trigger('change');
                        $('[name="jenis_pesanan"]').val(data.jenis_pesanan).trigger('change');
                        $('[name="status"]').val(data.status).trigger('change');
                        $('[name="keterangan"]').val(data.keterangan);

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat mengambil data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function detail(id) {
        $('#modal_detail').modal('show');
        $('#modal_detail .modal-content').html('<div class="modal-body text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

        $.ajax({
            url: "<?php echo site_url('pesanan/detail_modal') ?>/" + id,
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal_detail .modal-content').html(data);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#modal_detail .modal-content').html('<div class="modal-body text-center text-danger"><i class="fa fa-exclamation-triangle"></i> Error loading detail</div>');
            }
        });
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('pesanan/insert') ?>";
        } else {
            url = "<?php echo site_url('pesanan/update') ?>";
        }

        // ajax adding data to database
        var formData = new FormData($('#form')[0]);
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload();
                    
                    Swal.fire({
                        title: 'Data pesanan berhasil disimpan!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                    
                    // Reinisialisasi Select2 setelah validasi gagal
                    $('.select2').select2({
                        dropdownParent: $('#modal_form'),
                        placeholder: "-- Pilih --",
                        allowClear: true
                    });
                    
                    // Show SweetAlert notification with detailed errors
                    var errorMessages = data.error_string.join('<br>');
                    Swal.fire({
                        title: 'Gagal Menyimpan!',
                        html: 'Terjadi kesalahan:<br>' + errorMessages,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            }
        });
    }

    function hapus(id) {
        id_pesanan = id;
        $('#modal-hapus').modal('show');
    }

    $('#btn-hapus').click(function() {
        $.ajax({
            url: '<?= site_url('pesanan/delete') ?>',
            type: 'POST',
            data: {
                id: id_pesanan
            },
            dataType: 'json',
            success: function(response) {
                $('#modal-hapus').modal('hide');
                if (response.status == 'success') {
                    Swal.fire({
                        title: 'Terhapus!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    table.ajax.reload();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function() {
                $('#modal-hapus').modal('hide');
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan sistem!',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    });

    function updateStatus(id, status) {
        var statusText = '';
        switch(status) {
            case 'diproses': statusText = 'diproses'; break;
            case 'dikirim': statusText = 'dikirim'; break;
            case 'selesai': statusText = 'diselesaikan'; break;
            case 'dibatalkan': statusText = 'dibatalkan'; break;
        }

        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin mengubah status pesanan menjadi ' + statusText + '?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Ubah!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: '<?= site_url('pesanan/update_status') ?>',
                    type: 'POST',
                    data: {
                        id: id,
                        status: status
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status == 'success') {
                            Swal.fire({
                                title: 'Status pesanan berhasil diubah!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                            table.ajax.reload();
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan sistem!',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function printPesanan(id) {
        window.open('<?= site_url('pesanan/print_pesanan') ?>/' + id, '_blank');
    }
</script>

<!-- Modal Detail -->
<div class="modal fade" id="modal_detail" tabindex="-1" role="dialog" aria-labelledby="modal_detail_label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>