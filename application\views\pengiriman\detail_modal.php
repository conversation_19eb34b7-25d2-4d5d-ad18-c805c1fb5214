<div class="modal-header">
    <h4 class="modal-title">Detail Pengiriman</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-md-6">
            <table class="table table-bordered table-sm">
                <tr>
                    <th style="width: 150px">Nomor Pengiriman</th>
                    <td><?= $pengiriman->nomor_pengiriman ?></td>
                </tr>
                <tr>
                    <th>Tanggal</th>
                    <td><?= date('d/m/Y', strtotime($pengiriman->tanggal_pengiriman)) ?></td>
                </tr>
                <tr>
                    <th>Nomor Pesanan</th>
                    <td><?= $pengiriman->nomor_pesanan ?></td>
                </tr>
                <tr>
                    <th>Pelanggan</th>
                    <td><?= $pengiriman->nama_pelanggan ?> (<?= $pengiriman->kode_pelanggan ?>)</td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td>
                        <?php
                        switch ($pengiriman->status) {
                            case 'draft':
                                echo '<span class="badge badge-warning">Draft</span>';
                                break;
                            case 'prepared':
                                echo '<span class="badge badge-primary">Disiapkan</span>';
                                break;
                            case 'shipped':
                                echo '<span class="badge badge-info">Dikirim</span>';
                                break;
                            case 'in_transit':
                                echo '<span class="badge badge-secondary">Dalam Perjalanan</span>';
                                break;
                            case 'delivered':
                                echo '<span class="badge badge-success">Diterima</span>';
                                break;
                            case 'returned':
                                echo '<span class="badge badge-danger">Dikembalikan</span>';
                                break;
                            case 'cancelled':
                                echo '<span class="badge badge-danger">Dibatalkan</span>';
                                break;
                            default:
                                echo '<span class="badge badge-secondary">' . ucfirst($pengiriman->status) . '</span>';
                        }
                        ?>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <table class="table table-bordered table-sm">
                <tr>
                    <th style="width: 150px">Alamat Pengiriman</th>
                    <td><?= nl2br($pengiriman->alamat_pengiriman) ?></td>
                </tr>
                <tr>
                    <th>Estimasi Tiba</th>
                    <td><?= $pengiriman->estimasi_tiba ? date('d/m/Y', strtotime($pengiriman->estimasi_tiba)) : '-' ?></td>
                </tr>
                <tr>
                    <th>Tanggal Dikirim</th>
                    <td><?= $pengiriman->tanggal_dikirim ? date('d/m/Y H:i', strtotime($pengiriman->tanggal_dikirim)) : '-' ?></td>
                </tr>
                <tr>
                    <th>Tanggal Diterima</th>
                    <td><?= $pengiriman->tanggal_diterima ? date('d/m/Y H:i', strtotime($pengiriman->tanggal_diterima)) : '-' ?></td>
                </tr>
                <tr>
                    <th>Penerima</th>
                    <td><?= $pengiriman->penerima ?: '-' ?></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Item Pengiriman</h3>
                    <?php if ($pengiriman->status == 'draft' || $pengiriman->status == 'prepared'): ?>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" onclick="addItem(<?= $pengiriman->id ?>, <?= $pengiriman->id_pesanan ?>)">
                            <i class="fas fa-plus"></i> Tambah Item
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr class="bg-info">
                                    <th>No</th>
                                    <th>Kode Barang</th>
                                    <th>Nama Barang</th>
                                    <th>Gudang</th>
                                    <th>Qty</th>
                                    <th>Satuan</th>
                                    <th>Berat (kg)</th>
                                    <th>Total Berat (kg)</th>
                                    <th>Keterangan</th>
                                    <?php if ($pengiriman->status == 'draft' || $pengiriman->status == 'prepared'): ?>
                                    <th>Aksi</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($pengiriman_detail)): ?>
                                <tr>
                                    <td colspan="<?= ($pengiriman->status == 'draft' || $pengiriman->status == 'prepared') ? '10' : '9' ?>" class="text-center">Belum ada item</td>
                                </tr>
                                <?php else: ?>
                                <?php $no = 1; foreach ($pengiriman_detail as $item): ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><?= $item->kode_barang ?></td>
                                    <td><?= $item->nama_barang ?></td>
                                    <td><?= $item->nama_gudang ?></td>
                                    <td class="text-right"><?= number_format($item->qty_dikirim, 0) ?></td>
                                    <td><?= $item->nama_satuan ?></td>
                                    <td class="text-right"><?= number_format($item->berat_satuan, 2) ?></td>
                                    <td class="text-right"><?= number_format($item->total_berat, 2) ?></td>
                                    <td><?= $item->keterangan ?></td>
                                    <?php if ($pengiriman->status == 'draft' || $pengiriman->status == 'prepared'): ?>
                                    <td>
                                        <button type="button" class="btn btn-xs btn-info" onclick="editItem(<?= $item->id ?>)"><i class="fas fa-edit"></i></button>
                                        <button type="button" class="btn btn-xs btn-danger" onclick="deleteItem(<?= $item->id ?>)"><i class="fas fa-trash"></i></button>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr class="bg-light">
                                    <th colspan="4" class="text-right">Total:</th>
                                    <th class="text-right"><?= number_format($pengiriman->total_qty, 0) ?></th>
                                    <th colspan="2"></th>
                                    <th class="text-right"><?= number_format($pengiriman->total_berat, 2) ?> kg</th>
                                    <th colspan="<?= ($pengiriman->status == 'draft' || $pengiriman->status == 'prepared') ? '2' : '1' ?>"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($pengiriman->keterangan)): ?>
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Keterangan</h3>
                </div>
                <div class="card-body">
                    <?= nl2br($pengiriman->keterangan) ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-info" onclick="printPengiriman(<?= $pengiriman->id ?>)">Print</button>
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
</div>

<!-- Modal Form Item -->
<div class="modal fade" id="modal_item" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Item Pengiriman</h4>
                <button type="button" class="close" onclick="closeItemModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="#" id="form_item">
                    <input type="hidden" name="id_item" id="id_item">
                    <input type="hidden" name="id_pengiriman" id="id_pengiriman" value="<?= $pengiriman->id ?>">
                    
                    <div class="form-group">
                        <label>Barang</label>
                        <select name="id_barang" id="id_barang" class="form-control select2" style="width: 100%;">
                            <option value="">-- Pilih Barang --</option>
                            <?php foreach ($barang_list as $barang): ?>
                            <option value="<?= $barang->id ?>" data-harga="<?= $barang->harga_jual ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?> (<?= $barang->nama_satuan ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Gudang</label>
                        <select name="id_gudang" id="id_gudang" class="form-control select2" style="width: 100%;">
                            <option value="">-- Pilih Gudang --</option>
                            <?php foreach ($gudang_list as $gudang): ?>
                            <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Qty Dikirim</label>
                        <input type="number" class="form-control" id="qty_dikirim" name="qty_dikirim" min="1" step="1">
                    </div>
                    
                    <div class="form-group">
                        <label>Berat Satuan (kg)</label>
                        <input type="number" class="form-control" id="berat_satuan" name="berat_satuan" min="0.01" step="1">
                    </div>
                    
                    <div class="form-group">
                        <label>Keterangan</label>
                        <textarea class="form-control" id="keterangan_item" name="keterangan"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeItemModal()">Batal</button>
                <button type="button" class="btn btn-primary" id="btnSaveItem">Simpan</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Fix for nested modals - improve z-index handling
        $(document).on('show.bs.modal', '.modal', function() {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
        
        // Prevent modal backdrop issues when item modal is closed
        $('#modal_item').on('hidden.bs.modal', function(e) {
            // Prevent event propagation to parent modal
            e.stopPropagation();
            
            // Keep parent modal visible and scrollable
            $('body').addClass('modal-open');
            
            // Fix for backdrop
            if ($('.modal:visible').length > 0) {
                // Ensure parent modal is still visible
                $('#modal_detail').css('display', 'block');
                
                // Fix backdrop if needed
                if ($('.modal-backdrop').length === 0) {
                    $('body').append('<div class="modal-backdrop fade show"></div>');
                    $('.modal-backdrop').css('z-index', parseInt($('#modal_detail').css('z-index')) - 1);
                }
            }
        });
        
        $('.select2').select2({
            dropdownParent: $("#modal_item")
        });
        
        // Delegasi event untuk tombol simpan agar tetap aktif walau modal di-refresh
        $(document).off('click', '#btnSaveItem').on('click', '#btnSaveItem', function() {
            // Komentar: Ambil id_item untuk menentukan mode tambah/edit
            var id_item = $('#id_item').val();
            var url = id_item ? "<?= site_url('pengiriman/update_detail') ?>" : "<?= site_url('pengiriman/add_detail') ?>";
            
            // Komentar: Kirim data form via AJAX
            $.ajax({
                url: url,
                type: "POST",
                data: $('#form_item').serialize(),
                dataType: "JSON",
                success: function(data) {
                    if (data.status === 'success') {
                        // Komentar: Tutup modal item setelah berhasil
                        closeItemModal();
                        
                        Swal.fire({
                            title: 'Berhasil!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            // Komentar: Refresh modal detail tanpa menutupnya
                            refreshDetailModal(<?= $pengiriman->id ?>);
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menyimpan data.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });
    });
    
    function addItem(id_pengiriman, id_pesanan) {
        $('#form_item')[0].reset();
        $('#id_item').val('');
        $('.select2').val('').trigger('change');
        
        // Load the form with ordered items
        $.ajax({
            url: "<?= site_url('pengiriman/form_detail_item') ?>",
            type: "POST",
            data: {
                id_pengiriman: id_pengiriman,
                id_pesanan: id_pesanan
            },
            success: function(html) {
                $('#modal_item .modal-body').html(html);
                
                // Show modal with proper z-index
                $('#modal_item').modal('show');
                
                // Initialize select2 in the loaded form
                $('#modal_item .select2').select2({
                    dropdownParent: $("#modal_item")
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat memuat form: ' + textStatus,
                icon: 'error',
                confirmButtonText: 'OK'
            });
            }
        });
    }
    
    function editItem(id) {
        // First get the item data
        $.ajax({
            url: "<?= site_url('pengiriman/get_detail_item') ?>/" + id,
            type: "GET",
            dataType: "JSON",
            success: function(response) {
                if (response.status === 'success') {
                    var data = response.data;
                    
                    // Load the form with ordered items
                    $.ajax({
                        url: "<?= site_url('pengiriman/form_detail_item') ?>",
                        type: "POST",
                        data: {
                            id_pengiriman: data.id_pengiriman,
                            id_pesanan: <?= $pengiriman->id_pesanan ?>
                        },
                        success: function(html) {
                            $('#modal_item .modal-body').html(html);
                            
                            // Show modal with proper z-index
                            $('#modal_item').modal('show');
                            
                            // Initialize select2 in the loaded form
                            $('#modal_item .select2').select2({
                                dropdownParent: $("#modal_item")
                            });
                            
                            // Set form values after a short delay to ensure the form is loaded
                            setTimeout(function() {
                                $('#id_item').val(id);
                                $('#id_barang').val(data.id_barang).trigger('change');
                                
                                // Wait for warehouses to load before setting the warehouse
                                setTimeout(function() {
                                    $('#id_gudang').val(data.id_gudang).trigger('change');
                                    
                                    // Set other values after warehouse is selected
                                    setTimeout(function() {
                                        $('#qty_dikirim').val(data.qty_dikirim);
                                        $('#berat_satuan').val(data.berat_satuan);
                                        $('#keterangan_item').val(data.keterangan);
                                    }, 500);
                                }, 500);
                            }, 500);
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Error loading form: ' + textStatus,
                                icon: 'error'
                            });
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Data tidak ditemukan',
                        icon: 'error'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil data.',
                    icon: 'error'
                });
            }
        });
    }

    function deleteItem(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: "Apakah Anda yakin ingin menghapus item ini?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?= site_url('pengiriman/delete_detail') ?>",
                    type: "POST",
                    data: {
                        id: id,
                        id_pengiriman: <?= $pengiriman->id ?>
                    },
                    dataType: "JSON",
                    success: function(data) {
                        if (data.status === 'success') {
                            Swal.fire({
                                title: 'Terhapus!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                // Update the detail modal content without closing it
                                refreshDetailModal(<?= $pengiriman->id ?>);
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }
    
    // Function to refresh the detail modal content without closing it
    function refreshDetailModal(id_pengiriman) {
        $.ajax({
            url: "<?= site_url('pengiriman/detail_modal') ?>/" + id_pengiriman,
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                // Keep the modal open but update its content
                var $modalContent = $('#modal_detail .modal-content');
                $modalContent.html(data);
                
                // Update the main table in the background
                if (typeof table !== 'undefined') {
                    table.ajax.reload(null, false);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memperbarui data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
    
    // Function to close only the item modal without affecting the parent modal
    function closeItemModal() {
        // Hide the modal without using data-dismiss which can affect parent modals
        $('#modal_item').modal('hide');
        
        // Ensure body keeps modal-open class for parent modal
        $('body').addClass('modal-open');
        
        // Ensure parent modal backdrop stays visible
        $('.modal-backdrop').last().remove();
        
        // Fix scrolling
        setTimeout(function() {
            if ($('.modal:visible').length > 0) {
                $('body').addClass('modal-open');
            }
        }, 100);
    }
</script>