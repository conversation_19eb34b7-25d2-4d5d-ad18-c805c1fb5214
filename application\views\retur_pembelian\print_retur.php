<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Retur Pembelian',
    'document_number' => $retur->nomor_retur,
    'document_title' => 'RETUR PEMBELIAN'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Retur',
        'items' => [
            ['label' => 'Nomor Retur', 'value' => '<strong>' . $retur->nomor_retur . '</strong>'],
            ['label' => 'Tanggal Retur', 'value' => format_date_indonesia($retur->tanggal_retur)],
            ['label' => 'Status', 'value' => get_status_badge($retur->status)],
            ['label' => 'Alasan Retur', 'value' => $retur->alasan_retur ?? '-']
        ]
    ],
    [
        'title' => '<span class="icon-user"></span>Informasi Supplier',
        'items' => [
            ['label' => 'Nama Supplier', 'value' => '<strong>' . $retur->nama_supplier . '</strong> (' . $retur->kode_supplier . ')'],
            ['label' => 'Alamat', 'value' => $retur->alamat_supplier ?: '-'],
            ['label' => 'Telepon', 'value' => $retur->telepon_supplier ?: '-'],
            ['label' => 'Email', 'value' => $retur->email_supplier ?? '-']
        ]
    ]
];

// Add reference documents if available
if (!empty($retur->nomor_po) || !empty($retur->nomor_penerimaan)) {
    $ref_items = [];
    if (!empty($retur->nomor_po)) {
        $ref_items[] = ['label' => 'No. Purchase Order', 'value' => $retur->nomor_po];
    }
    if (!empty($retur->nomor_penerimaan)) {
        $ref_items[] = ['label' => 'No. Penerimaan', 'value' => $retur->nomor_penerimaan];
    }
    
    $info_sections[] = [
        'title' => '<span class="icon-document"></span>Dokumen Referensi',
        'items' => $ref_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '25%'],
    ['label' => 'Gudang', 'width' => '12%'],
    ['label' => 'Kondisi', 'width' => '10%', 'align' => 'center'],
    ['label' => 'Qty Retur', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Harga Satuan', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Total Harga', 'width' => '14%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$no = 1;

if (!empty($detail_items)) {
    foreach ($detail_items as $item) {
        $nama_barang = $item->nama_barang;
        if ($item->merk) {
            $nama_barang .= ' - ' . $item->merk;
        }
        if ($item->tipe) {
            $nama_barang .= ' (' . $item->tipe . ')';
        }
        
        // Color coding untuk kondisi
        $kondisi = ucfirst($item->kondisi_barang);
        if ($item->kondisi_barang == 'rusak') {
            $kondisi = '<span class="color-danger">' . $kondisi . '</span>';
        } elseif ($item->kondisi_barang == 'baik') {
            $kondisi = '<span class="color-success">' . $kondisi . '</span>';
        } elseif ($item->kondisi_barang == 'cacat') {
            $kondisi = '<span class="color-warning">' . $kondisi . '</span>';
        }
        
        $table_data[] = [
            $no++,
            $item->kode_barang,
            $nama_barang,
            $item->nama_gudang,
            $kondisi,
            number_format($item->qty_retur, 2),
            format_currency($item->harga_satuan),
            format_currency($item->total_harga)
        ];
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL</strong>', 'colspan' => '5', 'align' => 'right'],
        ['value' => '<strong>' . number_format($retur->total_qty, 2) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>Total Nilai</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($retur->total_nilai) . '</strong>', 'align' => 'right']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Barang Retur', $table_headers, $table_data, $table_options);

// Add notes if available
if (!empty($retur->keterangan)) {
    $content .= create_notes_section('Keterangan', $retur->keterangan);
}

// Add return policy notes
$content .= create_notes_section('Kebijakan Retur ke Supplier', 
    '• Barang yang diretur harus sesuai dengan kondisi yang dilaporkan' . "\n" .
    '• Proses retur mengikuti kebijakan yang telah disepakati dengan supplier' . "\n" .
    '• Barang rusak akan diklaim sesuai dengan ketentuan garansi' . "\n" .
    '• Credit note akan diproses setelah supplier menerima barang retur' . "\n" .
    '• Dokumen ini merupakan bukti sah pengiriman barang retur ke supplier');

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => '(............................)',
        'position' => 'Staff Purchasing'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Purchasing'
    ],
    [
        'title' => 'Disetujui Oleh',
        'name' => '(............................)',
        'position' => 'Manager'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>