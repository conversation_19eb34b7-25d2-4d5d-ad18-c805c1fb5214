<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Settlement Penjualan</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement/penjualan') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Pengiriman</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Nomor Pengiriman</th>
                                        <td><?= $pengiriman->nomor_pengiriman ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Pengiriman</th>
                                        <td><?= $pengiriman->tanggal_pengiriman ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status Pengiriman</th>
                                        <td>
                                            <?php if ($pengiriman->status == 'pending'): ?>
                                                <span class="badge badge-warning">Pending</span>
                                            <?php elseif ($pengiriman->status == 'shipped'): ?>
                                                <span class="badge badge-info">Dikirim</span>
                                            <?php elseif ($pengiriman->status == 'delivered'): ?>
                                                <span class="badge badge-success">Diterima</span>
                                            <?php elseif ($pengiriman->status == 'canceled'): ?>
                                                <span class="badge badge-danger">Dibatalkan</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Diterima</th>
                                        <td><?= $pengiriman->tanggal_diterima ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nomor Pesanan</th>
                                        <td><?= $pengiriman->nomor_pesanan ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Pesanan</th>
                                        <td><?= $pengiriman->tanggal_pesanan ?? '-' ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-info">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Pelanggan</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Kode Pelanggan</th>
                                        <td><?= $pengiriman->kode_pelanggan ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nama Pelanggan</th>
                                        <td><?= $pengiriman->nama_pelanggan ?></td>
                                    </tr>
                                    <tr>
                                        <th>Alamat</th>
                                        <td><?= $pengiriman->alamat_pelanggan ?></td>
                                    </tr>
                                    <tr>
                                        <th>Telepon</th>
                                        <td><?= $pengiriman->telepon_pelanggan ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Alamat Pengiriman</th>
                                        <td><?= $pengiriman->alamat_pengiriman ?></td>
                                    </tr>
                                    <tr>
                                        <th>Penerima</th>
                                        <td><?= $pengiriman->penerima ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Detail Barang Pengiriman</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Kode Barang</th>
                                                <th>Nama Barang</th>
                                                <th>Gudang</th>
                                                <th>Qty Dikirim</th>
                                                <th>Satuan</th>
                                                <th>Harga Satuan</th>
                                                <th>Subtotal</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            $total_qty = 0;
                                            $total_subtotal = 0;
                                            foreach ($detail as $row): 
                                                $subtotal = $row->qty_dikirim * $row->harga_jual;
                                                $total_qty += $row->qty_dikirim;
                                                $total_subtotal += $subtotal;
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->kode_barang ?></td>
                                                <td><?= $row->nama_barang ?> <?= $row->merk ? '- '.$row->merk : '' ?> <?= $row->tipe ? '- '.$row->tipe : '' ?></td>
                                                <td><?= $row->nama_gudang ?></td>
                                                <td class="text-right"><?= number_format($row->qty_dikirim, 2) ?></td>
                                                <td><?= $row->nama_satuan ?></td>
                                                <td class="text-right"><?= number_format($row->harga_jual, 2) ?></td>
                                                <td class="text-right"><?= number_format($subtotal, 2) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="4" class="text-right">Total</th>
                                                <th class="text-right"><?= number_format($total_qty, 2) ?></th>
                                                <th></th>
                                                <th></th>
                                                <th class="text-right"><?= number_format($total_subtotal, 2) ?></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($faktur): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Faktur</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-bordered">
                                            <tr>
                                                <th width="30%">Nomor Faktur</th>
                                                <td><?= $faktur->nomor_faktur ?></td>
                                            </tr>
                                            <tr>
                                                <th>Tanggal Faktur</th>
                                                <td><?= $faktur->tanggal_faktur ?></td>
                                            </tr>
                                            <tr>
                                                <th>Tanggal Jatuh Tempo</th>
                                                <td><?= $faktur->tanggal_jatuh_tempo ?? '-' ?></td>
                                            </tr>
                                            <tr>
                                                <th>Status Pembayaran</th>
                                                <td>
                                                    <?php if ($faktur->status_pembayaran == 'belum_bayar'): ?>
                                                        <span class="badge badge-danger">Belum Bayar</span>
                                                    <?php elseif ($faktur->status_pembayaran == 'sebagian'): ?>
                                                        <span class="badge badge-warning">Bayar Sebagian</span>
                                                    <?php elseif ($faktur->status_pembayaran == 'lunas'): ?>
                                                        <span class="badge badge-success">Lunas</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-bordered">
                                            <tr>
                                                <th width="30%">Subtotal</th>
                                                <td class="text-right"><?= number_format($faktur->subtotal, 2) ?></td>
                                            </tr>
                                            <tr>
                                                <th>Diskon</th>
                                                <td class="text-right"><?= number_format($faktur->diskon, 2) ?></td>
                                            </tr>
                                            <tr>
                                                <th>Pajak</th>
                                                <td class="text-right"><?= number_format($faktur->pajak, 2) ?></td>
                                            </tr>
                                            <tr>
                                                <th>Total Faktur</th>
                                                <td class="text-right"><?= number_format($faktur->total_faktur, 2) ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <div class="table-responsive mt-3">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Kode Barang</th>
                                                <th>Nama Barang</th>
                                                <th>Qty</th>
                                                <th>Satuan</th>
                                                <th>Harga Satuan</th>
                                                <th>Diskon</th>
                                                <th>Pajak</th>
                                                <th>Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            foreach ($faktur_detail as $row): 
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->kode_barang ?></td>
                                                <td><?= $row->nama_barang ?> <?= $row->merk ? '- '.$row->merk : '' ?> <?= $row->tipe ? '- '.$row->tipe : '' ?></td>
                                                <td class="text-right"><?= number_format($row->qty, 2) ?></td>
                                                <td><?= $row->nama_satuan ?></td>
                                                <td class="text-right"><?= number_format($row->harga_satuan, 2) ?></td>
                                                <td class="text-right"><?= number_format($row->diskon_nilai, 2) ?></td>
                                                <td class="text-right"><?= number_format($row->pajak_nilai, 2) ?></td>
                                                <td class="text-right"><?= number_format($row->total, 2) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-danger">
                            <div class="card-header">
                                <h3 class="card-title">Analisis Perbedaan</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Parameter</th>
                                                <th>Pengiriman</th>
                                                <th>Faktur</th>
                                                <th>Selisih</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Jumlah Item</td>
                                                <td class="text-center"><?= count($detail) ?></td>
                                                <td class="text-center"><?= count($faktur_detail) ?></td>
                                                <td class="text-center"><?= count($detail) - count($faktur_detail) ?></td>
                                                <td class="text-center">
                                                    <?php if (count($detail) == count($faktur_detail)): ?>
                                                        <span class="badge badge-success">Sesuai</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger">Tidak Sesuai</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Total Qty</td>
                                                <td class="text-center"><?= number_format($total_qty, 2) ?></td>
                                                <td class="text-center"><?= number_format($faktur->total_qty, 2) ?></td>
                                                <td class="text-center"><?= number_format($total_qty - $faktur->total_qty, 2) ?></td>
                                                <td class="text-center">
                                                    <?php if ($total_qty == $faktur->total_qty): ?>
                                                        <span class="badge badge-success">Sesuai</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger">Tidak Sesuai</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Total Nilai</td>
                                                <td class="text-center"><?= number_format($total_subtotal, 2) ?></td>
                                                <td class="text-center"><?= number_format($faktur->subtotal, 2) ?></td>
                                                <td class="text-center"><?= number_format($total_subtotal - $faktur->subtotal, 2) ?></td>
                                                <td class="text-center">
                                                    <?php if ($total_subtotal == $faktur->subtotal): ?>
                                                        <span class="badge badge-success">Sesuai</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger">Tidak Sesuai</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <?php if (count($detail) != count($faktur_detail) || $total_qty != $faktur->total_qty || $total_subtotal != $faktur->subtotal): ?>
                                <div class="alert alert-warning mt-3">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Terdapat perbedaan antara data pengiriman dan faktur. Silakan periksa kembali data tersebut dan lakukan penyesuaian jika diperlukan.</p>
                                    <a href="<?= site_url('FakturPenjualan/edit/' . $faktur->id) ?>" class="btn btn-warning"><i class="fas fa-edit"></i> Edit Faktur</a>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-success mt-3">
                                    <h5><i class="icon fas fa-check"></i> Informasi</h5>
                                    <p>Data pengiriman dan faktur telah sesuai.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                            <p>Pengiriman ini belum memiliki faktur. Silakan buat faktur untuk pengiriman ini.</p>
                            <a href="<?= site_url('settlement/penjualan_create_faktur/' . $pengiriman->id) ?>" class="btn btn-success"><i class="fas fa-file-invoice"></i> Buat Faktur</a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>