<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Settlement Pembayaran (Faktur vs Pembayaran)</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            Settlement Pembayaran digunakan untuk memastikan bahwa semua faktur telah dibayar dengan benar.
                            Proses ini akan membandingkan data faktur dengan data pembayaran untuk mengidentifikasi perbedaan atau kesalahan.
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card card-outline card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Filter Data</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="form-filter" class="form-horizontal">
                                    <div class="form-group row">
                                        <label for="tanggal_awal" class="col-sm-2 col-form-label">Tanggal Transaksi</label>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?= $tanggal_awal ?>">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">s/d</span>
                                                </div>
                                                <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?= $tanggal_akhir ?>">
                                            </div>
                                        </div>
                                        <label for="jenis" class="col-sm-2 col-form-label">Jenis Transaksi</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="jenis" name="jenis">
                                                <option value="">Semua Jenis</option>
                                                <option value="penjualan">Penjualan</option>
                                                <option value="pembelian">Pembelian</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="status" class="col-sm-2 col-form-label">Status Pembayaran</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="status" name="status">
                                                <option value="">Semua Status</option>
                                                <option value="belum_bayar">Belum Bayar</option>
                                                <option value="sebagian">Bayar Sebagian</option>
                                                <option value="lunas">Lunas</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-6">
                                            <button type="button" id="btn-filter" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                                            <button type="button" id="btn-reset" class="btn btn-default"><i class="fas fa-undo"></i> Reset</button>
                                            <button type="button" id="btn-export" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="table-settlement" class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Jenis</th>
                                        <th>Nomor Transaksi</th>
                                        <th>Tanggal Transaksi</th>
                                        <th>Partner</th>
                                        <th>Total Transaksi</th>
                                        <th>Total Dibayar</th>
                                        <th>Sisa Pembayaran</th>
                                        <th>Status Pembayaran</th>
                                        <th>Umur Piutang/Hutang</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#table-settlement').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?= site_url('settlement/pembayaran_data') ?>",
            "type": "POST",
            "data": function(data) {
                data.tanggal_awal = $('#tanggal_awal').val();
                data.tanggal_akhir = $('#tanggal_akhir').val();
                data.jenis = $('#jenis').val();
                data.status = $('#status').val();
            },
            "dataSrc": "data"
        },
        "columns": [
            { "data": null, "orderable": false, "render": function(data, type, row, meta) {
                return meta.row + 1;
            }},
            { "data": "jenis", "render": function(data, type, row) {
                if (data == 'penjualan') {
                    return '<span class="badge badge-info">Penjualan</span>';
                } else if (data == 'pembelian') {
                    return '<span class="badge badge-success">Pembelian</span>';
                } else {
                    return data;
                }
            }},
            { "data": "nomor_transaksi" },
            { "data": "tanggal_transaksi" },
            { "data": "nama_partner" },
            { "data": "total_transaksi", "render": function(data, type, row) {
                return formatRupiah(data);
            }},
            { "data": "total_dibayar", "render": function(data, type, row) {
                return formatRupiah(data);
            }},
            { "data": "sisa_pembayaran", "render": function(data, type, row) {
                if (data == 0) {
                    return '<span class="text-success">' + formatRupiah(data) + '</span>';
                } else {
                    return '<span class="text-danger">' + formatRupiah(data) + '</span>';
                }
            }},
            { "data": "status_pembayaran", "render": function(data, type, row) {
                if (data == 'belum_bayar') {
                    return '<span class="badge badge-danger">Belum Bayar</span>';
                } else if (data == 'sebagian') {
                    return '<span class="badge badge-warning">Bayar Sebagian</span>';
                } else if (data == 'lunas') {
                    return '<span class="badge badge-success">Lunas</span>';
                } else {
                    return data;
                }
            }},
            { "data": null, "render": function(data, type, row) {
                var umur = row.jenis == 'penjualan' ? row.umur_piutang : row.umur_hutang;
                if (umur === null || umur <= 0) {
                    return '-';
                } else if (umur <= 30) {
                    return '<span class="text-success">' + umur + ' hari</span>';
                } else if (umur <= 60) {
                    return '<span class="text-warning">' + umur + ' hari</span>';
                } else {
                    return '<span class="text-danger">' + umur + ' hari</span>';
                }
            }},
            { "data": null, "orderable": false, "render": function(data, type, row) {
                var html = '<div class="btn-group">';
                html += '<a href="<?= site_url('settlement/pembayaran_detail/') ?>' + row.jenis + '/' + row.id + '" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>';
                html += '</div>';
                return html;
            }}
        ],
        "order": [[3, 'desc']],
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    $('#btn-filter').click(function() {
        table.ajax.reload();
    });

    $('#btn-reset').click(function() {
        $('#form-filter')[0].reset();
        table.ajax.reload();
    });

    $('#btn-export').click(function() {
        var tanggal_awal = $('#tanggal_awal').val();
        var tanggal_akhir = $('#tanggal_akhir').val();
        var jenis = $('#jenis').val();
        var status = $('#status').val();
        
        window.location.href = '<?= site_url('settlement/export/pembayaran') ?>?tanggal_awal=' + tanggal_awal + '&tanggal_akhir=' + tanggal_akhir + '&jenis=' + jenis + '&status=' + status;
    });

    function formatRupiah(angka) {
        // Handle undefined, null, or empty values
        if (angka === undefined || angka === null || angka === '') {
            return 'Rp 0';
        }
        
        // Convert to number if it's a string
        if (typeof angka === 'string') {
            angka = parseFloat(angka) || 0;
        }
        
        var number_string = angka.toString(),
            split = number_string.split('.'),
            sisa = split[0].length % 3,
            rupiah = split[0].substr(0, sisa),
            ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }
});
</script>