<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

            <div class="form-group row">
                <!-- Nomor <PERSON>esanan -->
                <label for="nomor_pesanan" class="col-sm-3 col-form-label">Nomor <PERSON> <span class="text-danger">*</span></label>
                <div class="col-sm-4 kosong">
                    <input type="text" class="form-control" id="nomor_pesanan" value="<?= isset($nomor_pesanan) ? $nomor_pesanan : '' ?>" readonly>
                    <input type="hidden" name="nomor_pesanan" id="nomor_pesanan_hidden" value="<?= isset($nomor_pesanan) ? $nomor_pesanan : '' ?>">
                    <span class="help-block"></span>
                </div>

                <!-- Tanggal Pesanan -->
                <label for="tanggal_pesanan" class="col-sm-2 col-form-label">Tanggal <span class="text-danger">*</span></label>
                <div class="col-sm-3 kosong">
                    <input type="date" class="form-control" name="tanggal_pesanan" id="tanggal_pesanan" value="<?= isset($pesanan->tanggal_pesanan) ? $pesanan->tanggal_pesanan : date('Y-m-d') ?>" required>
                    <span class="help-block"></span>
                </div>
            </div>

            <!-- Informasi Pelanggan -->
            <div class="form-group row">
                <label for="id_pelanggan" class="col-sm-3 col-form-label">Pelanggan <span class="text-danger">*</span></label>
                <div class="col-sm-9 kosong">
                    <select class="form-control select2" name="id_pelanggan" id="id_pelanggan" required style="width: 100%;">
                        <option value="">-- Pilih Pelanggan --</option>
                        <?php if (isset($pelanggan_list)): ?>
                            <?php foreach ($pelanggan_list as $pelanggan): ?>
                                <option value="<?= $pelanggan->id ?>" <?= (isset($pesanan) && $pesanan->id_pelanggan == $pelanggan->id) ? 'selected' : '' ?>>
                                    <?= $pelanggan->kode ?> - <?= $pelanggan->nama ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                    <span class="help-block"></span>

                    <!-- Informasi Detail Pelanggan -->
                    <div id="pelanggan-info" class="mt-2" style="display: none;">
                        <div class="alert alert-info p-2">
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>Alamat:</strong> <span id="pelanggan-alamat">-</span></small><br>
                                    <small><strong>No. Telepon:</strong> <span id="pelanggan-telepon">-</span></small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>Email:</strong> <span id="pelanggan-email">-</span></small><br>
                                    <small><strong>PIC:</strong> <span id="pelanggan-pic">-</span></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <!-- Jenis Pesanan -->
                <label for="jenis_pesanan" class="col-sm-3 col-form-label">Jenis Pesanan <span class="text-danger">*</span></label>
                <div class="col-sm-4 kosong">
                    <input type="text" class="form-control" value="Manual" readonly>
                    <input type="hidden" name="jenis_pesanan" value="manual">
                    <span class="help-block"></span>
                </div>

                <!-- Status -->
                <label for="status" class="col-sm-2 col-form-label">Status <span class="text-danger">*</span></label>
                <div class="col-sm-3 kosong">
                    <input type="text" class="form-control" value="Draft" readonly>
                    <input type="hidden" name="status" value="draft">
                    <span class="help-block"></span>
                </div>
            </div>

            <!-- Keterangan -->
            <div class="form-group row">
                <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                <div class="col-sm-9 kosong">
                    <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan tambahan untuk pesanan ini" rows="3"><?= isset($pesanan->keterangan) ? $pesanan->keterangan : '' ?></textarea>
                    <span class="help-block"></span>
                </div>
            </div>
    </div>

    <!-- Informasi Tambahan -->
    <div class="alert alert-warning">
        <i class="fa fa-info-circle"></i> <strong>Catatan:</strong>
        <ul class="mb-0 mt-2">
            <li>Pesanan dengan status <strong>Draft</strong> masih dapat diedit dan dihapus</li>
            <li>Setelah diproses, pesanan tidak dapat diedit lagi</li>
            <li>Item pesanan dapat ditambahkan setelah pesanan disimpan</li>
        </ul>
    </div>

    </div>
</form>

<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            dropdownParent: $('#modal_form'),
            placeholder: "-- Pilih --",
            allowClear: true
        });

        // Event handler untuk perubahan pelanggan
        $('#id_pelanggan').on('change', function() {
            var pelangganId = $(this).val();

            if (pelangganId) {
                // Ambil data pelanggan via AJAX
                $.ajax({
                    url: "<?php echo site_url('pelanggan/get_pelanggan_info') ?>",
                    type: "POST",
                    data: {
                        id: pelangganId
                    },
                    dataType: "JSON",
                    success: function(data) {
                        if (data.status) {
                            $('#pelanggan-alamat').text(data.data.alamat || '-');
                            $('#pelanggan-telepon').text(data.data.no_telepon || '-');
                            $('#pelanggan-email').text(data.data.email || '-');
                            $('#pelanggan-pic').text(data.data.nama_pic || '-');
                            $('#pelanggan-info').show();
                        }
                    },
                    error: function() {
                        $('#pelanggan-info').hide();
                    }
                });
            } else {
                $('#pelanggan-info').hide();
            }
        });

        // Trigger change event untuk set initial state
        $('#id_pelanggan').trigger('change');
    });

    function generateNomor() {
        $.ajax({
            url: "<?php echo site_url('pesanan/generate_nomor') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.nomor) {
                    $('#nomor_pesanan').val(data.nomor);
                }
            },
            error: function() {
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal generate nomor pesanan.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
</script>