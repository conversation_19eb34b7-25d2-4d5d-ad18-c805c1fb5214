<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Settlement Stok (Stok Fisik vs Stok Sistem)</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            Settlement Stok digunakan untuk memastikan bahwa stok fisik sesuai dengan stok di sistem.
                            Proses ini akan membandingkan data stok opname dengan stok di sistem untuk mengidentifikasi perbedaan atau kesalahan.
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card card-outline card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Filter Data</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="form-filter" class="form-horizontal">
                                    <div class="form-group row">
                                        <label for="id_gudang" class="col-sm-2 col-form-label">Gudang</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="id_gudang" name="id_gudang">
                                                <option value="">Semua Gudang</option>
                                                <?php foreach ($gudang as $row): ?>
                                                <option value="<?= $row->id ?>"><?= $row->nama_gudang ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <label for="filter_barang" class="col-sm-2 col-form-label">Filter Barang</label>
                                        <div class="col-sm-4">
                                            <input type="text" class="form-control" id="filter_barang" name="filter_barang" placeholder="Kode/Nama Barang">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="status" class="col-sm-2 col-form-label">Status</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="status" name="status">
                                                <option value="">Semua Status</option>
                                                <option value="selisih">Ada Selisih</option>
                                                <option value="sesuai">Sesuai</option>
                                                <option value="belum_opname">Belum Opname</option>
                                            </select>
                                        </div>
                                        <div class="col-sm-6">
                                            <button type="button" id="btn-filter" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                                            <button type="button" id="btn-reset" class="btn btn-default"><i class="fas fa-undo"></i> Reset</button>
                                            <button type="button" id="btn-export" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="table-settlement" class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Kode Barang</th>
                                        <th>Nama Barang</th>
                                        <th>Gudang</th>
                                        <th>Stok Sistem</th>
                                        <th>Stok Fisik</th>
                                        <th>Selisih</th>
                                        <th>Tanggal Opname Terakhir</th>
                                        <th>Status</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#table-settlement').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?= site_url('settlement/stok_data') ?>",
            "type": "POST",
            "data": function(data) {
                data.id_gudang = $('#id_gudang').val();
                data.filter_barang = $('#filter_barang').val();
                data.status = $('#status').val();
            },
            "dataSrc": "data"
        },
        "columns": [
            { "data": null, "orderable": false, "render": function(data, type, row, meta) {
                return meta.row + 1;
            }},
            { "data": "kode_barang" },
            { "data": "nama_barang", "render": function(data, type, row) {
                var nama = data;
                if (row.merk) nama += ' - ' + row.merk;
                if (row.tipe) nama += ' - ' + row.tipe;
                return nama;
            }},
            { "data": "nama_gudang" },
            { "data": "stok_sistem", "render": function(data, type, row) {
                return formatAngka(data) + ' ' + row.nama_satuan;
            }},
            { "data": "stok_fisik", "render": function(data, type, row) {
                if (row.tanggal_opname_terakhir) {
                    return formatAngka(data) + ' ' + row.nama_satuan;
                } else {
                    return '<span class="text-muted">Belum Opname</span>';
                }
            }},
            { "data": "selisih", "render": function(data, type, row) {
                if (row.tanggal_opname_terakhir) {
                    if (data == 0) {
                        return '<span class="text-success">0 ' + row.nama_satuan + '</span>';
                    } else if (data > 0) {
                        return '<span class="text-danger">+' + formatAngka(data) + ' ' + row.nama_satuan + '</span>';
                    } else {
                        return '<span class="text-danger">' + formatAngka(data) + ' ' + row.nama_satuan + '</span>';
                    }
                } else {
                    return '<span class="text-muted">-</span>';
                }
            }},
            { "data": "tanggal_opname_terakhir", "render": function(data, type, row) {
                return data ? data : '<span class="text-muted">Belum Pernah</span>';
            }},
            { "data": null, "render": function(data, type, row) {
                if (!row.tanggal_opname_terakhir) {
                    return '<span class="badge badge-warning">Belum Opname</span>';
                } else if (row.selisih == 0) {
                    return '<span class="badge badge-success">Sesuai</span>';
                } else {
                    return '<span class="badge badge-danger">Ada Selisih</span>';
                }
            }},
            { "data": null, "orderable": false, "render": function(data, type, row) {
                var html = '<div class="btn-group">';
                html += '<a href="<?= site_url('settlement/stok_detail/') ?>' + row.id_barang + '/' + row.id_gudang + '" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>';
                if (row.selisih != 0 || !row.tanggal_opname_terakhir) {
                    html += '<a href="<?= site_url('settlement/stok_create_penyesuaian/') ?>' + row.id_barang + '/' + row.id_gudang + '" class="btn btn-warning btn-sm"><i class="fas fa-sync-alt"></i> Penyesuaian</a>';
                }
                html += '</div>';
                return html;
            }}
        ],
        "order": [[8, 'desc'], [1, 'asc']],
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    $('#btn-filter').click(function() {
        table.ajax.reload();
    });

    $('#btn-reset').click(function() {
        $('#form-filter')[0].reset();
        table.ajax.reload();
    });

    $('#btn-export').click(function() {
        var id_gudang = $('#id_gudang').val();
        var filter_barang = $('#filter_barang').val();
        var status = $('#status').val();
        
        window.location.href = '<?= site_url('settlement/export/stok') ?>?id_gudang=' + id_gudang + '&filter_barang=' + filter_barang + '&status=' + status;
    });

    function formatAngka(angka) {
        return parseFloat(angka).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    }
});
</script>