<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Penerimaan Pembelian
 * Mengatur data penerimaan pembelian dan detailnya
 * Terintegrasi dengan modul pembelian dan barang masuk
 */
class Mod_penerimaan_pembelian extends CI_Model
{
    var $table = 'penerimaan_pembelian';
    var $table_detail = 'penerimaan_pembelian_detail';
    var $column_search = array(
        'pp.nomor_penerimaan', 
        'pp.tanggal_penerimaan', 
        's.nama', 
        'p.nomor_pembelian', 
        'pp.status', 
        'pp.keterangan'
    );
    var $column_order = array(
        'pp.id', 
        'pp.nomor_penerimaan', 
        'pp.tanggal_penerimaan', 
        's.nama', 
        'p.nomor_pembelian', 
        'pp.status',
        'pp.total_item',
        'pp.total_qty'
    );
    var $order = array('pp.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            pp.id,
            pp.nomor_penerimaan,
            pp.tanggal_penerimaan,
            pp.id_pembelian,
            pp.id_supplier,
            pp.status,
            pp.total_item,
            pp.total_qty,
            pp.keterangan,
            pp.penerima,
            pp.tanggal_diterima,
            pp.created_by,
            pp.updated_by,
            pp.created_at,
            pp.updated_at,
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            p.nomor_pembelian
        ');
        $this->db->from($this->table . ' pp');
        $this->db->join('supplier s', 'pp.id_supplier = s.id', 'left');
        $this->db->join('pembelian p', 'pp.id_pembelian = p.id', 'left');
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->select('
            pp.*,
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            s.alamat as alamat_supplier,
            s.no_telepon,
            s.email,
            p.nomor_pembelian,
            p.tanggal_pembelian,
            p.jenis_pembelian
        ');
        $this->db->from($this->table . ' pp');
        $this->db->join('supplier s', 'pp.id_supplier = s.id', 'left');
        $this->db->join('pembelian p', 'pp.id_pembelian = p.id', 'left');
        $this->db->where('pp.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    // Method konsisten dengan modul lain
    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
        return $this->db->affected_rows();
    }

    public function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }

    // Detail penerimaan functions
    public function get_detail_by_penerimaan_id($id_penerimaan)
    {
        $this->db->select('
            ppd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.kode_satuan,
            g.nama_gudang,
            g.kode_gudang,
            pd.harga_satuan,
            pd.diskon_persen,
            pd.diskon_nominal,
            pd.subtotal_setelah_diskon as subtotal
        ');
        $this->db->from($this->table_detail . ' ppd');
        $this->db->join('barang b', 'ppd.id_barang = b.id', 'left');
        $this->db->join('pembelian_detail pd', 'ppd.id_pembelian_detail = pd.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'ppd.id_gudang = g.id', 'left');
        $this->db->where('ppd.id_penerimaan', $id_penerimaan);
        $this->db->order_by('ppd.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    public function update_detail($where, $data)
    {
        $this->db->update($this->table_detail, $data, $where);
        return $this->db->affected_rows();
    }

    public function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    public function delete_detail_by_penerimaan($id_penerimaan)
    {
        $this->db->where('id_penerimaan', $id_penerimaan);
        $this->db->delete($this->table_detail);
    }

    // Generate nomor penerimaan - Format: TPB-YYYYMMDD-XXXX (Terima Pembelian)
    public function generate_nomor_penerimaan()
    {
        $prefix = 'TPB';
        $date = date('Ymd');
        
        $this->db->select('nomor_penerimaan');
        $this->db->from($this->table);
        $this->db->like('nomor_penerimaan', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_penerimaan', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_penerimaan;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Update total penerimaan
    public function update_total_penerimaan($id_penerimaan)
    {
        $this->db->select('COUNT(DISTINCT id_barang) as total_item, SUM(qty_diterima) as total_qty');
        $this->db->from($this->table_detail);
        $this->db->where('id_penerimaan', $id_penerimaan);
        $query = $this->db->get();
        $result = $query->row();
        
        $data = array(
            'total_item' => $result->total_item ? $result->total_item : 0,
            'total_qty' => $result->total_qty ? $result->total_qty : 0,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id_penerimaan);
        $this->db->update($this->table, $data);
    }

    // Method untuk mendapatkan data dropdown
    public function get_supplier_aktif()
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('supplier');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pembelian_aktif()
    {
        // Perluas status yang dianggap aktif untuk mencakup lebih banyak kemungkinan
        $this->db->select('p.id, p.nomor_pembelian, p.tanggal_pembelian, p.id_supplier, p.status, s.nama as nama_supplier');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        
        // Tambahkan status lain yang mungkin valid untuk penerimaan
        $this->db->where_in('p.status', ['dipesan', 'disetujui', 'sebagian', 'proses']);
        
        // Pastikan pembelian belum selesai atau dibatalkan
        $this->db->where_not_in('p.status', ['selesai', 'dibatalkan', 'ditolak']);
        
        // Tambahkan filter untuk memastikan pembelian memiliki item
        $this->db->where('EXISTS (SELECT 1 FROM pembelian_detail pd WHERE pd.id_pembelian = p.id)');
        
        // Tambahkan filter untuk memastikan masih ada item yang belum diterima sepenuhnya
        $this->db->where('EXISTS (
            SELECT 1 FROM pembelian_detail pd 
            WHERE pd.id_pembelian = p.id 
            AND pd.qty > COALESCE(
                (SELECT SUM(ppd.qty_diterima) 
                FROM penerimaan_pembelian_detail ppd 
                JOIN penerimaan_pembelian pp ON ppd.id_penerimaan = pp.id 
                WHERE pp.id_pembelian = p.id AND ppd.id_pembelian_detail = pd.id), 0
            )
        )');
        
        $this->db->order_by('p.tanggal_pembelian', 'desc');
        $query = $this->db->get();
        
        // Log jumlah pembelian yang ditemukan
        $result = $query->result();
        log_message('info', 'Pembelian aktif ditemukan: ' . count($result));
        
        // Jika tidak ada pembelian aktif, coba ambil semua pembelian yang belum selesai
        if (empty($result)) {
            log_message('info', 'Tidak ada pembelian aktif dengan item yang belum diterima, mencoba ambil semua pembelian yang belum selesai');
            
            $this->db->select('p.id, p.nomor_pembelian, p.tanggal_pembelian, p.id_supplier, p.status, s.nama as nama_supplier');
            $this->db->from('pembelian p');
            $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
            $this->db->where_in('p.status', ['dipesan', 'disetujui', 'sebagian', 'proses']);
            $this->db->where_not_in('p.status', ['selesai', 'dibatalkan', 'ditolak']);
            $this->db->where('EXISTS (SELECT 1 FROM pembelian_detail pd WHERE pd.id_pembelian = p.id)');
            $this->db->order_by('p.tanggal_pembelian', 'desc');
            
            $query = $this->db->get();
            $result = $query->result();
            log_message('info', 'Semua pembelian yang belum selesai: ' . count($result));
        }
        
        return $result;
    }
    
    // Fungsi untuk memeriksa apakah pembelian memiliki item yang belum diterima sepenuhnya
    public function check_pembelian_has_unreceived_items($id_pembelian)
    {
        // Periksa apakah pembelian ada
        $pembelian = $this->get_pembelian_by_id($id_pembelian);
        if (!$pembelian) {
            log_message('error', 'Pembelian dengan ID: ' . $id_pembelian . ' tidak ditemukan');
            return false;
        }
        
        log_message('info', 'Memeriksa item untuk pembelian ID: ' . $id_pembelian . ' dengan status: ' . $pembelian->status);
        
        // Ambil semua item pembelian
        $this->db->select('pd.id, pd.qty, b.nama_barang');
        $this->db->from('pembelian_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->where('pd.id_pembelian', $id_pembelian);
        $query = $this->db->get();
        
        // Log query SQL
        log_message('info', 'SQL query untuk pembelian detail: ' . $this->db->last_query());
        
        $items = $query->result();
        
        if (empty($items)) {
            log_message('info', 'Pembelian ID: ' . $id_pembelian . ' tidak memiliki item');
            return false;
        }
        
        log_message('info', 'Pembelian ID: ' . $id_pembelian . ' memiliki ' . count($items) . ' item');
        
        // Jika pembelian memiliki item, anggap saja memiliki item yang belum diterima
        // Ini untuk mengatasi kasus di mana pembelian baru dibuat dan belum ada penerimaan
        if ($pembelian->status == 'dipesan' || $pembelian->status == 'disetujui') {
            log_message('info', 'Pembelian ID: ' . $id_pembelian . ' dengan status ' . $pembelian->status . ' dianggap memiliki item yang belum diterima');
            return true;
        }
        
        // Periksa setiap item
        foreach ($items as $item) {
            // Hitung total yang sudah diterima
            $this->db->select('COALESCE(SUM(ppd.qty_diterima), 0) as received_qty');
            $this->db->from('penerimaan_pembelian_detail ppd');
            $this->db->join('penerimaan_pembelian pp', 'pp.id = ppd.id_penerimaan', 'left');
            $this->db->where('pp.id_pembelian', $id_pembelian);
            $this->db->where('ppd.id_pembelian_detail', $item->id);
            $received = $this->db->get()->row();
            
            $qty_received = $received ? floatval($received->received_qty) : 0;
            $qty_remaining = floatval($item->qty) - $qty_received;
            
            log_message('info', 'Item ID: ' . $item->id . ', Barang: ' . ($item->nama_barang ?? 'N/A') . 
                ', Qty: ' . $item->qty . ', Qty Diterima: ' . $qty_received . ', Qty Sisa: ' . $qty_remaining);
            
            // Jika masih ada sisa, return true
            if ($qty_remaining > 0) {
                log_message('info', 'Pembelian ID: ' . $id_pembelian . ' memiliki item yang belum diterima sepenuhnya');
                return true;
            }
        }
        
        log_message('info', 'Pembelian ID: ' . $id_pembelian . ' semua item sudah diterima sepenuhnya');
        return false;
    }

    public function get_gudang_aktif()
    {
        $this->db->select('id, kode_gudang, nama_gudang, alamat');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_detail_by_id($id)
    {
        $this->db->select('*');
        $this->db->from($this->table_detail);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pembelian_by_id($id)
    {
        $this->db->select('p.*, s.nama as nama_supplier, s.alamat as alamat_supplier');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        $this->db->where('p.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pembelian_detail($id_pembelian)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan
        ');
        $this->db->from('pembelian_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('pd.id_pembelian', $id_pembelian);
        $query = $this->db->get();
        return $query->result();
    }
    
    // Get pembelian items with remaining quantities to be received
    public function get_pembelian_items_for_receiving($id_pembelian, $id_penerimaan = null)
    {
        // Log untuk debugging
        log_message('info', 'get_pembelian_items_for_receiving dipanggil dengan id_pembelian: ' . $id_pembelian . ', id_penerimaan: ' . ($id_penerimaan ?? 'null'));
        
        if (!$id_pembelian) {
            log_message('error', 'id_pembelian kosong di get_pembelian_items_for_receiving');
            return [];
        }
        
        // Periksa status pembelian
        $pembelian = $this->get_pembelian_by_id($id_pembelian);
        if (!$pembelian) {
            log_message('error', 'Pembelian dengan ID: ' . $id_pembelian . ' tidak ditemukan');
            return [];
        }
        
        log_message('info', 'Status pembelian: ' . $pembelian->status);
        
        // First, get all items from the purchase order
        $this->db->select('
            pd.id,
            pd.id_pembelian,
            pd.id_barang,
            pd.qty as qty_pembelian,
            pd.harga_satuan,
            pd.diskon_persen,
            pd.diskon_nominal,
            pd.subtotal_setelah_diskon as subtotal,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.id as id_satuan
        ');
        $this->db->from('pembelian_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('pd.id_pembelian', $id_pembelian);
        
        // Tambahkan error handling
        try {
            $query = $this->db->get();
            if (!$query) {
                log_message('error', 'Database error in get_pembelian_items_for_receiving: ' . print_r($this->db->error(), true));
                return [];
            }
            $purchase_items = $query->result();
            
            // Log jumlah item yang ditemukan
            log_message('info', 'Jumlah item pembelian ditemukan: ' . count($purchase_items));
            
            // Jika tidak ada item, coba periksa query SQL
            if (empty($purchase_items)) {
                $sql = $this->db->last_query();
                log_message('info', 'SQL query: ' . $sql);
            }
        } catch (Exception $e) {
            log_message('error', 'Exception in get_pembelian_items_for_receiving: ' . $e->getMessage());
            return [];
        }
        
        // If no items in purchase, return empty array
        if (empty($purchase_items)) {
            log_message('info', 'Tidak ada item pembelian ditemukan untuk id_pembelian: ' . $id_pembelian);
            return [];
        }
        
        // For each item, calculate how much has been received already
        foreach ($purchase_items as $item) {
            try {
                // Get already received quantity (excluding current receipt if editing)
                $this->db->select('COALESCE(SUM(ppd.qty_diterima), 0) as received_qty');
                $this->db->from('penerimaan_pembelian_detail ppd');
                $this->db->join('penerimaan_pembelian pp', 'pp.id = ppd.id_penerimaan', 'left');
                $this->db->where('pp.id_pembelian', $id_pembelian);
                $this->db->where('ppd.id_pembelian_detail', $item->id);
                
                if ($id_penerimaan) {
                    $this->db->where('pp.id !=', $id_penerimaan);
                }
                
                $received_query = $this->db->get();
                $received = $received_query->row();
                $item->qty_sudah_diterima = $received ? floatval($received->received_qty) : 0;
                
                // Get quantity already in the current receipt
                if ($id_penerimaan) {
                    $this->db->select('COALESCE(SUM(qty_diterima), 0) as current_qty');
                    $this->db->from('penerimaan_pembelian_detail');
                    $this->db->where('id_penerimaan', $id_penerimaan);
                    $this->db->where('id_pembelian_detail', $item->id);
                    $current_result = $this->db->get()->row();
                    $item->qty_dalam_penerimaan_ini = $current_result ? floatval($current_result->current_qty) : 0;
                } else {
                    $item->qty_dalam_penerimaan_ini = 0;
                }
                
                // Calculate remaining quantity to receive
                $item->qty_sisa = floatval($item->qty_pembelian) - floatval($item->qty_sudah_diterima);
                
                // If editing, add current receipt's quantity to remaining quantity
                if ($id_penerimaan) {
                    $item->qty_sisa += floatval($item->qty_dalam_penerimaan_ini);
                }
                
                // Pastikan nilai tidak negatif
                $item->qty_sisa = max(0, $item->qty_sisa);
                
                // Log untuk debugging
                log_message('info', 'Item ID: ' . $item->id . ', Barang: ' . $item->nama_barang . 
                    ', Qty Pembelian: ' . $item->qty_pembelian . 
                    ', Qty Sudah Diterima: ' . $item->qty_sudah_diterima . 
                    ', Qty Sisa: ' . $item->qty_sisa);
                
            } catch (Exception $e) {
                log_message('error', 'Exception processing item in get_pembelian_items_for_receiving: ' . $e->getMessage());
                // Set default values if there's an error
                $item->qty_sudah_diterima = 0;
                $item->qty_dalam_penerimaan_ini = 0;
                $item->qty_sisa = floatval($item->qty_pembelian);
            }
        }
        
        // Filter hanya item yang masih memiliki qty sisa
        $filtered_items = array_filter($purchase_items, function($item) {
            return isset($item->qty_sisa) && $item->qty_sisa > 0;
        });
        
        // Reindex array
        $filtered_items = array_values($filtered_items);
        
        // Log jumlah item setelah filtering
        log_message('info', 'Jumlah item setelah filtering (qty_sisa > 0): ' . count($filtered_items));
        
        return $filtered_items;
    }
    
    // Integrasi dengan modul barang masuk
    public function create_barang_masuk($penerimaan_id)
    {
        // Get penerimaan data
        $penerimaan = $this->get_by_id($penerimaan_id);
        if (!$penerimaan) {
            return false;
        }
        
        // Create barang masuk header
        $barang_masuk_data = array(
            'nomor_penerimaan' => 'BM-' . substr($penerimaan->nomor_penerimaan, 4), // Convert RCVxxxxxxxx to BM-xxxxxxxx
            'tanggal' => $penerimaan->tanggal_penerimaan,
            'id_supplier' => $penerimaan->id_supplier,
            'jenis' => 'pembelian',
            'ref_nomor' => $penerimaan->nomor_pembelian,
            'keterangan' => 'Dibuat otomatis dari penerimaan pembelian ' . $penerimaan->nomor_penerimaan,
            'status' => 'draft',
            'created_by' => $this->session->userdata('id_user')
        );
        
        $this->db->insert('barang_masuk', $barang_masuk_data);
        $barang_masuk_id = $this->db->insert_id();
        
        if (!$barang_masuk_id) {
            return false;
        }
        
        // Get penerimaan details
        $penerimaan_details = $this->get_detail_by_penerimaan_id($penerimaan_id);
        
        // Log jumlah detail yang ditemukan
        log_message('info', 'Jumlah detail penerimaan: ' . count($penerimaan_details));
        
        // Create barang masuk details
        foreach ($penerimaan_details as $detail) {
            // Log detail item
            log_message('info', 'Detail item: ID Barang=' . $detail->id_barang . ', Qty=' . $detail->qty_diterima);
            
            if ($detail->qty_diterima > 0) {
                // Ambil satuan dari master barang, bukan dari detail penerimaan
                $this->db->select('satuan_id');
                $this->db->from('barang');
                $this->db->where('id', $detail->id_barang);
                $barang_query = $this->db->get();
                $barang_data = $barang_query->row();
                
                $id_satuan_barang = $barang_data ? $barang_data->satuan_id : null;
                
                $barang_masuk_detail_data = array(
                    'id_barang_masuk' => $barang_masuk_id,
                    'id_barang' => $detail->id_barang,
                    'id_gudang' => $detail->id_gudang,
                    'id_satuan' => $id_satuan_barang, // Menggunakan satuan dari master barang
                    'qty_diterima' => $detail->qty_diterima,
                    'keterangan' => $detail->keterangan
                );
                
                $insert_result = $this->db->insert('barang_masuk_detail', $barang_masuk_detail_data);
                
                // Log hasil insert
                if (!$insert_result) {
                    log_message('error', 'Gagal menyimpan detail barang masuk: ' . $this->db->error()['message']);
                } else {
                    log_message('info', 'Detail barang masuk berhasil disimpan dengan satuan ID: ' . $id_satuan_barang);
                }
            }
        }
        
        // Update totals
        $this->db->query("UPDATE barang_masuk SET 
            total_item = (SELECT COUNT(DISTINCT id_barang) FROM barang_masuk_detail WHERE id_barang_masuk = ?),
            total_qty = (SELECT SUM(qty_diterima) FROM barang_masuk_detail WHERE id_barang_masuk = ?)
            WHERE id = ?", array($barang_masuk_id, $barang_masuk_id, $barang_masuk_id));
        
        return $barang_masuk_id;
    }
    
    // Update status pembelian
    public function update_pembelian_status($id_pembelian)
    {
        $this->db->query("CALL sp_update_pembelian_status_after_penerimaan(?)", array($id_pembelian));
    }

    public function update_status_by_nomor_penerimaan($nomor_penerimaan, $status)
    {
        $data = array(
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        );
        $this->db->where('nomor_penerimaan', $nomor_penerimaan);
        $this->db->update($this->table, $data);
        return $this->db->affected_rows();
    }
}