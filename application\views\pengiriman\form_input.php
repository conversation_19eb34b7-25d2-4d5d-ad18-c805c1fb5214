<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id"/> 
    <div class="form-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Nomor Pengiriman</label>
                    <div class="input-group">
                        <input name="nomor_pengiriman" placeholder="Nomor Pengiriman" class="form-control" type="text" value="<?= $nomor_pengiriman ?>">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" onclick="generateNomor()"><i class="fas fa-sync-alt"></i></button>
                        </div>
                        <span class="help-block text-danger"></span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Tanggal Pengiriman</label>
                    <input name="tanggal_pengiriman" placeholder="Tanggal Pengiriman" class="form-control datepicker" type="date" value="<?= date('Y-m-d') ?>">
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Pesanan</label>
                    <select name="id_pesanan" id="id_pesanan" class="form-control select2" style="width: 100%;" onchange="getPesananInfo()">
                        <option value="">-- Pilih Pesanan --</option>
                        <?php foreach ($pesanan_list as $pesanan) : ?>
                            <option value="<?= $pesanan->id ?>"><?= $pesanan->nomor_pesanan ?> - <?= $pesanan->nama_pelanggan ?></option>
                        <?php endforeach; ?>
                    </select>
                    <span class="help-block text-danger"></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Pelanggan</label>
                    <select name="id_pelanggan" id="id_pelanggan" class="form-control select2" style="width: 100%;" disabled>
                        <option value="">-- Pilih Pelanggan --</option>
                        <?php foreach ($pelanggan_list as $pelanggan) : ?>
                            <option value="<?= $pelanggan->id ?>"><?= $pelanggan->nama ?> (<?= $pelanggan->kode ?>)</option>
                        <?php endforeach; ?>
                    </select>
                    <!-- Hidden input to ensure the value is submitted even when the select is disabled -->
                    <input type="hidden" name="id_pelanggan" id="id_pelanggan_hidden">
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label">Alamat Pengiriman</label>
                    <textarea name="alamat_pengiriman" id="alamat_pengiriman" placeholder="Alamat Pengiriman" class="form-control" rows="3"></textarea>
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Status</label>
                    <select name="status" class="form-control" disabled>
                        <option value="draft">Draft</option>
                    </select>
                    <!-- Hidden input to ensure the value is submitted even when the select is disabled -->
                    <input type="hidden" name="status" value="draft">
                    <span class="help-block text-danger"></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Estimasi Tiba</label>
                    <input name="estimasi_tiba" placeholder="Estimasi Tiba" class="form-control datepicker" type="date" value="<?= date('Y-m-d') ?>" required>
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label">Keterangan</label>
                    <textarea name="keterangan" placeholder="Keterangan" class="form-control" rows="3"></textarea>
                    <span class="help-block text-danger"></span>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
    
    function generateNomor() {
        $.ajax({
            url: "<?php echo site_url('pengiriman/generate_nomor') ?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="nomor_pengiriman"]').val(data.nomor);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat membuat nomor pengiriman.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
    
    function getPesananInfo() {
        var id_pesanan = $('#id_pesanan').val();
        if (id_pesanan) {
            $.ajax({
                url: "<?php echo site_url('pengiriman/get_pesanan_info') ?>",
                type: "POST",
                data: {id: id_pesanan},
                dataType: "JSON",
                success: function(response) {
                    if (response.status) {
                        var data = response.data;
                        // Set the visible select for display purposes
                        $('#id_pelanggan').val(data.id_pelanggan).trigger('change');
                        // Set the hidden input to ensure the value is submitted
                        $('#id_pelanggan_hidden').val(data.id_pelanggan);
                        $('#alamat_pengiriman').val(data.alamat_pelanggan);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengambil informasi pesanan.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        } else {
            // Clear customer and address if no order is selected
            $('#id_pelanggan').val('').trigger('change');
            $('#id_pelanggan_hidden').val('');
            $('#alamat_pengiriman').val('');
        }
    }
</script>