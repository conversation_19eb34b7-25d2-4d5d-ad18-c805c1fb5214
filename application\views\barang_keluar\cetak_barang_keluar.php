<?php
// Load print helper
$this->load->helper('print');

// Get data from controller
$barang_keluar = $barang_keluar ?? null;
$detail_list = $detail_list ?? [];

if (!$barang_keluar) {
    echo '<div class="alert alert-danger">Data barang keluar tidak ditemukan!</div>';
    return;
}

// Prepare data untuk template
$template_data = [
    'title' => 'Cetak Barang Keluar',
    'document_number' => $barang_keluar->nomor_pengeluaran ?? 'BK-' . date('Ymd-His'),
    'document_title' => 'LAPORAN BARANG KELUAR'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Transaksi',
        'items' => [
            ['label' => 'Nomor Pengeluaran', 'value' => '<strong>' . ($barang_keluar->nomor_pengeluaran ?? '-') . '</strong>'],
            ['label' => 'Tanggal', 'value' => format_date_indonesia($barang_keluar->tanggal ?? date('Y-m-d'))],
            ['label' => 'Jenis', 'value' => ucwords(str_replace('_', ' ', $barang_keluar->jenis ?? 'penjualan'))],
            ['label' => 'Status', 'value' => get_status_badge($barang_keluar->status ?? 'draft')]
        ]
    ]
];

// Add pelanggan info if available
if (!empty($barang_keluar->nama_pelanggan)) {
    $info_sections[] = [
        'title' => '<span class="icon-user"></span>Informasi Pelanggan',
        'items' => [
            ['label' => 'Nama Pelanggan', 'value' => '<strong>' . $barang_keluar->nama_pelanggan . '</strong>'],
            ['label' => 'Kode Pelanggan', 'value' => $barang_keluar->kode_pelanggan ?? '-'],
            ['label' => 'Alamat', 'value' => $barang_keluar->alamat_pelanggan ?? '-'],
            ['label' => 'Telepon', 'value' => $barang_keluar->telepon_pelanggan ?? '-']
        ]
    ];
}

// Add reference info if available
$ref_items = [];
if (!empty($barang_keluar->ref_nomor)) {
    $ref_items[] = ['label' => 'No. Referensi', 'value' => $barang_keluar->ref_nomor];
}
if (!empty($barang_keluar->keterangan)) {
    $ref_items[] = ['label' => 'Keterangan', 'value' => $barang_keluar->keterangan];
}
if (!empty($barang_keluar->created_by_name)) {
    $ref_items[] = ['label' => 'Dibuat Oleh', 'value' => $barang_keluar->created_by_name];
}
if (!empty($barang_keluar->created_at)) {
    $ref_items[] = ['label' => 'Tanggal Dibuat', 'value' => format_date_indonesia($barang_keluar->created_at, true)];
}

if (!empty($ref_items)) {
    $info_sections[] = [
        'title' => '<span class="icon-info"></span>Informasi Tambahan',
        'items' => $ref_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '30%'],
    ['label' => 'Satuan', 'width' => '8%', 'align' => 'center'],
    ['label' => 'Qty Keluar', 'width' => '10%', 'align' => 'right'],

    ['label' => 'Keterangan', 'width' => '10%']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_qty = 0;
$total_nilai = 0;

if (!empty($detail_list)) {
    foreach ($detail_list as $detail) {
        $nama_barang = $detail->nama_barang ?? 'Barang tidak diketahui';
        if (!empty($detail->merk)) {
            $nama_barang .= ' - ' . $detail->merk;
        }
        if (!empty($detail->tipe)) {
            $nama_barang .= ' (' . $detail->tipe . ')';
        }
        
        $qty_keluar = $detail->qty_keluar ?? 0;
        
        $table_data[] = [
            $no++,
            $detail->kode_barang ?? '-',
            $nama_barang,
            $detail->nama_satuan ?? '-',
            number_format($qty_keluar, 0, ',', '.'),
            !empty($detail->keterangan) ? $detail->keterangan : '-'
        ];
        
        $total_qty += $qty_keluar;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL:</strong>', 'colspan' => '4', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Barang Keluar', $table_headers, $table_data, $table_options);

// Add summary section
$content .= '<div class="summary-section">';
$content .= '<div class="summary-left">';

// Add notes if available
if (!empty($barang_keluar->keterangan)) {
    $content .= create_notes_section('Keterangan', $barang_keluar->keterangan);
}

// Add general notes
$content .= create_notes_section('Catatan Penting', 
    '• Semua barang telah diperiksa kondisi dan jumlahnya sebelum keluar' . "\n" .
    '• Barang keluar telah dicatat dalam sistem inventory' . "\n" .
    '• Stok barang telah diperbarui secara otomatis' . "\n" .
    '• Dokumen ini merupakan bukti sah transaksi barang keluar' . "\n" .
    '• Penerima wajib memeriksa kondisi barang saat diterima');

$content .= '</div>';
$content .= '<div class="summary-right">';

// Summary table
$summary_items = [
    ['label' => 'Total Item', 'value' => count($detail_list) . ' item'],
    ['label' => 'Total Quantity', 'value' => number_format($total_qty, 0, ',', '.')],
];

$content .= create_summary_table($summary_items);

$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Petugas Gudang',
        'name' => $barang_keluar->petugas ?? '(............................)',
        'position' => 'Staff Gudang'
    ],
    [
        'title' => 'Penerima',
        'name' => $barang_keluar->penerima ?? '(............................)',
        'position' => 'Penerima Barang'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Supervisor Gudang'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>