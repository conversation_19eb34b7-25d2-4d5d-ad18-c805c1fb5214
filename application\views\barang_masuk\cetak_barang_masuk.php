<?php
// Load print helper
$this->load->helper('print');

// Get data from controller
$barang_masuk = $barang_masuk ?? null;
$detail_list = $detail_list ?? [];

if (!$barang_masuk) {
    echo '<div class="alert alert-danger">Data barang masuk tidak ditemukan!</div>';
    return;
}

// Prepare data untuk template
$template_data = [
    'title' => 'Cetak Barang Masuk',
    'document_number' => $barang_masuk->nomor_penerimaan ?? 'BM-' . date('Ymd-His'),
    'document_title' => 'LAPORAN BARANG MASUK'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Transaksi',
        'items' => [
            ['label' => 'Nomor Penerimaan', 'value' => '<strong>' . ($barang_masuk->nomor_penerimaan ?? '-') . '</strong>'],
            ['label' => 'Tanggal', 'value' => format_date_indonesia($barang_masuk->tanggal ?? date('Y-m-d'))],
            ['label' => 'Jenis', 'value' => ucwords(str_replace('_', ' ', $barang_masuk->jenis ?? 'pembelian'))],
            ['label' => 'Status', 'value' => get_status_badge($barang_masuk->status ?? 'draft')]
        ]
    ]
];

// Add supplier info if available
if (!empty($barang_masuk->nama_supplier)) {
    $info_sections[] = [
        'title' => '<span class="icon-user"></span>Informasi Supplier',
        'items' => [
            ['label' => 'Nama Supplier', 'value' => '<strong>' . $barang_masuk->nama_supplier . '</strong>'],
            ['label' => 'Kode Supplier', 'value' => $barang_masuk->kode_supplier ?? '-'],
            ['label' => 'Alamat', 'value' => $barang_masuk->alamat_supplier ?? '-'],
            ['label' => 'Telepon', 'value' => $barang_masuk->telepon_supplier ?? '-']
        ]
    ];
}

// Add reference info if available
$ref_items = [];
if (!empty($barang_masuk->ref_nomor)) {
    $ref_items[] = ['label' => 'No. Referensi', 'value' => $barang_masuk->ref_nomor];
}
if (!empty($barang_masuk->keterangan)) {
    $ref_items[] = ['label' => 'Keterangan', 'value' => $barang_masuk->keterangan];
}
if (!empty($barang_masuk->created_by_name)) {
    $ref_items[] = ['label' => 'Dibuat Oleh', 'value' => $barang_masuk->created_by_name];
}
if (!empty($barang_masuk->created_at)) {
    $ref_items[] = ['label' => 'Tanggal Dibuat', 'value' => format_date_indonesia($barang_masuk->created_at, true)];
}

if (!empty($ref_items)) {
    $info_sections[] = [
        'title' => '<span class="icon-info"></span>Informasi Tambahan',
        'items' => $ref_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '30%'],
    ['label' => 'Satuan', 'width' => '8%', 'align' => 'center'],
    ['label' => 'Qty Masuk', 'width' => '10%', 'align' => 'right'],

    ['label' => 'Keterangan', 'width' => '10%']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_qty = 0;
$total_nilai = 0;

if (!empty($detail_list)) {
    foreach ($detail_list as $detail) {
        $nama_barang = $detail->nama_barang ?? 'Barang tidak diketahui';
        if (!empty($detail->merk)) {
            $nama_barang .= ' - ' . $detail->merk;
        }
        if (!empty($detail->tipe)) {
            $nama_barang .= ' (' . $detail->tipe . ')';
        }
        
        $qty_diterima = $detail->qty_diterima ?? 0;
        
        $table_data[] = [
            $no++,
            $detail->kode_barang ?? '-',
            $nama_barang,
            $detail->nama_satuan ?? '-',
            number_format($qty_diterima, 0, ',', '.'),
            !empty($detail->keterangan) ? $detail->keterangan : '-'
        ];
        
        $total_qty += $qty_diterima;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL:</strong>', 'colspan' => '4', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Barang Masuk', $table_headers, $table_data, $table_options);

// Add summary section
$content .= '<div class="summary-section">';
$content .= '<div class="summary-left">';

// Add notes if available
if (!empty($barang_masuk->keterangan)) {
    $content .= create_notes_section('Keterangan', $barang_masuk->keterangan);
}

// Add general notes
$content .= create_notes_section('Catatan Penting', 
    '• Semua barang telah diperiksa kondisi dan jumlahnya' . "\n" .
    '• Barang masuk telah dicatat dalam sistem inventory' . "\n" .
    '• Stok barang telah diperbarui secara otomatis' . "\n" .
    '• Dokumen ini merupakan bukti sah transaksi barang masuk');

$content .= '</div>';
$content .= '<div class="summary-right">';

// Summary table
$summary_items = [
    ['label' => 'Total Item', 'value' => count($detail_list) . ' item'],
    ['label' => 'Total Quantity', 'value' => number_format($total_qty, 0, ',', '.')]
];

$content .= create_summary_table($summary_items);

$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Petugas Gudang',
        'name' => $barang_masuk->petugas ?? '(............................)',
        'position' => 'Staff Gudang'
    ],
    [
        'title' => 'Supervisor',
        'name' => '(............................)',
        'position' => 'Supervisor Gudang'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Manager'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>