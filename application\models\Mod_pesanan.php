<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Pesanan Pelanggan
 * Mengatur data pesanan pelanggan dan detailnya
 */
class Mod_pesanan extends CI_Model
{
    var $table = 'pesanan';
    var $table_detail = 'pesanan_detail';
    var $column_search = array(
        'p.nomor_pesanan', 
        'p.tanggal_pesanan', 
        'pel.nama as nama_pelanggan', 
        'p.jenis_pesanan',
        'p.status', 
        'p.keterangan'
    );
    var $column_order = array(
        'p.id', 
        'p.nomor_pesanan', 
        'p.tanggal_pesanan', 
        'pel.nama', 
        'p.jenis_pesanan',
        'p.status', 
        'p.total_item',
        'p.total_qty',
        'p.total_akhir'
    );
    var $order = array('p.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            p.id,
            p.nomor_pesanan,
            p.tanggal_pesanan,
            p.id_pelanggan,
            p.jenis_pesanan,
            p.status,
            p.total_item,
            p.total_qty,
            p.subtotal,
            p.ppn_persen,
            p.ppn_nominal,
            p.total_setelah_ppn,
            p.total_akhir,
            p.keterangan,
            p.created_by,
            p.updated_by,
            p.created_at,
            p.updated_at,
            pel.nama as nama_pelanggan,
            pel.kode as kode_pelanggan,
            pel.alamat as alamat_pelanggan,
            pel.no_telepon
        ');
        $this->db->from($this->table . ' p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->select('
            p.*,
            pel.nama as nama_pelanggan,
            pel.kode as kode_pelanggan,
            pel.alamat as alamat_pelanggan,
            pel.no_telepon,
            pel.email
        ');
        $this->db->from($this->table . ' p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->where('p.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function save($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    public function delete_by_id($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Method konsisten dengan modul lain
    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
        return $this->db->affected_rows();
    }

    public function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }

    // Detail pesanan functions
    public function get_detail_by_pesanan_id($id_pesanan)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            b.jenis_pajak_id,
            s.nama_satuan,
            s.kode_satuan,
            jp.nama_pajak,
            jp.tarif_persen as pajak_barang_persen
        ');
        $this->db->from($this->table_detail . ' pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('jenis_pajak jp', 'b.jenis_pajak_id = jp.id', 'left');
        $this->db->where('pd.id_pesanan', $id_pesanan);
        $this->db->order_by('pd.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    public function update_detail($where, $data)
    {
        $this->db->update($this->table_detail, $data, $where);
        return $this->db->affected_rows();
    }

    public function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    public function delete_detail_by_pesanan($id_pesanan)
    {
        $this->db->where('id_pesanan', $id_pesanan);
        $this->db->delete($this->table_detail);
    }

    // Generate nomor pesanan - Format: PSN-YYYYMMDD-XXXX
    public function generate_nomor_pesanan()
    {
        $prefix = 'PSN';
        $date = date('Ymd');
        
        $this->db->select('nomor_pesanan');
        $this->db->from($this->table);
        $this->db->like('nomor_pesanan', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_pesanan', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_pesanan;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Update total pesanan dengan PPN
    public function update_total_pesanan($id_pesanan)
    {
        $this->db->select('
            COUNT(*) as total_item, 
            SUM(qty) as total_qty, 
            SUM(subtotal) as subtotal_detail,
            SUM(ppn_nominal) as ppn_nominal_detail,
            SUM(total_setelah_ppn) as total_setelah_ppn_detail
        ');
        $this->db->from($this->table_detail);
        $this->db->where('id_pesanan', $id_pesanan);
        $query = $this->db->get();
        $result = $query->row();
        
        $subtotal = $result->subtotal_detail ? $result->subtotal_detail : 0;
        $ppn_nominal = $result->ppn_nominal_detail ? $result->ppn_nominal_detail : 0;
        $total_setelah_ppn = $result->total_setelah_ppn_detail ? $result->total_setelah_ppn_detail : 0;
        
        // Hitung persentase PPN rata-rata (untuk display)
        $ppn_persen = 11.00; // Default PPN
        if ($subtotal > 0) {
            $ppn_persen = round(($ppn_nominal / $subtotal) * 100, 2);
        }
        
        $data = array(
            'total_item' => $result->total_item,
            'total_qty' => $result->total_qty ? $result->total_qty : 0,
            'subtotal' => $subtotal,
            'ppn_persen' => $ppn_persen,
            'ppn_nominal' => $ppn_nominal,
            'total_setelah_ppn' => $total_setelah_ppn,
            'total_akhir' => $total_setelah_ppn, // Total akhir sama dengan total setelah PPN
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id_pesanan);
        $this->db->update($this->table, $data);
    }

    // Method untuk mendapatkan data dropdown
    public function get_pelanggan_aktif()
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('pelanggan');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_barang_aktif()
    {
        $this->db->select('
            b.id, 
            b.kode_barang, 
            b.nama_barang, 
            b.merk, 
            b.tipe, 
            b.harga_jual, 
            b.jenis_pajak_id,
            s.nama_satuan,
            jp.nama_pajak,
            jp.tarif_persen as pajak_persen
        ');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('jenis_pajak jp', 'b.jenis_pajak_id = jp.id', 'left');
        $this->db->where('b.aktif', 1);
        $this->db->order_by('b.nama_barang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    
    public function get_detail_by_id($id)
    {
        $this->db->select('*');
        $this->db->from($this->table_detail);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pelanggan_by_id($id)
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('pelanggan');
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

}