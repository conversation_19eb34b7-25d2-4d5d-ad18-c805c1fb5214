<?php
// Template functions untuk cetak (sama seperti laporan stok)
function create_header_section($title, $document_number, $document_title) {
    $content = '<div class="header-section">';
    $content .= '<div class="company-info">';
    $content .= '<h1 class="company-name">TOKO ELEKTRONIK</h1>';
    $content .= '<div class="company-details">';
    $content .= 'Jl. Contoh No. 123, Kota Contoh<br>';
    $content .= 'Telp: (021) 1234567 | Email: <EMAIL>';
    $content .= '</div>';
    $content .= '</div>';
    $content .= '<div class="document-info">';
    $content .= '<h2 class="document-title">' . $document_title . '</h2>';
    $content .= '<div class="document-number">No: ' . $document_number . '</div>';
    $content .= '</div>';
    $content .= '</div>';
    return $content;
}

function create_info_section($title, $items) {
    $content = '<div class="info-section">';
    $content .= '<h3 class="section-title">' . $title . '</h3>';
    $content .= '<div class="info-grid">';
    foreach ($items as $item) {
        $content .= '<div class="info-item">';
        $content .= '<span class="info-label">' . $item['label'] . '</span>';
        $content .= '<span class="info-value">' . $item['value'] . '</span>';
        $content .= '</div>';
    }
    $content .= '</div>';
    $content .= '</div>';
    return $content;
}

function create_table_section($title, $headers, $data, $totals = null) {
    $content = '<div class="table-section">';
    if ($title) {
        $content .= '<h3 class="section-title">' . $title . '</h3>';
    }
    $content .= '<table class="data-table">';
    
    // Headers
    $content .= '<thead><tr>';
    foreach ($headers as $header) {
        $width = isset($header['width']) ? ' style="width: ' . $header['width'] . '"' : '';
        $align = isset($header['align']) ? ' class="text-' . $header['align'] . '"' : '';
        $content .= '<th' . $width . $align . '>' . $header['label'] . '</th>';
    }
    $content .= '</tr></thead>';
    
    // Data
    $content .= '<tbody>';
    foreach ($data as $row) {
        $content .= '<tr>';
        for ($i = 0; $i < count($headers); $i++) {
            $align = isset($headers[$i]['align']) ? ' class="text-' . $headers[$i]['align'] . '"' : '';
            $content .= '<td' . $align . '>' . (isset($row[$i]) ? $row[$i] : '') . '</td>';
        }
        $content .= '</tr>';
    }
    $content .= '</tbody>';
    
    // Totals
    if ($totals) {
        $content .= '<tfoot>';
        foreach ($totals as $total_row) {
            $content .= '<tr class="total-row">';
            for ($i = 0; $i < count($headers); $i++) {
                $align = isset($headers[$i]['align']) ? ' class="text-' . $headers[$i]['align'] . '"' : '';
                $class = isset($total_row[$i]['class']) ? ' ' . $total_row[$i]['class'] : '';
                $colspan = isset($total_row[$i]['colspan']) ? ' colspan="' . $total_row[$i]['colspan'] . '"' : '';
                $content .= '<td' . $align . $class . $colspan . '>' . (isset($total_row[$i]['value']) ? $total_row[$i]['value'] : (isset($total_row[$i]) ? $total_row[$i] : '')) . '</td>';
            }
            $content .= '</tr>';
        }
        $content .= '</tfoot>';
    }
    
    $content .= '</table>';
    $content .= '</div>';
    return $content;
}

function create_notes_section($title, $content) {
    $notes = '<div class="notes-section">';
    $notes .= '<h3 class="section-title">' . $title . '</h3>';
    $notes .= '<div class="notes-content">' . nl2br($content) . '</div>';
    $notes .= '</div>';
    return $notes;
}

function create_signature_section($signatures) {
    $content = '<div class="signature-section">';
    foreach ($signatures as $sig) {
        $content .= '<div class="signature-box">';
        $content .= '<div class="signature-title">' . $sig['title'] . '</div>';
        $content .= '<div class="signature-space"></div>';
        $content .= '<div class="signature-name">' . $sig['name'] . '</div>';
        $content .= '<div class="signature-position">' . $sig['position'] . '</div>';
        $content .= '</div>';
    }
    $content .= '</div>';
    return $content;
}

// Definisi judul laporan
$judul_laporan = array(
    'pembelian_periode' => 'LAPORAN PEMBELIAN PERIODE',
    'detail_pembelian' => 'LAPORAN DETAIL PEMBELIAN',
    'pembelian_supplier' => 'LAPORAN PEMBELIAN PER SUPPLIER',
    'pembelian_barang' => 'LAPORAN PEMBELIAN PER BARANG',
    'outstanding_po' => 'LAPORAN OUTSTANDING PURCHASE ORDER'
);

$periode_text = '';
if (!empty($filter['tanggal_dari']) && !empty($filter['tanggal_sampai'])) {
    $periode_text = ' Periode: ' . date('d/m/Y', strtotime($filter['tanggal_dari'])) . ' - ' . date('d/m/Y', strtotime($filter['tanggal_sampai']));
}

// Template data
$template_data = [
    'title' => 'Cetak Laporan Pembelian',
    'document_number' => 'LPB-' . date('Ymd-His'),
    'document_title' => ($judul_laporan[$jenis_laporan] ?? 'LAPORAN PEMBELIAN') . $periode_text
];

$info_sections = [];

// Filter info
$filter_items = [];
if (!empty($info_supplier)) {
    $filter_items[] = ['label' => 'Supplier', 'value' => $info_supplier];
}
if (!empty($info_barang)) {
    $filter_items[] = ['label' => 'Barang', 'value' => $info_barang];
}
if (!empty($filter['status'])) {
    $filter_items[] = ['label' => 'Status', 'value' => ucfirst($filter['status'])];
}
$filter_items[] = ['label' => 'Tanggal Cetak', 'value' => date('d/m/Y H:i:s')];
$filter_items[] = ['label' => 'User', 'value' => $this->session->userdata('nama_user')];

if (!empty($filter_items)) {
    $info_sections[] = [
        'title' => 'Informasi Filter',
        'items' => $filter_items
    ];
}

// Prepare content
$content = '';

// Add info sections
foreach ($info_sections as $section) {
    $content .= create_info_section($section['title'], $section['items']);
}

// Process data berdasarkan jenis laporan
if ($laporan && $laporan->num_rows() > 0) {
    $laporan_data = $laporan;
    
    switch ($jenis_laporan) {
        case 'pembelian_periode':
            $headers = [
                ['label' => 'No', 'width' => '5%', 'align' => 'center'],
                ['label' => 'Nomor Pembelian', 'width' => '15%'],
                ['label' => 'Tanggal', 'width' => '10%', 'align' => 'center'],
                ['label' => 'Supplier', 'width' => '20%'],
                ['label' => 'Jenis', 'width' => '10%', 'align' => 'center'],
                ['label' => 'Status', 'width' => '10%', 'align' => 'center'],
                ['label' => 'Item', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Qty', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Total', 'width' => '14%', 'align' => 'right']
            ];
            
            $data_rows = [];
            $no = 1;
            $total_item = 0;
            $total_qty = 0;
            $total_nilai = 0;
            
            foreach ($laporan_data->result() as $data) {
                $total_item += $data->total_item;
                $total_qty += $data->total_qty;
                $total_nilai += $data->total_akhir;
                
                $data_rows[] = [
                    $no++,
                    $data->nomor_pembelian,
                    date('d/m/Y', strtotime($data->tanggal_pembelian)),
                    $data->nama_supplier,
                    ucfirst($data->jenis_pembelian),
                    ucfirst($data->status),
                    number_format($data->total_item, 0),
                    number_format($data->total_qty, 0),
                    'Rp ' . number_format($data->total_akhir, 0, ',', '.')
                ];
            }
            
            $totals = [[
                ['value' => '<strong>TOTAL:</strong>', 'colspan' => '6', 'align' => 'right'],
                ['value' => '<strong>' . number_format($total_item, 0) . '</strong>', 'align' => 'right'],
                ['value' => '<strong>' . number_format($total_qty, 0) . '</strong>', 'align' => 'right'],
                ['value' => '<strong>Rp ' . number_format($total_nilai, 0, ',', '.') . '</strong>', 'align' => 'right']
            ]];
            
            $content .= create_table_section('', $headers, $data_rows, $totals);
            break;
            
        case 'detail_pembelian':
            $headers = [
                ['label' => 'No', 'width' => '4%', 'align' => 'center'],
                ['label' => 'No. Pembelian', 'width' => '12%'],
                ['label' => 'Tanggal', 'width' => '8%', 'align' => 'center'],
                ['label' => 'Supplier', 'width' => '15%'],
                ['label' => 'Kode Barang', 'width' => '10%'],
                ['label' => 'Nama Barang', 'width' => '20%'],
                ['label' => 'Satuan', 'width' => '6%', 'align' => 'center'],
                ['label' => 'Qty', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Harga', 'width' => '9%', 'align' => 'right'],
                ['label' => 'Total', 'width' => '8%', 'align' => 'right']
            ];
            
            $data_rows = [];
            $no = 1;
            $total_qty = 0;
            $total_nilai = 0;
            
            foreach ($laporan_data->result() as $data) {
                $total_qty += $data->qty;
                $total_nilai += $data->total_akhir;
                
                $data_rows[] = [
                    $no++,
                    $data->nomor_pembelian,
                    date('d/m/Y', strtotime($data->tanggal_pembelian)),
                    $data->nama_supplier,
                    $data->kode_barang,
                    $data->nama_barang,
                    $data->nama_satuan,
                    number_format($data->qty, 2),
                    'Rp ' . number_format($data->harga_satuan, 0, ',', '.'),
                    'Rp ' . number_format($data->total_akhir, 0, ',', '.')
                ];
            }
            
            $totals = [[
                ['value' => '<strong>TOTAL:</strong>', 'colspan' => '7', 'align' => 'right'],
                ['value' => '<strong>' . number_format($total_qty, 2) . '</strong>', 'align' => 'right'],
                ['value' => '', 'align' => 'right'],
                ['value' => '<strong>Rp ' . number_format($total_nilai, 0, ',', '.') . '</strong>', 'align' => 'right']
            ]];
            
            $content .= create_table_section('', $headers, $data_rows, $totals);
            break;
            
        case 'pembelian_supplier':
            $headers = [
                ['label' => 'No', 'width' => '5%', 'align' => 'center'],
                ['label' => 'Kode Supplier', 'width' => '12%'],
                ['label' => 'Nama Supplier', 'width' => '25%'],
                ['label' => 'Transaksi', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Item', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Qty', 'width' => '10%', 'align' => 'right'],
                ['label' => 'Total Beli', 'width' => '16%', 'align' => 'right'],
                ['label' => 'Outstanding', 'width' => '16%', 'align' => 'right']
            ];
            
            $data_rows = [];
            $no = 1;
            $total_transaksi = 0;
            $total_item = 0;
            $total_qty = 0;
            $total_pembelian = 0;
            $total_outstanding = 0;
            
            foreach ($laporan_data->result() as $data) {
                $total_transaksi += $data->total_transaksi;
                $total_item += $data->total_item;
                $total_qty += $data->total_qty;
                $total_pembelian += $data->total_pembelian;
                $total_outstanding += $data->sisa_pembayaran;
                
                $data_rows[] = [
                    $no++,
                    $data->kode_supplier,
                    $data->nama_supplier,
                    number_format($data->total_transaksi, 0),
                    number_format($data->total_item, 0),
                    number_format($data->total_qty, 0),
                    'Rp ' . number_format($data->total_pembelian, 0, ',', '.'),
                    'Rp ' . number_format($data->sisa_pembayaran, 0, ',', '.')
                ];
            }
            
            $totals = [[
                ['value' => '<strong>TOTAL:</strong>', 'colspan' => '3', 'align' => 'right'],
                ['value' => '<strong>' . number_format($total_transaksi, 0) . '</strong>', 'align' => 'right'],
                ['value' => '<strong>' . number_format($total_item, 0) . '</strong>', 'align' => 'right'],
                ['value' => '<strong>' . number_format($total_qty, 0) . '</strong>', 'align' => 'right'],
                ['value' => '<strong>Rp ' . number_format($total_pembelian, 0, ',', '.') . '</strong>', 'align' => 'right'],
                ['value' => '<strong>Rp ' . number_format($total_outstanding, 0, ',', '.') . '</strong>', 'align' => 'right']
            ]];
            
            $content .= create_table_section('', $headers, $data_rows, $totals);
            break;
            
        case 'pembelian_barang':
            $headers = [
                ['label' => 'No', 'width' => '5%', 'align' => 'center'],
                ['label' => 'Kode Barang', 'width' => '12%'],
                ['label' => 'Nama Barang', 'width' => '25%'],
                ['label' => 'Merk', 'width' => '12%'],
                ['label' => 'Satuan', 'width' => '8%', 'align' => 'center'],
                ['label' => 'Total Qty', 'width' => '10%', 'align' => 'right'],
                ['label' => 'Harga Rata²', 'width' => '12%', 'align' => 'right'],
                ['label' => 'Total Beli', 'width' => '16%', 'align' => 'right']
            ];
            
            $data_rows = [];
            $no = 1;
            $total_qty = 0;
            $total_pembelian = 0;
            
            foreach ($laporan_data->result() as $data) {
                $total_qty += $data->total_qty;
                $total_pembelian += $data->total_pembelian;
                
                $data_rows[] = [
                    $no++,
                    $data->kode_barang,
                    $data->nama_barang,
                    $data->merk,
                    $data->nama_satuan,
                    number_format($data->total_qty, 2),
                    'Rp ' . number_format($data->harga_rata_rata, 0, ',', '.'),
                    'Rp ' . number_format($data->total_pembelian, 0, ',', '.')
                ];
            }
            
            $totals = [[
                ['value' => '<strong>TOTAL:</strong>', 'colspan' => '5', 'align' => 'right'],
                ['value' => '<strong>' . number_format($total_qty, 2) . '</strong>', 'align' => 'right'],
                ['value' => '', 'align' => 'right'],
                ['value' => '<strong>Rp ' . number_format($total_pembelian, 0, ',', '.') . '</strong>', 'align' => 'right']
            ]];
            
            $content .= create_table_section('', $headers, $data_rows, $totals);
            break;
            
        case 'outstanding_po':
            $headers = [
                ['label' => 'No', 'width' => '5%', 'align' => 'center'],
                ['label' => 'Nomor PO', 'width' => '15%'],
                ['label' => 'Tanggal', 'width' => '10%', 'align' => 'center'],
                ['label' => 'Supplier', 'width' => '20%'],
                ['label' => 'Status', 'width' => '10%', 'align' => 'center'],
                ['label' => 'Total PO', 'width' => '15%', 'align' => 'right'],
                ['label' => 'Umur (Hari)', 'width' => '10%', 'align' => 'right'],
                ['label' => 'Outstanding', 'width' => '15%', 'align' => 'right']
            ];
            
            $data_rows = [];
            $no = 1;
            $total_po = 0;
            $total_outstanding = 0;
            
            foreach ($laporan_data->result() as $data) {
                $total_po += $data->total_akhir;
                $total_outstanding += $data->sisa_pembayaran;
                $umur_po = floor((strtotime(date('Y-m-d')) - strtotime($data->tanggal_pembelian)) / (60 * 60 * 24));
                
                $data_rows[] = [
                    $no++,
                    $data->nomor_pembelian,
                    date('d/m/Y', strtotime($data->tanggal_pembelian)),
                    $data->nama_supplier,
                    ucfirst($data->status),
                    'Rp ' . number_format($data->total_akhir, 0, ',', '.'),
                    number_format($umur_po, 0),
                    'Rp ' . number_format($data->sisa_pembayaran, 0, ',', '.')
                ];
            }
            
            $totals = [[
                ['value' => '<strong>TOTAL:</strong>', 'colspan' => '5', 'align' => 'right'],
                ['value' => '<strong>Rp ' . number_format($total_po, 0, ',', '.') . '</strong>', 'align' => 'right'],
                ['value' => '', 'align' => 'right'],
                ['value' => '<strong>Rp ' . number_format($total_outstanding, 0, ',', '.') . '</strong>', 'align' => 'right']
            ]];
            
            $content .= create_table_section('', $headers, $data_rows, $totals);
            break;
    }
    
    // Add summary info
    $summary_items = [
        ['label' => 'Total Data', 'value' => number_format($laporan->num_rows(), 0) . ' record']
    ];
    
    $content .= create_notes_section('Ringkasan Laporan', 
        'Total Data: ' . number_format($laporan->num_rows(), 0) . ' record' . "\n" .
        'Tanggal Generate: ' . date('d/m/Y H:i:s') . "\n" .
        'User: ' . $this->session->userdata('nama_user')
    );
    
} else {
    $content .= '<div class="no-data-message">';
    $content .= '<h3>Data Tidak Ditemukan</h3>';
    $content .= '<p>Tidak ada data yang sesuai dengan filter yang dipilih.</p>';
    $content .= '</div>';
}

// Add signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => $this->session->userdata('nama_user'),
        'position' => 'Staff Purchasing'
    ],
    [
        'title' => 'Disetujui Oleh',
        'name' => '(............................)',
        'position' => 'Manager Purchasing'
    ]
];

$content .= create_signature_section($signatures);
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?= $template_data['title'] ?></title>
    <style>
        /* CSS sama seperti laporan stok */
        @page {
            size: A4;
            margin: 1cm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #333;
        }
        
        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #333;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 10px;
            color: #666;
            line-height: 1.3;
        }
        
        .document-info {
            text-align: right;
            flex: 1;
        }
        
        .document-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .document-number {
            font-size: 12px;
            color: #666;
        }
        
        .info-section {
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            padding-bottom: 3px;
            border-bottom: 1px solid #ddd;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .table-section {
            margin-bottom: 15px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 4px 6px;
            vertical-align: top;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
            color: #2c3e50;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .data-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        
        .data-table .total-row {
            background-color: #e9ecef !important;
            font-weight: bold;
        }
        
        .text-left { text-align: left !important; }
        .text-center { text-align: center !important; }
        .text-right { text-align: right !important; }
        
        .notes-section {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        .notes-content {
            font-size: 10px;
            line-height: 1.4;
            color: #555;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            page-break-inside: avoid;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .signature-space {
            height: 60px;
            border-bottom: 1px solid #333;
            margin-bottom: 5px;
        }
        
        .signature-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .signature-position {
            font-size: 10px;
            color: #666;
        }
        
        .no-data-message {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-data-message h3 {
            margin-bottom: 10px;
            color: #999;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-print {
                display: none;
            }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                text-align: center;
            }
            
            .document-info {
                text-align: center;
                margin-top: 10px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .signature-section {
                flex-direction: column;
                align-items: center;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <?= create_header_section($template_data['title'], $template_data['document_number'], $template_data['document_title']) ?>
    <?= $content ?>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>