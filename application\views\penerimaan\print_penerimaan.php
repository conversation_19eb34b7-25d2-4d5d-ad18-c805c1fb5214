<?php
// Load print helper
$this->load->helper('print');

// Get data from controller
$penerimaan = $penerimaan ?? null;
$detail_items = $detail_items ?? [];

if (!$penerimaan) {
    echo '<div class="alert alert-danger">Data penerimaan tidak ditemukan!</div>';
    return;
}

// Prepare data untuk template
$template_data = [
    'title' => 'Print Penerimaan',
    'document_number' => $penerimaan->nomor_penerimaan ?? 'PN-' . date('Ymd-His'),
    'document_title' => 'TANDA TERIMA BARANG'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Penerimaan',
        'items' => [
            ['label' => 'Nomor Penerimaan', 'value' => '<strong>' . ($penerimaan->nomor_penerimaan ?? '-') . '</strong>'],
            ['label' => 'Tanggal Penerimaan', 'value' => format_date_indonesia($penerimaan->tanggal_penerimaan ?? date('Y-m-d'))],
            ['label' => 'Status', 'value' => get_status_badge($penerimaan->status ?? 'draft')],
            ['label' => 'Penerima', 'value' => $penerimaan->penerima ?? '-']
        ]
    ]
];

// Add supplier info if available
if (!empty($penerimaan->nama_supplier)) {
    $info_sections[] = [
        'title' => '<span class="icon-user"></span>Informasi Supplier',
        'items' => [
            ['label' => 'Nama Supplier', 'value' => '<strong>' . $penerimaan->nama_supplier . '</strong>'],
            ['label' => 'Kode Supplier', 'value' => $penerimaan->kode_supplier ?? '-'],
            ['label' => 'Alamat', 'value' => $penerimaan->alamat_supplier ?? '-'],
            ['label' => 'Telepon', 'value' => $penerimaan->telepon_supplier ?? '-'],
            ['label' => 'Email', 'value' => $penerimaan->email_supplier ?? '-']
        ]
    ];
}

// Add reference documents if available
$ref_items = [];
if (!empty($penerimaan->nomor_pembelian)) {
    $ref_items[] = ['label' => 'No. Pembelian', 'value' => $penerimaan->nomor_pembelian];
}
if (!empty($penerimaan->nomor_surat_jalan)) {
    $ref_items[] = ['label' => 'No. Surat Jalan', 'value' => $penerimaan->nomor_surat_jalan];
}
if (!empty($penerimaan->tanggal_surat_jalan)) {
    $ref_items[] = ['label' => 'Tgl. Surat Jalan', 'value' => format_date_indonesia($penerimaan->tanggal_surat_jalan)];
}
if (!empty($penerimaan->keterangan)) {
    $ref_items[] = ['label' => 'Keterangan', 'value' => $penerimaan->keterangan];
}

if (!empty($ref_items)) {
    $info_sections[] = [
        'title' => '<span class="icon-document"></span>Dokumen Referensi',
        'items' => $ref_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '25%'],
    ['label' => 'Gudang', 'width' => '12%'],
    ['label' => 'Satuan', 'width' => '8%', 'align' => 'center'],
    ['label' => 'Qty Pembelian', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Qty Diterima', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Qty Ditolak', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Keterangan', 'width' => '8%']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_qty_pembelian = 0;
$total_qty_diterima = 0;
$total_qty_ditolak = 0;

if (!empty($detail_items)) {
    foreach ($detail_items as $item) {
        $nama_barang = $item->nama_barang ?? 'Barang tidak diketahui';
        if (!empty($item->merk)) {
            $nama_barang .= ' - ' . $item->merk;
        }
        if (!empty($item->tipe)) {
            $nama_barang .= ' (' . $item->tipe . ')';
        }
        
        $qty_pembelian = $item->qty_pembelian ?? 0;
        $qty_diterima = $item->qty_diterima ?? 0;
        $qty_ditolak = $item->qty_ditolak ?? 0;
        
        // Highlight jika qty diterima tidak sesuai dengan yang dipesan
        $qty_diterima_display = number_format($qty_diterima, 0, ',', '.');
        if ($qty_diterima != $qty_pembelian) {
            $qty_diterima_display = '<span class="color-warning font-bold">' . $qty_diterima_display . '</span>';
        }
        
        // Highlight qty ditolak jika ada
        $qty_ditolak_display = number_format($qty_ditolak, 0, ',', '.');
        if ($qty_ditolak > 0) {
            $qty_ditolak_display = '<span class="color-danger font-bold">' . $qty_ditolak_display . '</span>';
        }
        
        $table_data[] = [
            $no++,
            $item->kode_barang ?? '-',
            $nama_barang,
            $item->nama_gudang ?? '-',
            $item->nama_satuan ?? '-',
            number_format($qty_pembelian, 0, ',', '.'),
            $qty_diterima_display,
            $qty_ditolak_display,
            $item->keterangan ?? '-'
        ];
        
        $total_qty_pembelian += $qty_pembelian;
        $total_qty_diterima += $qty_diterima;
        $total_qty_ditolak += $qty_ditolak;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL:</strong>', 'colspan' => '5', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty_pembelian, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty_diterima, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty_ditolak, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);

// Add table only if there's data
if (!empty($table_data)) {
    $content .= create_data_table('<span class="icon-package"></span>Detail Barang Diterima', $table_headers, $table_data, $table_options);
} else {
    $content .= '<div class="notes-section">';
    $content .= '<div class="notes-title">Tidak Ada Data</div>';
    $content .= '<div class="notes-content">Tidak ada detail item dalam penerimaan ini.</div>';
    $content .= '</div>';
}

// Add summary section only if there's data
if (!empty($table_data)) {
    // Add discrepancy summary if any
    if ($total_qty_pembelian != $total_qty_diterima || $total_qty_ditolak > 0) {
        $discrepancy_items = [
            ['label' => 'Total Qty Pembelian', 'value' => number_format($total_qty_pembelian, 0, ',', '.')],
            ['label' => 'Total Qty Diterima', 'value' => number_format($total_qty_diterima, 0, ',', '.')],
            ['label' => 'Total Qty Ditolak', 'value' => number_format($total_qty_ditolak, 0, ',', '.')],
            ['label' => 'Selisih', 'value' => number_format($total_qty_diterima - $total_qty_pembelian, 0, ',', '.'), 'class' => 'total-final']
        ];
        
        $content .= '<div class="summary-section">';
        $content .= '<div class="summary-left">';
        
        $notes_content = '';
        if ($total_qty_pembelian != $total_qty_diterima) {
            $notes_content .= 'Terdapat perbedaan antara qty yang dipesan dengan qty yang diterima. ';
        }
        if ($total_qty_ditolak > 0) {
            $notes_content .= 'Terdapat barang yang ditolak sebanyak ' . number_format($total_qty_ditolak, 0, ',', '.') . ' unit. ';
        }
        $notes_content .= 'Harap lakukan koordinasi dengan supplier untuk menyelesaikan perbedaan ini. ' .
            'Barang yang rusak atau cacat harus segera dilaporkan untuk proses klaim.';
        
        $content .= create_notes_section('Catatan Penting', $notes_content);
        $content .= '</div>';
        $content .= '<div class="summary-right">';
        $content .= create_summary_table($discrepancy_items);
        $content .= '</div>';
        $content .= '</div>';
    } else {
        // Summary normal jika tidak ada discrepancy
        $content .= '<div class="summary-section">';
        $content .= '<div class="summary-left">';
        $content .= create_notes_section('Ringkasan Penerimaan', 
            'Semua barang telah diterima sesuai dengan pembelian. ' .
            'Tidak ada barang yang ditolak atau mengalami discrepancy. ' .
            'Proses penerimaan berjalan dengan lancar.');
        $content .= '</div>';
        $content .= '<div class="summary-right">';
        
        $summary_items = [
            ['label' => 'Total Item', 'value' => count($table_data) . ' item'],
            ['label' => 'Total Qty Diterima', 'value' => number_format($total_qty_diterima, 0, ',', '.'), 'class' => 'total-final']
        ];
        
        $content .= create_summary_table($summary_items);
        $content .= '</div>';
        $content .= '</div>';
    }
}

// Add receiving guidelines
$content .= create_notes_section('Petunjuk Penerimaan Barang', 
    '• Semua barang telah diperiksa kondisi dan jumlahnya sesuai dengan dokumen' . "\n" .
    '• Barang yang rusak atau cacat telah dicatat dan akan diproses sesuai prosedur' . "\n" .
    '• Dokumen ini merupakan bukti sah penerimaan barang dari supplier' . "\n" .
    '• Segala klaim terkait barang harus dilaporkan maksimal 3x24 jam setelah penerimaan');

// Signatures
$signatures = [
    [
        'title' => 'Diterima Oleh',
        'name' => $penerimaan->penerima ?? '(............................)',
        'position' => 'Staff Gudang'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => $penerimaan->created_by ?? '(............................)',
        'position' => 'Quality Control'
    ],
    [
        'title' => 'Disetujui Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Gudang'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>