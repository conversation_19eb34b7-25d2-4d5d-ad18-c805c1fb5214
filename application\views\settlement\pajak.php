<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Settlement Pajak (PPN Masukan vs PPN Keluaran)</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            Settlement Pajak digunakan untuk memastikan bahwa pajak telah dihitung dan dilaporkan dengan benar.
                            Proses ini akan membandingkan PPN masukan dari pembelian dengan PPN keluaran dari penjualan untuk mengidentifikasi perbedaan atau kesalahan.
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card card-outline card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Filter Data</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="form-filter" class="form-horizontal">
                                    <div class="form-group row">
                                        <label for="tahun" class="col-sm-2 col-form-label">Tahun</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="tahun" name="tahun">
                                                <?php
                                                $tahun_sekarang = date('Y');
                                                for ($i = $tahun_sekarang; $i >= $tahun_sekarang - 5; $i--) {
                                                    $selected = ($i == $tahun) ? 'selected' : '';
                                                    echo "<option value=\"$i\" $selected>$i</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <label for="bulan" class="col-sm-2 col-form-label">Bulan</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="bulan" name="bulan">
                                                <?php
                                                $nama_bulan = [
                                                    1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
                                                    5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
                                                    9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
                                                ];
                                                
                                                foreach ($nama_bulan as $nomor => $nama) {
                                                    $selected = ($nomor == $bulan) ? 'selected' : '';
                                                    echo "<option value=\"$nomor\" $selected>$nama</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="offset-sm-2 col-sm-10">
                                            <button type="button" id="btn-filter" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                                            <button type="button" id="btn-export" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Ringkasan Pajak</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="info-box bg-info">
                                            <span class="info-box-icon"><i class="fas fa-arrow-down"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">PPN Masukan</span>
                                                <span class="info-box-number" id="ppn-masukan">Rp 0</span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    <span id="jumlah-transaksi-masukan">0</span> Transaksi
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-box bg-success">
                                            <span class="info-box-icon"><i class="fas fa-arrow-up"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">PPN Keluaran</span>
                                                <span class="info-box-number" id="ppn-keluaran">Rp 0</span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    <span id="jumlah-transaksi-keluaran">0</span> Transaksi
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-box bg-warning">
                                            <span class="info-box-icon"><i class="fas fa-balance-scale"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Selisih (Kurang/Lebih Bayar)</span>
                                                <span class="info-box-number" id="selisih-pajak">Rp 0</span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    PPN Keluaran - PPN Masukan
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-info">
                            <div class="card-header">
                                <h3 class="card-title">PPN Masukan (Pembelian)</h3>
                                <div class="card-tools">
                                    <a href="#" id="btn-detail-masukan" class="btn btn-tool">
                                        <i class="fas fa-eye"></i> Lihat Detail
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="chart-masukan" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">PPN Keluaran (Penjualan)</h3>
                                <div class="card-tools">
                                    <a href="#" id="btn-detail-keluaran" class="btn btn-tool">
                                        <i class="fas fa-eye"></i> Lihat Detail
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="chart-keluaran" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Analisis Pajak</h3>
                            </div>
                            <div class="card-body">
                                <div id="analisis-pajak">
                                    <p class="text-center">Silakan pilih tahun dan bulan untuk melihat analisis pajak.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var chartMasukan;
    var chartKeluaran;
    
    // Load data saat halaman dimuat
    loadData();
    
    $('#btn-filter').click(function() {
        loadData();
    });
    
    $('#btn-export').click(function() {
        var tahun = $('#tahun').val();
        var bulan = $('#bulan').val();
        
        window.location.href = '<?= site_url('settlement/export/pajak') ?>?tahun=' + tahun + '&bulan=' + bulan;
    });
    
    $('#btn-detail-masukan').click(function(e) {
        e.preventDefault();
        var tahun = $('#tahun').val();
        var bulan = $('#bulan').val();
        
        window.location.href = '<?= site_url('settlement/pajak_detail/masukan/') ?>' + tahun + '/' + bulan;
    });
    
    $('#btn-detail-keluaran').click(function(e) {
        e.preventDefault();
        var tahun = $('#tahun').val();
        var bulan = $('#bulan').val();
        
        window.location.href = '<?= site_url('settlement/pajak_detail/keluaran/') ?>' + tahun + '/' + bulan;
    });
    
    function loadData() {
        var tahun = $('#tahun').val();
        var bulan = $('#bulan').val();
        
        $.ajax({
            url: '<?= site_url('settlement/pajak_data') ?>',
            type: 'POST',
            data: {
                tahun: tahun,
                bulan: bulan
            },
            dataType: 'json',
            success: function(response) {
                updateRingkasan(response);
                updateChart(response);
                updateAnalisis(response);
            },
            error: function(xhr, status, error) {
                console.error(error);
                alert('Terjadi kesalahan saat memuat data. Silakan coba lagi.');
            }
        });
    }
    
    function updateRingkasan(data) {
        $('#ppn-masukan').text(formatRupiah(data.masukan.total_pajak));
        $('#jumlah-transaksi-masukan').text(data.masukan.jumlah_transaksi);
        
        $('#ppn-keluaran').text(formatRupiah(data.keluaran.total_pajak));
        $('#jumlah-transaksi-keluaran').text(data.keluaran.jumlah_transaksi);
        
        $('#selisih-pajak').text(formatRupiah(data.selisih));
        
        // Ubah warna box selisih berdasarkan nilai
        var selisihBox = $('#selisih-pajak').closest('.info-box');
        if (data.selisih > 0) {
            selisihBox.removeClass('bg-warning bg-danger').addClass('bg-success');
        } else if (data.selisih < 0) {
            selisihBox.removeClass('bg-warning bg-success').addClass('bg-danger');
        } else {
            selisihBox.removeClass('bg-success bg-danger').addClass('bg-warning');
        }
    }
    
    function updateChart(data) {
        // Destroy chart lama jika ada
        if (chartMasukan) chartMasukan.destroy();
        if (chartKeluaran) chartKeluaran.destroy();
        
        // Chart PPN Masukan
        var ctxMasukan = document.getElementById('chart-masukan').getContext('2d');
        chartMasukan = new Chart(ctxMasukan, {
            type: 'pie',
            data: {
                labels: ['PPN Masukan', 'Nilai Pembelian (DPP)'],
                datasets: [{
                    data: [
                        data.masukan.total_pajak,
                        data.masukan.total_pajak * 10 // Asumsi PPN 10%
                    ],
                    backgroundColor: ['#17a2b8', '#3c8dbc']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            var dataset = data.datasets[tooltipItem.datasetIndex];
                            var value = dataset.data[tooltipItem.index];
                            return data.labels[tooltipItem.index] + ': ' + formatRupiah(value);
                        }
                    }
                }
            }
        });
        
        // Chart PPN Keluaran
        var ctxKeluaran = document.getElementById('chart-keluaran').getContext('2d');
        chartKeluaran = new Chart(ctxKeluaran, {
            type: 'pie',
            data: {
                labels: ['PPN Keluaran', 'Nilai Penjualan (DPP)'],
                datasets: [{
                    data: [
                        data.keluaran.total_pajak,
                        data.keluaran.total_pajak * 10 // Asumsi PPN 10%
                    ],
                    backgroundColor: ['#28a745', '#6fd080']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            var dataset = data.datasets[tooltipItem.datasetIndex];
                            var value = dataset.data[tooltipItem.index];
                            return data.labels[tooltipItem.index] + ': ' + formatRupiah(value);
                        }
                    }
                }
            }
        });
    }
    
    function updateAnalisis(data) {
        var html = '';
        var namaBulan = [
            'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        ];
        
        html += '<h5>Analisis Pajak Periode ' + namaBulan[data.bulan - 1] + ' ' + data.tahun + '</h5>';
        
        if (data.masukan.jumlah_transaksi == 0 && data.keluaran.jumlah_transaksi == 0) {
            html += '<div class="alert alert-warning">';
            html += '<h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>';
            html += '<p>Tidak ada transaksi pajak pada periode ini.</p>';
            html += '</div>';
        } else {
            if (data.selisih > 0) {
                html += '<div class="alert alert-success">';
                html += '<h5><i class="icon fas fa-check"></i> Informasi</h5>';
                html += '<p>Pada periode ini, PPN Keluaran lebih besar dari PPN Masukan dengan selisih sebesar ' + formatRupiah(data.selisih) + '. Anda perlu menyetorkan kelebihan PPN tersebut ke kas negara.</p>';
                html += '</div>';
            } else if (data.selisih < 0) {
                html += '<div class="alert alert-info">';
                html += '<h5><i class="icon fas fa-info"></i> Informasi</h5>';
                html += '<p>Pada periode ini, PPN Masukan lebih besar dari PPN Keluaran dengan selisih sebesar ' + formatRupiah(Math.abs(data.selisih)) + '. Kelebihan PPN Masukan ini dapat dikompensasikan ke masa pajak berikutnya.</p>';
                html += '</div>';
            } else {
                html += '<div class="alert alert-warning">';
                html += '<h5><i class="icon fas fa-exclamation-triangle"></i> Informasi</h5>';
                html += '<p>Pada periode ini, PPN Keluaran sama dengan PPN Masukan. Tidak ada PPN yang perlu disetor atau dikompensasikan.</p>';
                html += '</div>';
            }
            
            html += '<div class="row">';
            html += '<div class="col-md-6">';
            html += '<table class="table table-bordered">';
            html += '<tr><th colspan="2" class="text-center">Ringkasan PPN Masukan</th></tr>';
            html += '<tr><th>Jumlah Transaksi</th><td>' + data.masukan.jumlah_transaksi + '</td></tr>';
            html += '<tr><th>Total PPN Masukan</th><td>' + formatRupiah(data.masukan.total_pajak) + '</td></tr>';
            html += '<tr><th>Rata-rata PPN per Transaksi</th><td>' + formatRupiah(data.masukan.jumlah_transaksi > 0 ? data.masukan.total_pajak / data.masukan.jumlah_transaksi : 0) + '</td></tr>';
            html += '</table>';
            html += '</div>';
            
            html += '<div class="col-md-6">';
            html += '<table class="table table-bordered">';
            html += '<tr><th colspan="2" class="text-center">Ringkasan PPN Keluaran</th></tr>';
            html += '<tr><th>Jumlah Transaksi</th><td>' + data.keluaran.jumlah_transaksi + '</td></tr>';
            html += '<tr><th>Total PPN Keluaran</th><td>' + formatRupiah(data.keluaran.total_pajak) + '</td></tr>';
            html += '<tr><th>Rata-rata PPN per Transaksi</th><td>' + formatRupiah(data.keluaran.jumlah_transaksi > 0 ? data.keluaran.total_pajak / data.keluaran.jumlah_transaksi : 0) + '</td></tr>';
            html += '</table>';
            html += '</div>';
            html += '</div>';
            
            html += '<div class="alert alert-secondary mt-3">';
            html += '<h5><i class="icon fas fa-info"></i> Rekomendasi</h5>';
            html += '<ol>';
            
            if (data.masukan.jumlah_transaksi == 0) {
                html += '<li>Tidak ada transaksi pembelian dengan PPN pada periode ini. Pastikan semua faktur pajak masukan telah dicatat dengan benar.</li>';
            }
            
            if (data.keluaran.jumlah_transaksi == 0) {
                html += '<li>Tidak ada transaksi penjualan dengan PPN pada periode ini. Pastikan semua faktur pajak keluaran telah dicatat dengan benar.</li>';
            }
            
            if (data.selisih > 0) {
                html += '<li>Siapkan SPT Masa PPN untuk melaporkan dan menyetorkan kelebihan PPN sebesar ' + formatRupiah(data.selisih) + '.</li>';
            } else if (data.selisih < 0) {
                html += '<li>Siapkan SPT Masa PPN untuk melaporkan kompensasi kelebihan PPN Masukan sebesar ' + formatRupiah(Math.abs(data.selisih)) + ' ke masa pajak berikutnya.</li>';
            }
            
            html += '<li>Pastikan semua faktur pajak masukan dan keluaran telah dicatat dengan benar dan lengkap.</li>';
            html += '<li>Periksa kembali perhitungan PPN pada setiap transaksi untuk memastikan keakuratan data.</li>';
            html += '</ol>';
            html += '</div>';
        }
        
        $('#analisis-pajak').html(html);
    }
    
    function formatRupiah(angka) {
        var number_string = angka.toString(),
            split = number_string.split('.'),
            sisa = split[0].length % 3,
            rupiah = split[0].substr(0, sisa),
            ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }
});
</script>