<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Settlement
 * Mengelola data rekonsiliasi dan verifikasi transaksi
 */
class Mod_settlement extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Settlement Penjualan (Faktur vs Pengiriman)
     */
    public function get_penjualan_settlement($filter = [])
    {
        $this->db->select('
            p.id,
            p.nomor_pengiriman,
            p.tanggal_pengiri<PERSON>,
            p.status,
            p.tanggal_diterima,
            p.al<PERSON><PERSON>_pengiri<PERSON>,
            p.penerima,
            pl.nama as nama_pelanggan,
            pl.kode as kode_pelanggan,
            ps.nomor_pesanan,
            ps.tanggal_pesanan,
            COUNT(pd.id) as total_item,
            SUM(pd.qty_dikirim) as total_qty,
            IF(f.id IS NULL, "belum_difakturkan", "sudah_difakturkan") as status_faktur,
            f.id as id_faktur,
            f.nomor_faktur,
            f.tanggal_faktur,
            f.status_pembayaran,
            f.total_item as faktur_total_item,
            f.total_qty as faktur_total_qty,
            f.total_faktur,
            (COUNT(pd.id) - IFNULL(f.total_item, 0)) as selisih_item,
            (SUM(pd.qty_dikirim) - IFNULL(f.total_qty, 0)) as selisih_qty,
            0 as selisih_nilai
        ');
        $this->db->from('pengiriman p');
        $this->db->join('pengiriman_detail pd', 'pd.id_pengiriman = p.id', 'left');
        $this->db->join('pelanggan pl', 'pl.id = p.id_pelanggan', 'left');
        $this->db->join('pesanan ps', 'ps.id = p.id_pesanan', 'left');
        $this->db->join('faktur_penjualan f', 'f.id_pengiriman = p.id', 'left');
        
        // Filter berdasarkan status pengiriman
        $this->db->where('p.status', 'delivered');
        
        // Filter berdasarkan tanggal
        if (!empty($filter['tanggal_awal'])) {
            $this->db->where('p.tanggal_pengiriman >=', $filter['tanggal_awal']);
        }
        
        if (!empty($filter['tanggal_akhir'])) {
            $this->db->where('p.tanggal_pengiriman <=', $filter['tanggal_akhir']);
        }
        
        // Filter berdasarkan status faktur
        if (!empty($filter['status'])) {
            if ($filter['status'] == 'belum_difakturkan') {
                $this->db->where('f.id IS NULL');
            } else if ($filter['status'] == 'sudah_difakturkan') {
                $this->db->where('f.id IS NOT NULL');
            }
        }
        
        $this->db->group_by('p.id');
        $this->db->order_by('p.tanggal_pengiriman', 'desc');
        
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pengiriman_by_id($id)
    {
        $this->db->select('
            p.*,
            pl.nama as nama_pelanggan,
            pl.kode as kode_pelanggan,
            pl.alamat as alamat_pelanggan,
            pl.no_telepon as telepon_pelanggan,
            ps.nomor_pesanan,
            ps.tanggal_pesanan
        ');
        $this->db->from('pengiriman p');
        $this->db->join('pelanggan pl', 'pl.id = p.id_pelanggan', 'left');
        $this->db->join('pesanan ps', 'ps.id = p.id_pesanan', 'left');
        $this->db->where('p.id', $id);
        
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pengiriman_detail($id_pengiriman)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            b.harga_jual,
            s.nama_satuan,
            g.nama_gudang
        ');
        $this->db->from('pengiriman_detail pd');
        $this->db->join('barang b', 'b.id = pd.id_barang', 'left');
        $this->db->join('satuan s', 's.id = b.satuan_id', 'left');
        $this->db->join('gudang g', 'g.id = pd.id_gudang', 'left');
        $this->db->where('pd.id_pengiriman', $id_pengiriman);
        
        $query = $this->db->get();
        return $query->result();
    }



    /**
     * Settlement Pembelian (Pembelian vs Penerimaan)
     */
    public function get_pembelian_settlement($filter = [])
    {
        $this->db->select('
            p.id,
            p.nomor_pembelian,
            p.tanggal_pembelian,
            p.jenis_pembelian,
            p.status,
            s.nama as nama_supplier,
            s.kode as kode_supplier,
            COUNT(pd.id) as total_item,
            SUM(pd.qty) as total_qty,
            p.total_akhir,
            COALESCE(SUM(pp.qty_diterima), 0) as total_diterima,
            (SUM(pd.qty) - COALESCE(SUM(pp.qty_diterima), 0)) as selisih_qty,
            IF(SUM(pd.qty) = COALESCE(SUM(pp.qty_diterima), 0), "sesuai", IF(COALESCE(SUM(pp.qty_diterima), 0) = 0, "belum_diterima", IF(SUM(pd.qty) > COALESCE(SUM(pp.qty_diterima), 0), "diterima_sebagian", "diterima_lebih"))) as status_penerimaan
        ');
        $this->db->from('pembelian p');
        $this->db->join('pembelian_detail pd', 'pd.id_pembelian = p.id', 'left');
        $this->db->join('supplier s', 's.id = p.id_supplier', 'left');
        $this->db->join('penerimaan_pembelian_detail pp', 'pp.id_pembelian_detail = pd.id', 'left');
        
        // Filter berdasarkan tanggal
        if (!empty($filter['tanggal_awal'])) {
            $this->db->where('p.tanggal_pembelian >=', $filter['tanggal_awal']);
        }
        
        if (!empty($filter['tanggal_akhir'])) {
            $this->db->where('p.tanggal_pembelian <=', $filter['tanggal_akhir']);
        }
        
        // Filter berdasarkan status
        if (!empty($filter['status'])) {
            if ($filter['status'] == 'belum_diterima') {
                $this->db->having('status_penerimaan', 'belum_diterima');
            } else if ($filter['status'] == 'diterima_sebagian') {
                $this->db->having('status_penerimaan', 'diterima_sebagian');
            } else if ($filter['status'] == 'sesuai') {
                $this->db->having('status_penerimaan', 'sesuai');
            } else if ($filter['status'] == 'diterima_lebih') {
                $this->db->having('status_penerimaan', 'diterima_lebih');
            }
        }
        
        $this->db->group_by('p.id');
        $this->db->order_by('p.tanggal_pembelian', 'desc');
        
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pembelian_by_id($id)
    {
        $this->db->select('
            p.*,
            s.nama as nama_supplier,
            s.kode as kode_supplier,
            s.alamat as alamat_supplier,
            s.no_telepon as telepon_supplier,
            s.email as email_supplier
        ');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 's.id = p.id_supplier', 'left');
        $this->db->where('p.id', $id);
        
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pembelian_detail($id_pembelian)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            pd.harga
        ');
        $this->db->from('pembelian_detail pd');
        $this->db->join('barang b', 'b.id = pd.id_barang', 'left');
        $this->db->join('satuan s', 's.id = b.satuan_id', 'left');
        $this->db->where('pd.id_pembelian', $id_pembelian);
        
        $query = $this->db->get();
        return $query->result();
    }

    public function get_penerimaan_by_pembelian($id_pembelian)
    {
        $this->db->select('
            pp.*,
            u.full_name as nama_penerima
        ');
        $this->db->from('penerimaan_pembelian pp');
        $this->db->join('tbl_user u', 'u.id_user = pp.created_by', 'left');
        $this->db->where('pp.id_pembelian', $id_pembelian);
        
        $query = $this->db->get();
        return $query->result();
    }

    public function get_penerimaan_detail($id_penerimaan)
    {
        $this->db->select('
            ppd.*,
            pd.id_barang,
            pd.harga_satuan as harga,
            pd.diskon,
            pd.qty as qty_pembelian,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            g.nama_gudang
        ');
        $this->db->from('penerimaan_pembelian_detail ppd');
        $this->db->join('pembelian_detail pd', 'pd.id = ppd.id_pembelian_detail', 'left');
        $this->db->join('barang b', 'b.id = pd.id_barang', 'left');
        $this->db->join('satuan s', 's.id = b.satuan_id', 'left');
        $this->db->join('gudang g', 'g.id = ppd.id_gudang', 'left');
        $this->db->where('ppd.id_penerimaan', $id_penerimaan);
        
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Settlement Retur (Retur Penjualan dan Pembelian)
     */
    public function get_retur_settlement($filter = [])
    {
        $result = [];
        
        // Jika filter jenis adalah penjualan atau semua
        if (empty($filter['jenis']) || $filter['jenis'] == 'penjualan') {
            $this->db->select('
                "penjualan" as jenis,
                rp.id,
                rp.nomor_retur,
                rp.tanggal_retur,
                p.nama as nama_partner,
                p.kode as kode_partner,
                f.nomor_faktur,
                f.tanggal_faktur,
                COUNT(rpd.id) as total_item,
                SUM(rpd.qty_retur) as total_qty,
                COALESCE(rp.total_nilai, 0) as total_nilai_retur,
                rp.status,
                IF(rp.status = "draft", "Draft", IF(rp.status = "diproses", "Diproses", IF(rp.status = "selesai", "Selesai", "Dibatalkan"))) as status_penggantian_text
            ');
            $this->db->from('retur_penjualan rp');
            $this->db->join('retur_penjualan_detail rpd', 'rpd.id_retur = rp.id', 'left');
            $this->db->join('pelanggan p', 'p.id = rp.id_pelanggan', 'left');
            $this->db->join('faktur_penjualan f', 'f.id = rp.id_faktur', 'left');
            
            // Filter berdasarkan tanggal
            if (!empty($filter['tanggal_awal'])) {
                $this->db->where('rp.tanggal_retur >=', $filter['tanggal_awal']);
            }
            
            if (!empty($filter['tanggal_akhir'])) {
                $this->db->where('rp.tanggal_retur <=', $filter['tanggal_akhir']);
            }
            
            // Filter berdasarkan status
            if (!empty($filter['status'])) {
                $this->db->where('rp.status', $filter['status']);
            }
            
            $this->db->group_by('rp.id');
            $this->db->order_by('rp.tanggal_retur', 'desc');
            
            $query = $this->db->get();
            $result = $query->result();
        }
        
        // Jika filter jenis adalah pembelian atau semua
        if (empty($filter['jenis']) || $filter['jenis'] == 'pembelian') {
            $this->db->select('
                "pembelian" as jenis,
                rp.id,
                rp.nomor_retur,
                rp.tanggal_retur,
                s.nama as nama_partner,
                s.kode as kode_partner,
                p.nomor_pembelian,
                p.tanggal_pembelian,
                COUNT(rpd.id) as total_item,
                SUM(rpd.qty_retur) as total_qty,
                COALESCE(rp.total_nilai, 0) as total_nilai_retur,
                rp.compensation_status
            ', FALSE);
            $this->db->select('(
                CASE 
                    WHEN rp.compensation_status = "menunggu" THEN "Menunggu"
                    WHEN rp.compensation_status = "dalam_proses" THEN "Dalam Proses"
                    WHEN rp.compensation_status = "selesai" THEN "Selesai"
                    WHEN rp.compensation_status = "gagal" THEN "Gagal"
                    ELSE "Ditolak"
                END
            ) as status_penggantian_text', FALSE);
            $this->db->from('retur_pembelian rp');
            $this->db->join('retur_pembelian_detail rpd', 'rpd.id_retur = rp.id', 'left');
            $this->db->join('supplier s', 's.id = rp.id_supplier', 'left');
            $this->db->join('pembelian p', 'p.id = rp.id_pembelian', 'left');
            
            // Filter berdasarkan tanggal
            if (!empty($filter['tanggal_awal'])) {
                $this->db->where('rp.tanggal_retur >=', $filter['tanggal_awal']);
            }
            
            if (!empty($filter['tanggal_akhir'])) {
                $this->db->where('rp.tanggal_retur <=', $filter['tanggal_akhir']);
            }
            
            // Filter berdasarkan status
            if (!empty($filter['status'])) {
                $this->db->where('rp.compensation_status', $filter['status']);
            }
            
            $this->db->group_by('rp.id');
            $this->db->order_by('rp.tanggal_retur', 'desc');
            
            $query = $this->db->get();
            
            // Jika sudah ada hasil dari penjualan, gabungkan
            if (!empty($result)) {
                $result = array_merge($result, $query->result());
            } else {
                $result = $query->result();
            }
        }
        
        return $result;
    }

    public function get_retur_penjualan_by_id($id)
    {
        $this->db->select('
            rp.*,
            p.nama as nama_pelanggan,
            p.kode as kode_pelanggan,
            p.alamat as alamat_pelanggan,
            p.no_telepon as telepon_pelanggan,
            f.nomor_faktur,
            f.tanggal_faktur
        ');
        $this->db->from('retur_penjualan rp');
        $this->db->join('pelanggan p', 'p.id = rp.id_pelanggan', 'left');
        $this->db->join('faktur_penjualan f', 'f.id = rp.id_faktur', 'left');
        $this->db->where('rp.id', $id);
        
        $query = $this->db->get();
        return $query->row();
    }

    public function get_retur_penjualan_detail($id_retur)
    {
        $this->db->select('
            rpd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            g.nama_gudang
        ');
        $this->db->from('retur_penjualan_detail rpd');
        $this->db->join('barang b', 'b.id = rpd.id_barang', 'left');
        $this->db->join('satuan s', 's.id = b.satuan_id', 'left');
        $this->db->join('gudang g', 'g.id = rpd.id_gudang', 'left');
        $this->db->where('rpd.id_retur', $id_retur);
        
        $query = $this->db->get();
        return $query->result();
    }

    public function get_retur_pembelian_by_id($id)
    {
        $this->db->select('
            rp.*,
            s.nama as nama_supplier,
            s.kode as kode_supplier,
            s.alamat as alamat_supplier,
            s.no_telepon as telepon_supplier,
            p.nomor_pembelian,
            p.tanggal_pembelian
        ');
        $this->db->from('retur_pembelian rp');
        $this->db->join('supplier s', 's.id = rp.id_supplier', 'left');
        $this->db->join('pembelian p', 'p.id = rp.id_pembelian', 'left');
        $this->db->where('rp.id', $id);
        
        $query = $this->db->get();
        return $query->row();
    }

    public function get_retur_pembelian_detail($id_retur)
    {
        $this->db->select('
            rpd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            g.nama_gudang
        ');
        $this->db->from('retur_pembelian_detail rpd');
        $this->db->join('barang b', 'b.id = rpd.id_barang', 'left');
        $this->db->join('satuan s', 's.id = b.satuan_id', 'left');
        $this->db->join('gudang g', 'g.id = rpd.id_gudang', 'left');
        $this->db->where('rpd.id_retur', $id_retur);
        
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Mendapatkan data pembayaran untuk settlement
     * Membandingkan data pembayaran dengan faktur penjualan
     */
    public function get_pembayaran_settlement($filter = [])
    {
        $this->db->select('
            f.id,
            "penjualan" as jenis,
            f.nomor_faktur as nomor_transaksi,
            f.tanggal_faktur as tanggal_transaksi,
            p.nama as nama_partner,
            COALESCE(f.total_faktur, 0) as total_transaksi,
            COALESCE(SUM(pb.jumlah_pembayaran), 0) as total_dibayar,
            COALESCE((f.total_faktur - COALESCE(SUM(pb.jumlah_pembayaran), 0)), 0) as sisa_pembayaran,
            CASE 
                WHEN f.status_pembayaran = "lunas" THEN "lunas"
                WHEN COALESCE(SUM(pb.jumlah_pembayaran), 0) > 0 THEN "sebagian"
                ELSE "belum_bayar"
            END as status_pembayaran,
            DATEDIFF(CURDATE(), f.tanggal_faktur) as umur_piutang
        ');
        $this->db->from('faktur_penjualan f');
        $this->db->join('pelanggan p', 'p.id = f.id_pelanggan', 'left');
        $this->db->join('faktur_penjualan_pembayaran pb', 'pb.id_faktur_penjualan = f.id', 'left');
        
        // Filter berdasarkan tanggal
        if (!empty($filter['tanggal_awal']) && !empty($filter['tanggal_akhir'])) {
            $this->db->where('f.tanggal_faktur >=', $filter['tanggal_awal']);
            $this->db->where('f.tanggal_faktur <=', $filter['tanggal_akhir']);
        }
        
        // Filter berdasarkan jenis pembayaran
        if (!empty($filter['jenis'])) {
            if ($filter['jenis'] == 'tunai') {
                $this->db->where('pb.metode_pembayaran', 'tunai');
            } elseif ($filter['jenis'] == 'transfer') {
                $this->db->where('pb.metode_pembayaran', 'transfer');
            }
        }
        
        // Filter berdasarkan status
        if (!empty($filter['status'])) {
            if ($filter['status'] == 'lunas') {
                $this->db->where('f.status_pembayaran', 'lunas');
            } elseif ($filter['status'] == 'belum_lunas') {
                $this->db->where('f.status_pembayaran', 'belum_lunas');
            }
        }
        
        $this->db->group_by('f.id');
        $this->db->order_by('f.tanggal_faktur', 'desc');
        
        $query = $this->db->get();
        
        // Penanganan error jika query gagal
        if ($query === FALSE) {
            log_message('error', 'Query error in get_pembayaran_settlement: ' . $this->db->error()['message']);
            return [];
        }
        
        return $query->result();
    }

    /**
     * Mendapatkan daftar gudang yang aktif
     */
    public function get_gudang_aktif()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'asc');
        
        $query = $this->db->get();
        
        // Penanganan error jika query gagal
        if ($query === FALSE) {
            log_message('error', 'Query error in get_gudang_aktif: ' . $this->db->error()['message']);
            return [];
        }
        
        return $query->result();
    }

    /**
     * Mendapatkan data stok untuk settlement
     * Membandingkan stok fisik dengan stok sistem
     */
    public function get_stok_settlement($filter = [])
    {
        $this->db->select('
            b.id,
            b.id as id_barang,
            sb.id_gudang,
            b.kode_barang,
            b.nama_barang,
            s.nama_satuan as satuan,
            s.nama_satuan,
            g.nama_gudang,
            COALESCE(sb.qty_terakhir, 0) as stok_sistem,
            COALESCE(so.stok_fisik, 0) as stok_fisik,
            COALESCE((sb.qty_terakhir - COALESCE(so.stok_fisik, 0)), 0) as selisih,
            so.tanggal_opname as tanggal_opname_terakhir,
            CASE 
                WHEN sb.qty_terakhir = COALESCE(so.stok_fisik, 0) THEN "sesuai"
                WHEN sb.qty_terakhir > COALESCE(so.stok_fisik, 0) THEN "lebih_sistem"
                ELSE "kurang_sistem"
            END as status_stok
        ');
        $this->db->from('barang b');
        $this->db->join('satuan s', 's.id = b.satuan_id', 'left');
        $this->db->join('stok_barang sb', 'sb.id_barang = b.id', 'left');
        $this->db->join('gudang g', 'g.id = sb.id_gudang', 'left');
        $this->db->join('(
            SELECT sod.id_barang, so.id_gudang, sod.qty_fisik as stok_fisik, so.tanggal_opname 
            FROM stok_opname_detail sod
            JOIN stok_opname so ON so.id = sod.id_opname
            WHERE so.tanggal_opname = (
                SELECT MAX(so2.tanggal_opname) 
                FROM stok_opname so2 
                JOIN stok_opname_detail sod2 ON sod2.id_opname = so2.id
                WHERE sod2.id_barang = sod.id_barang AND so2.id_gudang = so.id_gudang
            )
        ) so', 'so.id_barang = b.id AND so.id_gudang = sb.id_gudang', 'left');
        
        // Filter berdasarkan gudang
        if (!empty($filter['id_gudang'])) {
            $this->db->where('sb.id_gudang', $filter['id_gudang']);
        }
        
        // Filter berdasarkan barang
        if (!empty($filter['filter_barang'])) {
            $this->db->group_start();
            $this->db->like('b.kode_barang', $filter['filter_barang']);
            $this->db->or_like('b.nama_barang', $filter['filter_barang']);
            $this->db->group_end();
        }
        
        // Filter berdasarkan status
        if (!empty($filter['status'])) {
            if ($filter['status'] == 'sesuai') {
                $this->db->having('stok_sistem = stok_fisik');
            } elseif ($filter['status'] == 'tidak_sesuai') {
                $this->db->having('stok_sistem != stok_fisik');
            }
        }
        
        $this->db->order_by('b.nama_barang', 'asc');
        
        $query = $this->db->get();
        
        // Penanganan error jika query gagal
        if ($query === FALSE) {
            log_message('error', 'Query error in get_stok_settlement: ' . $this->db->error()['message']);
            return [];
        }
        
        return $query->result();
    }

    /**
     * Mendapatkan daftar bank untuk settlement kas bank
     */
    public function get_bank_list()
    {
        $this->db->select('id_bank, kode_bank, nama_bank, nomor_rekening');
        $this->db->from('bank');
        $this->db->where('status', 'AKTIF');
        $this->db->order_by('nama_bank', 'asc');
        
        $query = $this->db->get();
        
        // Penanganan error jika query gagal
        if ($query === FALSE) {
            log_message('error', 'Query error in get_bank_list: ' . $this->db->error()['message']);
            return [];
        }
        
        return $query->result();
    }





    /**
     * Get faktur by pengiriman
     */
    public function get_faktur_by_pengiriman($id_pengiriman)
    {
        try {
            $this->db->select('f.*');
            $this->db->from('faktur_penjualan f');
            $this->db->where('f.id_pengiriman', $id_pengiriman);
            $query = $this->db->get();
            return $query->row();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_faktur_by_pengiriman error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get faktur detail
     */
    public function get_faktur_detail($id_faktur)
    {
        try {
            $this->db->select('fd.*, b.nama as nama_barang, b.kode as kode_barang');
            $this->db->from('faktur_penjualan_detail fd');
            $this->db->join('barang b', 'b.id = fd.id_barang', 'left');
            $this->db->where('fd.id_faktur_penjualan', $id_faktur);
            $query = $this->db->get();
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_faktur_detail error: ' . $e->getMessage());
            return [];
        }
    }



    /**
     * Get kas bank settlement
     */
    public function get_kas_bank_settlement($filter = [])
    {
        try {
            $this->db->select('tk.*, b.nama_bank as nama_bank');
            $this->db->from('transaksi_kas tk');
            $this->db->join('bank b', 'b.id_bank = tk.id_bank', 'left');
            
            if (!empty($filter['tanggal_awal'])) {
                $this->db->where('tk.tanggal_transaksi >=', $filter['tanggal_awal']);
            }
            if (!empty($filter['tanggal_akhir'])) {
                $this->db->where('tk.tanggal_transaksi <=', $filter['tanggal_akhir']);
            }
            
            $this->db->order_by('tk.tanggal_transaksi', 'DESC');
            $query = $this->db->get();
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_kas_bank_settlement error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get pajak settlement
     */
    public function get_pajak_settlement($filter = [])
    {
        try {
            $tahun = !empty($filter['tahun']) ? $filter['tahun'] : date('Y');
            $bulan = !empty($filter['bulan']) ? $filter['bulan'] : date('m');
            
            // PPN Masukan dari pembelian
            $this->db->select('SUM(p.ppn_nominal) as ppn_masukan');
            $this->db->from('pembelian p');
            $this->db->where('YEAR(p.tanggal_pembelian)', $tahun);
            $this->db->where('MONTH(p.tanggal_pembelian)', $bulan);
            $query_masukan = $this->db->get();
            $ppn_masukan = ($query_masukan && $query_masukan->row()) ? $query_masukan->row()->ppn_masukan ?? 0 : 0;
            
            // PPN Keluaran dari penjualan
            $this->db->select('SUM(f.pajak) as ppn_keluaran');
            $this->db->from('faktur_penjualan f');
            $this->db->where('YEAR(f.tanggal_faktur)', $tahun);
            $this->db->where('MONTH(f.tanggal_faktur)', $bulan);
            $query_keluaran = $this->db->get();
            $ppn_keluaran = ($query_keluaran && $query_keluaran->row()) ? $query_keluaran->row()->ppn_keluaran ?? 0 : 0;
            
            return [
                (object)[
                    'jenis' => 'PPN Masukan',
                    'nilai' => $ppn_masukan,
                    'tahun' => $tahun,
                    'bulan' => $bulan
                ],
                (object)[
                    'jenis' => 'PPN Keluaran', 
                    'nilai' => $ppn_keluaran,
                    'tahun' => $tahun,
                    'bulan' => $bulan
                ]
            ];
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_pajak_settlement error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get pajak masukan detail
     */
    public function get_pajak_masukan_detail($tahun, $bulan)
    {
        try {
            $this->db->select('p.*, s.nama as nama_supplier');
            $this->db->from('pembelian p');
            $this->db->join('supplier s', 's.id = p.id_supplier', 'left');
            $this->db->where('YEAR(p.tanggal_pembelian)', $tahun);
            $this->db->where('MONTH(p.tanggal_pembelian)', $bulan);
            $this->db->where('p.ppn >', 0);
            $this->db->order_by('p.tanggal_pembelian', 'DESC');
            $query = $this->db->get();
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_pajak_masukan_detail error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get pajak keluaran detail
     */
    public function get_pajak_keluaran_detail($tahun, $bulan)
    {
        try {
            $this->db->select('f.*, pl.nama as nama_pelanggan');
            $this->db->from('faktur_penjualan f');
            $this->db->join('pelanggan pl', 'pl.id = f.id_pelanggan', 'left');
            $this->db->where('YEAR(f.tanggal_faktur)', $tahun);
            $this->db->where('MONTH(f.tanggal_faktur)', $bulan);
            $this->db->where('f.ppn >', 0);
            $this->db->order_by('f.tanggal_faktur', 'DESC');
            $query = $this->db->get();
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_pajak_keluaran_detail error: ' . $e->getMessage());
            return [];
        }
    }



    /**
     * Get user by ID
     */
    public function get_user_by_id($id)
    {
        try {
            $this->db->where('id', $id);
            $query = $this->db->get('users');
            return $query->row();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_user_by_id error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Mendapatkan data barang berdasarkan ID
     */
    public function get_barang_by_id($id)
    {
        try {
            $this->db->select('b.*, s.nama_satuan');
            $this->db->from('barang b');
            $this->db->join('satuan s', 's.id = b.satuan_id', 'left');
            $this->db->where('b.id', $id);
            $query = $this->db->get();
            return $query->row();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_barang_by_id error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Mendapatkan data gudang berdasarkan ID
     */
    public function get_gudang_by_id($id)
    {
        try {
            $this->db->where('id', $id);
            $query = $this->db->get('gudang');
            return $query->row();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_gudang_by_id error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Mendapatkan stok sistem untuk barang dan gudang tertentu
     */
    public function get_stok_sistem($id_barang, $id_gudang)
    {
        try {
            $this->db->select('qty_terakhir as stok_tersedia');
            $this->db->from('stok_barang');
            $this->db->where('id_barang', $id_barang);
            $this->db->where('id_gudang', $id_gudang);
            $query = $this->db->get();
            $result = $query->row();
            return $result ? $result->stok_tersedia : 0;
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_stok_sistem error: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Mendapatkan data stok opname terakhir untuk barang dan gudang tertentu
     */
    public function get_stok_opname_terakhir($id_barang, $id_gudang)
    {
        try {
            $this->db->select('sod.qty_fisik, so.tanggal_opname, so.nomor_opname');
            $this->db->from('stok_opname_detail sod');
            $this->db->join('stok_opname so', 'so.id = sod.id_opname');
            $this->db->where('sod.id_barang', $id_barang);
            $this->db->where('so.id_gudang', $id_gudang);
            $this->db->where('so.status', 'final');
            $this->db->order_by('so.tanggal_opname', 'DESC');
            $this->db->limit(1);
            $query = $this->db->get();
            return $query->row();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_stok_opname_terakhir error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Mendapatkan mutasi stok untuk barang dan gudang tertentu
     */
    public function get_mutasi_stok($id_barang, $id_gudang, $limit = 20)
    {
        try {
            $this->db->select('*');
            $this->db->from('stok_movement');
            $this->db->where('id_barang', $id_barang);
            $this->db->where('id_gudang', $id_gudang);
            $this->db->order_by('tanggal', 'DESC');
            $this->db->order_by('id', 'DESC');
            $this->db->limit($limit);
            $query = $this->db->get();
            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Mod_settlement get_mutasi_stok error: ' . $e->getMessage());
            return [];
        }
    }

}





