<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Buat Tabel Baru</h3>

                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">

                        <form id="forminput" method="post" action="<?= base_url('templatecontroller/create_table') ?>">
                            <input type="hidden" id="csrf_token" name="<?= $this->security->get_csrf_token_name(); ?>"
                                value="<?= $this->security->get_csrf_hash(); ?>">
                            <div class="row">
                                <div class="form-group col-4 ">
                                    <label>Nama Tabel:</label><br>
                                    <input type="text" class="form-control" name="table_name" placeholder="Contoh: users, produk, transaksi" required>
                                </div>
                                <div class="form-group col-4 ">
                                    <label>Generate CRUD:</label><br>
                                    <input type="checkbox" name="generate" value="1" checked> <span>Jika Generate CRUD Sekalain Silahkan Checklist</span>
                                </div>
                            </div>

                            <div id="fields-wrapper">
                                <div class="field-row ">
                                    <div class="row">
                                        <div class="form-group  col-3 ">
                                            <input type="text" class="form-control" name="fields[0][name]" placeholder="Nama Field" required>
                                        </div>
                                        <div class="form-group  col-3 ">
                                            <select name="fields[0][type]" class="form-control select2" required>
                                                <option value="">-- Tipe Data --</option>
                                                <option value="INT(11)">INT(11)</option>
                                                <option value="VARCHAR(255)">VARCHAR(255)</option>
                                                <option value="TEXT">TEXT</option>
                                                <option value="DATE">DATE</option>
                                                <option value="TIMESTAMP">TIMESTAMP</option>
                                                <option value="FLOAT">FLOAT</option>
                                                <option value="DOUBLE">DOUBLE</option>
                                            </select>
                                        </div>

                                        <div class="form-group col-3">
                                            <label>Extra:</label><br>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="fields[0][extra][]" value="PRIMARY KEY" id="pk0">
                                                <label class="form-check-label" for="pk0">PRIMARY KEY</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="fields[0][extra][]" value="AUTO_INCREMENT" id="ai0">
                                                <label class="form-check-label" for="ai0">AUTO_INCREMENT</label>
                                            </div>
                                        </div>


                                        <div class="form-group  col-3 ">
                                            <select name="fields[0][tipe]" class="form-control select2" required>
                                                <option value="">-- Tipe Input --</option>
                                                <option value="text">Text</option>
                                                <option value="textarea">Text Area</option>
                                                <option value="select">Select</option>
                                                <option value="date">Date</option>
                                                <option value="file">File</option>
                                                <option value="checkbox">Checkbox</option>
                                                <option value="radio">Radio</option>
                                                <option value="select2">Select2</option>
                                                <option value="datetime">Datetime</option>
                                                <option value="time">Time</option>
                                                <option value="color">Color</option>
                                                <option value="email">Email</option>
                                                <option value="number">Number</option>
                                                <option value="url">URL</option>
                                                <option value="tel">Tel</option>
                                                <option value="password">Password</option>
                                                <option value="hidden">Hidden</option>
                                                <option value="range">Range</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="addField()">Tambah Field</button><br><br>

                            <button type="button" id="btnSave" onclick="save()" class="btn btn-success">Buat Tabel & Generate CRUD</button>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 
        var action = $('#forminput').attr('action');

        var formdata = new FormData($('#forminput')[0]);
        // formdata.append('<?= $this->security->get_csrf_token_name(); ?>', $('#csrf_token').val());
        // ajax adding data to database
        $.ajax({
            url: action,
            type: "POST",
            data: formdata,
            dataType: "JSON",
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                // if (data.csrf_token) {
                //     $('#csrf_token').val(data.csrf_token);
                // }
                if (data.status) //if success close modal and reload ajax table
                {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!!.',
                        text: data.pesan,
                        timer: 3000
                    });
                    location.reload();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.pesan,
                        icon: 'error',
                        showConfirmButton: true,
                    });

                }
                $('#btnSave').text('Buat Tabel & Generate CRUD'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 


            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('Buat Tabel & Generate CRUD'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 

            }
        });
    }
    let fieldCount = 1;

    function addField() {
        const wrapper = document.getElementById('fields-wrapper');

        const div = document.createElement('div');
        div.classList.add('field-row');
        div.innerHTML = `
    <div class="row">
        <div class="form-group col-3">
            <input type="text" class="form-control" name="fields[${fieldCount}][name]" placeholder="Nama Field" required>
        </div>
        <div class="form-group col-3">
            <select name="fields[${fieldCount}][type]" class="form-control select2" required>
                <option value="">-- Tipe Data --</option>
                <option value="INT(11)">INT(11)</option>
                <option value="VARCHAR(255)">VARCHAR(255)</option>
                <option value="TEXT">TEXT</option>
                <option value="DATE">DATE</option>
                <option value="TIMESTAMP">TIMESTAMP</option>
                <option value="FLOAT">FLOAT</option>
                <option value="DOUBLE">DOUBLE</option>
            </select>
        </div>
        <div class="form-group col-3">
            <label>Extra:</label><br>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="fields[${fieldCount}][extra][]" value="PRIMARY KEY" id="pk0">
                <label class="form-check-label" for="pk0">PRIMARY KEY</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="fields[${fieldCount}][extra][]" value="AUTO_INCREMENT" id="ai0">
                <label class="form-check-label" for="ai0">AUTO_INCREMENT</label>
            </div>
        </div>
        
        <div class="form-group col-3">
            <select name="fields[${fieldCount}][tipe]" class="form-control select2" required>
                <option value="">-- Tipe Input --</option>
                <option value="text">Text</option>
                <option value="textarea">Text Area</option>
                <option value="select">Select</option>
                <option value="date">Date</option>
                <option value="file">File</option>
                <option value="checkbox">Checkbox</option>
                <option value="radio">Radio</option>
                <option value="select2">Select2</option>
                <option value="datetime">Datetime</option>
                <option value="time">Time</option>
                <option value="color">Color</option>
                <option value="email">Email</option>
                <option value="number">Number</option>
                <option value="url">URL</option>
                <option value="tel">Tel</option>
                <option value="password">Password</option>
                <option value="hidden">Hidden</option>
                <option value="range">Range</option>
            </select>
        </div>
        <div class="form-group col-12">
            <button type="button" class="btn btn-danger" onclick="removeField(this)">Hapus</button>
        </div>
    </div>
`;

        $(document).ready(function() {
            $('.select2').select2();
        });
        wrapper.appendChild(div);
        fieldCount++;
    }

    function removeField(btn) {
        const fieldRow = btn.closest('.field-row');
        fieldRow.remove();
    }
</script>