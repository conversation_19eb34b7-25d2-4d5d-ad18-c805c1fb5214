<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Settlement Pembayaran Pembelian</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement/pembayaran') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Pembelian</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Nomor Pembelian</th>
                                        <td><?= $pembelian->nomor_pembelian ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Pembelian</th>
                                        <td><?= $pembelian->tanggal_pembelian ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Jatuh Tempo</th>
                                        <td><?= $pembelian->tanggal_jatuh_tempo ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jenis Pembelian</th>
                                        <td>
                                            <?php if ($pembelian->jenis_pembelian == 'reguler'): ?>
                                                Reguler
                                            <?php elseif ($pembelian->jenis_pembelian == 'konsinyasi'): ?>
                                                Konsinyasi
                                            <?php elseif ($pembelian->jenis_pembelian == 'kontrak'): ?>
                                                Kontrak
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Status Pembelian</th>
                                        <td>
                                            <?php if ($pembelian->status == 'draft'): ?>
                                                <span class="badge badge-secondary">Draft</span>
                                            <?php elseif ($pembelian->status == 'disetujui'): ?>
                                                <span class="badge badge-info">Disetujui</span>
                                            <?php elseif ($pembelian->status == 'dipesan'): ?>
                                                <span class="badge badge-primary">Dipesan</span>
                                            <?php elseif ($pembelian->status == 'diterima'): ?>
                                                <span class="badge badge-warning">Diterima</span>
                                            <?php elseif ($pembelian->status == 'selesai'): ?>
                                                <span class="badge badge-success">Selesai</span>
                                            <?php elseif ($pembelian->status == 'dibatalkan'): ?>
                                                <span class="badge badge-danger">Dibatalkan</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Status Pembayaran</th>
                                        <td>
                                            <?php if ($pembelian->status_pembayaran == 'belum_bayar'): ?>
                                                <span class="badge badge-danger">Belum Bayar</span>
                                            <?php elseif ($pembelian->status_pembayaran == 'sebagian'): ?>
                                                <span class="badge badge-warning">Bayar Sebagian</span>
                                            <?php elseif ($pembelian->status_pembayaran == 'lunas'): ?>
                                                <span class="badge badge-success">Lunas</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-info">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Supplier</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Kode Supplier</th>
                                        <td><?= $pembelian->kode_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nama Supplier</th>
                                        <td><?= $pembelian->nama_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Alamat</th>
                                        <td><?= $pembelian->alamat_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Telepon</th>
                                        <td><?= $pembelian->telepon_supplier ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email</th>
                                        <td><?= $pembelian->email_supplier ?? '-' ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Nilai Pembelian</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-bordered">
                                            <tr>
                                                <th width="30%">Subtotal</th>
                                                <td class="text-right"><?= 'Rp ' . number_format($pembelian->subtotal, 2, ',', '.') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Diskon</th>
                                                <td class="text-right"><?= 'Rp ' . number_format($pembelian->diskon, 2, ',', '.') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Pajak</th>
                                                <td class="text-right"><?= 'Rp ' . number_format($pembelian->pajak, 2, ',', '.') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Biaya Pengiriman</th>
                                                <td class="text-right"><?= 'Rp ' . number_format($pembelian->biaya_pengiriman, 2, ',', '.') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Total Pembelian</th>
                                                <td class="text-right font-weight-bold"><?= 'Rp ' . number_format($pembelian->total_pembelian, 2, ',', '.') ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <?php
                                        $total_pembayaran = 0;
                                        foreach ($pembayaran as $row) {
                                            $total_pembayaran += $row->jumlah_pembayaran;
                                        }
                                        $sisa_pembayaran = $pembelian->total_pembelian - $total_pembayaran;
                                        ?>
                                        <table class="table table-bordered">
                                            <tr>
                                                <th width="30%">Total Pembayaran</th>
                                                <td class="text-right"><?= 'Rp ' . number_format($total_pembayaran, 2, ',', '.') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Sisa Pembayaran</th>
                                                <td class="text-right <?= $sisa_pembayaran > 0 ? 'text-danger' : 'text-success' ?>"><?= 'Rp ' . number_format($sisa_pembayaran, 2, ',', '.') ?></td>
                                            </tr>
                                            <tr>
                                                <th>Persentase Terbayar</th>
                                                <td class="text-right">
                                                    <?php
                                                    $persentase = ($total_pembayaran / $pembelian->total_pembelian) * 100;
                                                    echo number_format($persentase, 2) . '%';
                                                    ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Umur Hutang</th>
                                                <td class="text-right">
                                                    <?php
                                                    if ($pembelian->status_pembayaran == 'lunas') {
                                                        echo '-';
                                                    } else {
                                                        $tanggal_jatuh_tempo = new DateTime($pembelian->tanggal_jatuh_tempo ?? $pembelian->tanggal_pembelian);
                                                        $tanggal_sekarang = new DateTime();
                                                        $selisih = $tanggal_sekarang->diff($tanggal_jatuh_tempo);
                                                        
                                                        if ($tanggal_sekarang > $tanggal_jatuh_tempo) {
                                                            echo '<span class="text-danger">' . $selisih->days . ' hari</span>';
                                                        } else {
                                                            echo '<span class="text-success">Belum jatuh tempo</span>';
                                                        }
                                                    }
                                                    ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Riwayat Pembayaran</h3>
                            </div>
                            <div class="card-body">
                                <?php if (empty($pembayaran)): ?>
                                <div class="alert alert-warning">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Belum ada pembayaran untuk pembelian ini.</p>
                                </div>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Tanggal Pembayaran</th>
                                                <th>Jumlah Pembayaran</th>
                                                <th>Metode Pembayaran</th>
                                                <th>Nomor Referensi</th>
                                                <th>Keterangan</th>
                                                <th>Dibuat Oleh</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            foreach ($pembayaran as $row): 
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->tanggal_pembayaran ?></td>
                                                <td class="text-right"><?= 'Rp ' . number_format($row->jumlah_pembayaran, 2, ',', '.') ?></td>
                                                <td>
                                                    <?php
                                                    switch ($row->metode_pembayaran) {
                                                        case 'tunai':
                                                            echo 'Tunai';
                                                            break;
                                                        case 'transfer':
                                                            echo 'Transfer Bank';
                                                            break;
                                                        case 'cek':
                                                            echo 'Cek/Giro';
                                                            break;
                                                        case 'kartu_kredit':
                                                            echo 'Kartu Kredit';
                                                            break;
                                                        case 'kartu_debit':
                                                            echo 'Kartu Debit';
                                                            break;
                                                        default:
                                                            echo 'Lainnya';
                                                    }
                                                    ?>
                                                </td>
                                                <td><?= $row->nomor_referensi ?? '-' ?></td>
                                                <td><?= $row->keterangan ?? '-' ?></td>
                                                <td><?= $row->created_by ?? '-' ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="2" class="text-right">Total Pembayaran</th>
                                                <th class="text-right"><?= 'Rp ' . number_format($total_pembayaran, 2, ',', '.') ?></th>
                                                <th colspan="4"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                <?php endif; ?>

                                <?php if ($pembelian->status_pembayaran != 'lunas'): ?>
                                <div class="alert alert-warning mt-3">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Pembelian ini belum lunas. Silakan lakukan pembayaran untuk menyelesaikan transaksi ini.</p>
                                    <a href="<?= site_url('Pembelian/pembayaran/' . $pembelian->id) ?>" class="btn btn-primary"><i class="fas fa-plus"></i> Tambah Pembayaran</a>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-success mt-3">
                                    <h5><i class="icon fas fa-check"></i> Informasi</h5>
                                    <p>Pembelian ini telah lunas.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>