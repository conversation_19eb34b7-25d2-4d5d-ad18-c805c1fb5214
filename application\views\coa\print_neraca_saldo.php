<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Neraca Saldo',
    'document_number' => 'NS-' . date('Ymd-His'),
    'document_title' => 'NERACA SALDO'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Laporan',
        'items' => [
            ['label' => 'Per Tanggal', 'value' => '<strong>' . format_date_indonesia($tanggal) . '</strong>'],
            ['label' => 'Periode', 'value' => 'Sampai dengan ' . format_date_indonesia($tanggal)],
            ['label' => 'Tanggal Cetak', 'value' => format_date_indonesia(date('Y-m-d'), true)],
            ['label' => 'Mata Uang', 'value' => 'IDR (Rupiah)']
        ]
    ]
];

// Prepare table headers
$table_headers = [
    ['label' => 'Kode Akun', 'width' => '12%'],
    ['label' => 'Nama Akun', 'width' => '35%'],
    ['label' => 'Saldo Awal Debit', 'width' => '13%', 'align' => 'right'],
    ['label' => 'Saldo Awal Kredit', 'width' => '13%', 'align' => 'right'],
    ['label' => 'Mutasi Debit', 'width' => '13%', 'align' => 'right'],
    ['label' => 'Mutasi Kredit', 'width' => '13%', 'align' => 'right'],
    ['label' => 'Saldo Akhir Debit', 'width' => '13%', 'align' => 'right'],
    ['label' => 'Saldo Akhir Kredit', 'width' => '13%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$total_saldo_awal_debit = 0;
$total_saldo_awal_kredit = 0;
$total_mutasi_debit = 0;
$total_mutasi_kredit = 0;
$total_saldo_akhir_debit = 0;
$total_saldo_akhir_kredit = 0;

if (!empty($neraca_data)) {
    foreach ($neraca_data as $data) {
        $nama_akun = $data->nama_akun;
        if ($data->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($data->level - 1)) . $nama_akun;
        }
        
        $saldo_awal_debit = $data->saldo_awal > 0 ? $data->saldo_awal : 0;
        $saldo_awal_kredit = $data->saldo_awal < 0 ? abs($data->saldo_awal) : 0;
        
        $saldo_akhir = $data->saldo_awal + $data->mutasi_debit - $data->mutasi_kredit;
        $saldo_akhir_debit = $saldo_akhir > 0 ? $saldo_akhir : 0;
        $saldo_akhir_kredit = $saldo_akhir < 0 ? abs($saldo_akhir) : 0;
        
        $table_data[] = [
            $data->kode_akun,
            $nama_akun,
            $saldo_awal_debit > 0 ? format_currency($saldo_awal_debit, false) : '-',
            $saldo_awal_kredit > 0 ? format_currency($saldo_awal_kredit, false) : '-',
            $data->mutasi_debit > 0 ? format_currency($data->mutasi_debit, false) : '-',
            $data->mutasi_kredit > 0 ? format_currency($data->mutasi_kredit, false) : '-',
            $saldo_akhir_debit > 0 ? format_currency($saldo_akhir_debit, false) : '-',
            $saldo_akhir_kredit > 0 ? format_currency($saldo_akhir_kredit, false) : '-'
        ];
        
        $total_saldo_awal_debit += $saldo_awal_debit;
        $total_saldo_awal_kredit += $saldo_awal_kredit;
        $total_mutasi_debit += $data->mutasi_debit;
        $total_mutasi_kredit += $data->mutasi_kredit;
        $total_saldo_akhir_debit += $saldo_akhir_debit;
        $total_saldo_akhir_kredit += $saldo_akhir_kredit;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL:</strong>', 'colspan' => '2', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_saldo_awal_debit, false) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_saldo_awal_kredit, false) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_mutasi_debit, false) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_mutasi_kredit, false) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_saldo_akhir_debit, false) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_saldo_akhir_kredit, false) . '</strong>', 'align' => 'right']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-money"></span>Daftar Saldo Akun', $table_headers, $table_data, $table_options);

// Add validation section
$is_balanced_awal = ($total_saldo_awal_debit == $total_saldo_awal_kredit);
$is_balanced_akhir = ($total_saldo_akhir_debit == $total_saldo_akhir_kredit);

$content .= '<div class="summary-section">';
$content .= '<div class="summary-left">';

$validation_content = 'Validasi Neraca Saldo:' . "\n";
$validation_content .= '• Saldo Awal - Debit: ' . format_currency($total_saldo_awal_debit) . "\n";
$validation_content .= '• Saldo Awal - Kredit: ' . format_currency($total_saldo_awal_kredit) . "\n";
$validation_content .= '• Status Saldo Awal: ' . ($is_balanced_awal ? '✅ SEIMBANG' : '❌ TIDAK SEIMBANG') . "\n\n";

$validation_content .= '• Saldo Akhir - Debit: ' . format_currency($total_saldo_akhir_debit) . "\n";
$validation_content .= '• Saldo Akhir - Kredit: ' . format_currency($total_saldo_akhir_kredit) . "\n";
$validation_content .= '• Status Saldo Akhir: ' . ($is_balanced_akhir ? '✅ SEIMBANG' : '❌ TIDAK SEIMBANG') . "\n\n";

$validation_content .= 'Catatan:' . "\n";
$validation_content .= '• Neraca saldo harus seimbang (Total Debit = Total Kredit)' . "\n";
$validation_content .= '• Jika tidak seimbang, periksa kembali pencatatan transaksi' . "\n";
$validation_content .= '• Laporan ini menunjukkan posisi saldo semua akun';

$content .= create_notes_section('Validasi & Analisis', $validation_content);

$content .= '</div>';
$content .= '<div class="summary-right">';

// Summary table
$summary_items = [
    ['label' => 'Total Saldo Awal Debit', 'value' => format_currency($total_saldo_awal_debit)],
    ['label' => 'Total Saldo Awal Kredit', 'value' => format_currency($total_saldo_awal_kredit)],
    ['label' => 'Total Mutasi Debit', 'value' => format_currency($total_mutasi_debit)],
    ['label' => 'Total Mutasi Kredit', 'value' => format_currency($total_mutasi_kredit)],
    ['label' => 'Total Saldo Akhir Debit', 'value' => format_currency($total_saldo_akhir_debit)],
    ['label' => 'Total Saldo Akhir Kredit', 'value' => format_currency($total_saldo_akhir_kredit), 'class' => 'total-final']
];

$content .= create_summary_table($summary_items);

$content .= '</div>';
$content .= '</div>';

// Add balance validation
$content .= '<div class="notes-section">';
$content .= '<div class="notes-title">Status Keseimbangan:</div>';
$content .= '<div class="notes-content text-center">';
if ($is_balanced_akhir) {
    $content .= '<span class="color-success font-bold text-large">✅ NERACA SALDO SEIMBANG</span><br>';
    $content .= 'Total Debit = Total Kredit = ' . format_currency($total_saldo_akhir_debit);
} else {
    $content .= '<span class="color-danger font-bold text-large">❌ NERACA SALDO TIDAK SEIMBANG</span><br>';
    $content .= 'Selisih: ' . format_currency(abs($total_saldo_akhir_debit - $total_saldo_akhir_kredit)) . '<br>';
    $content .= '<small>Harap periksa kembali pencatatan transaksi</small>';
}
$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => '(............................)',
        'position' => 'Staff Accounting'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Accounting'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Manager Finance'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>