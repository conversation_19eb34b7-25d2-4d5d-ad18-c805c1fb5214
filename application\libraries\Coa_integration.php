<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * COA Integration Library
 * Handles automatic journal entries for business transactions
 */
class Coa_integration
{
    protected $CI;
    protected $coa_model;
    
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->load->model('Mod_coa');
        $this->coa_model = $this->CI->Mod_coa;
    }

    /**
     * Process sales transaction and create journal entries
     * @param array $sales_data Sales transaction data
     * @return array Result with journal IDs
     */
    public function process_sales_transaction($sales_data)
    {
        $result = array(
            'success' => false,
            'sales_journal_id' => null,
            'cogs_journal_id' => null,
            'message' => ''
        );

        try {
            // Create sales journal entry
            $sales_journal_data = array(
                'tanggal' => $sales_data['tanggal_faktur'],
                'nomor_transaksi' => $sales_data['nomor_faktur'],
                'total_amount' => $sales_data['total_faktur'],
                'payment_method' => isset($sales_data['payment_method']) ? $sales_data['payment_method'] : 'kredit'
            );

            $sales_journal_id = $this->coa_model->create_sales_journal($sales_journal_data);
            
            if (!$sales_journal_id) {
                $result['message'] = 'Failed to create sales journal';
                return $result;
            }

            $result['sales_journal_id'] = $sales_journal_id;

            // Create COGS journal entry if COGS data is available
            if (isset($sales_data['total_cogs']) && $sales_data['total_cogs'] > 0) {
                $cogs_journal_data = array(
                    'tanggal' => $sales_data['tanggal_faktur'],
                    'nomor_transaksi' => $sales_data['nomor_faktur'],
                    'total_cogs' => $sales_data['total_cogs']
                );

                $cogs_journal_id = $this->coa_model->create_cogs_journal($cogs_journal_data);
                $result['cogs_journal_id'] = $cogs_journal_id;
            }

            $result['success'] = true;
            $result['message'] = 'Sales transaction processed successfully';

        } catch (Exception $e) {
            $result['message'] = 'Error processing sales transaction: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Process purchase transaction and create journal entries
     * @param array $purchase_data Purchase transaction data
     * @return array Result with journal ID
     */
    public function process_purchase_transaction($purchase_data)
    {
        $result = array(
            'success' => false,
            'journal_id' => null,
            'message' => ''
        );

        try {
            $journal_data = array(
                'tanggal' => $purchase_data['tanggal_pembelian'],
                'nomor_transaksi' => $purchase_data['nomor_pembelian'],
                'total_amount' => $purchase_data['total_akhir'],
                'payment_method' => isset($purchase_data['payment_method']) ? $purchase_data['payment_method'] : 'kredit'
            );

            $journal_id = $this->coa_model->create_purchase_journal($journal_data);
            
            if (!$journal_id) {
                $result['message'] = 'Failed to create purchase journal';
                return $result;
            }

            $result['journal_id'] = $journal_id;
            $result['success'] = true;
            $result['message'] = 'Purchase transaction processed successfully';

        } catch (Exception $e) {
            $result['message'] = 'Error processing purchase transaction: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Process payment transaction and create journal entries
     * @param array $payment_data Payment transaction data
     * @return array Result with journal ID
     */
    public function process_payment_transaction($payment_data)
    {
        $result = array(
            'success' => false,
            'journal_id' => null,
            'message' => ''
        );

        try {
            $journal_data = array(
                'tanggal' => $payment_data['tanggal_pembayaran'],
                'referensi' => $payment_data['referensi'],
                'amount' => $payment_data['jumlah_pembayaran'],
                'type' => $payment_data['type'] // 'receivable' or 'payable'
            );

            $journal_id = $this->coa_model->create_payment_journal($journal_data);
            
            if (!$journal_id) {
                $result['message'] = 'Failed to create payment journal';
                return $result;
            }

            $result['journal_id'] = $journal_id;
            $result['success'] = true;
            $result['message'] = 'Payment transaction processed successfully';

        } catch (Exception $e) {
            $result['message'] = 'Error processing payment transaction: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Process inventory adjustment and create journal entries
     * @param array $adjustment_data Inventory adjustment data
     * @return array Result with journal ID
     */
    public function process_inventory_adjustment($adjustment_data)
    {
        $result = array(
            'success' => false,
            'journal_id' => null,
            'message' => ''
        );

        try {
            $journal_data = array(
                'tanggal' => $adjustment_data['tanggal'],
                'referensi' => $adjustment_data['referensi'],
                'adjustment_value' => $adjustment_data['adjustment_value']
            );

            $journal_id = $this->coa_model->create_inventory_adjustment_journal($journal_data);
            
            if (!$journal_id) {
                $result['message'] = 'Failed to create inventory adjustment journal';
                return $result;
            }

            $result['journal_id'] = $journal_id;
            $result['success'] = true;
            $result['message'] = 'Inventory adjustment processed successfully';

        } catch (Exception $e) {
            $result['message'] = 'Error processing inventory adjustment: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Calculate COGS for sales transaction
     * @param array $sales_detail Sales detail items
     * @return float Total COGS
     */
    public function calculate_cogs($sales_detail)
    {
        $total_cogs = 0;
        
        foreach ($sales_detail as $item) {
            // Get current cost from barang table
            $this->CI->db->select('harga_beli');
            $this->CI->db->from('barang');
            $this->CI->db->where('id', $item['id_barang']);
            $barang = $this->CI->db->get()->row();
            
            if ($barang) {
                $cogs_per_item = $barang->harga_beli * $item['qty'];
                $total_cogs += $cogs_per_item;
            }
        }
        
        return $total_cogs;
    }

    /**
     * Calculate inventory adjustment value
     * @param array $adjustment_items Adjustment items
     * @return float Total adjustment value
     */
    public function calculate_inventory_adjustment_value($adjustment_items)
    {
        $total_adjustment = 0;
        
        foreach ($adjustment_items as $item) {
            // Get current cost from barang table
            $this->CI->db->select('harga_beli');
            $this->CI->db->from('barang');
            $this->CI->db->where('id', $item['id_barang']);
            $barang = $this->CI->db->get()->row();
            
            if ($barang) {
                $qty_difference = $item['qty_fisik'] - $item['qty_sistem'];
                $adjustment_value = $barang->harga_beli * $qty_difference;
                $total_adjustment += $adjustment_value;
            }
        }
        
        return $total_adjustment;
    }

    /**
     * Get account balance
     * @param string $account_code Account code
     * @param string $date Date for balance calculation
     * @return float Account balance
     */
    public function get_account_balance($account_code, $date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $account = $this->coa_model->get_akun_by_kode($account_code);
        if (!$account) {
            return 0;
        }
        
        return $this->coa_model->get_saldo_awal($account->id, $date);
    }

    /**
     * Validate journal entry balance
     * @param array $entries Journal entries
     * @return bool True if balanced
     */
    public function validate_journal_balance($entries)
    {
        $total_debit = 0;
        $total_kredit = 0;
        
        foreach ($entries as $entry) {
            $total_debit += isset($entry['debit']) ? $entry['debit'] : 0;
            $total_kredit += isset($entry['kredit']) ? $entry['kredit'] : 0;
        }
        
        return abs($total_debit - $total_kredit) < 0.01; // Allow small rounding differences
    }
}
