<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Laporan Pembelian
 * Mengatur berbagai jenis laporan pembelian
 */
class Mod_laporan_pembelian extends CI_Model
{
    /**
     * Get supplier list untuk dropdown
     */
    function get_supplier_list()
    {
        $this->db->select('id, kode, nama');
        $this->db->from('supplier');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'ASC');
        return $this->db->get();
    }

    /**
     * Get barang list untuk dropdown
     */
    function get_barang_list()
    {
        $this->db->select('id, kode_barang, nama_barang');
        $this->db->from('barang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_barang', 'ASC');
        return $this->db->get();
    }

    /**
     * Get status list untuk dropdown
     */
    function get_status_list()
    {
        return array(
            'draft' => 'Draft',
            'disetujui' => 'Disetujui',
            'dipesan' => 'Dipesan',
            'diterima' => 'Diterima',
            'selesai' => 'Selesai',
            'dibatalkan' => 'Dibatalkan'
        );
    }

    /**
     * Get summary pembelian untuk dashboard
     */
    function get_summary_pembelian()
    {
        $query = $this->db->query("
            SELECT 
                COUNT(*) as total_transaksi,
                COUNT(DISTINCT id_supplier) as total_supplier,
                SUM(total_item) as total_item,
                SUM(total_qty) as total_qty,
                SUM(total_akhir) as total_nilai_pembelian,
                COALESCE(SUM(pp.total_dibayar), 0) as total_dibayar,
                SUM(total_akhir) - COALESCE(SUM(pp.total_dibayar), 0) as total_outstanding
            FROM pembelian p
            LEFT JOIN (
                SELECT id_pembelian, SUM(jumlah_bayar) as total_dibayar
                FROM pembelian_pembayaran 
                WHERE status = 'verified'
                GROUP BY id_pembelian
            ) pp ON p.id = pp.id_pembelian
            WHERE p.status != 'dibatalkan'
        ");
        
        if ($query) {
            return $query->row();
        } else {
            // Return default values if query fails
            return (object) array(
                'total_transaksi' => 0,
                'total_supplier' => 0,
                'total_item' => 0,
                'total_qty' => 0,
                'total_nilai_pembelian' => 0,
                'total_dibayar' => 0,
                'total_outstanding' => 0
            );
        }
    }

    /**
     * Laporan Pembelian Periode
     */
    function get_laporan_pembelian_periode($id_supplier = null, $status = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            p.id,
            p.nomor_pembelian,
            p.tanggal_pembelian,
            p.jenis_pembelian,
            p.status,
            p.total_item,
            p.total_qty,
            p.subtotal,
            p.ppn_nominal,
            p.total_akhir,
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            COALESCE(pp.total_dibayar, 0) as total_dibayar,
            p.total_akhir - COALESCE(pp.total_dibayar, 0) as sisa_pembayaran,
            CASE 
                WHEN COALESCE(pp.total_dibayar, 0) = 0 THEN "belum_bayar"
                WHEN COALESCE(pp.total_dibayar, 0) >= p.total_akhir THEN "lunas"
                ELSE "sebagian"
            END as status_pembayaran
        ');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        $this->db->join('(SELECT id_pembelian, SUM(jumlah_bayar) as total_dibayar FROM pembelian_pembayaran WHERE status = "verified" GROUP BY id_pembelian) pp', 'p.id = pp.id_pembelian', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_supplier)) {
            $this->db->where('p.id_supplier', $id_supplier);
        }
        
        if (!empty($status)) {
            $this->db->where('p.status', $status);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('p.tanggal_pembelian >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('p.tanggal_pembelian <=', $tanggal_sampai);
        }
        
        $this->db->order_by('p.tanggal_pembelian', 'DESC');
        $this->db->order_by('p.nomor_pembelian', 'ASC');
        
        return $this->db->get();
    }

    /**
     * Laporan Detail Pembelian (Item per Item)
     */
    function get_laporan_detail_pembelian($id_supplier = null, $id_barang = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            p.nomor_pembelian,
            p.tanggal_pembelian,
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            sat.nama_satuan,
            pd.qty,
            pd.harga_satuan,
            pd.diskon_persen,
            pd.diskon_nominal,
            pd.subtotal_sebelum_diskon,
            pd.subtotal_setelah_diskon,
            pd.ppn_persen,
            pd.ppn_nominal,
            pd.total_akhir,
            g.nama_gudang
        ');
        $this->db->from('pembelian_detail pd');
        $this->db->join('pembelian p', 'pd.id_pembelian = p.id');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        $this->db->join('barang b', 'pd.id_barang = b.id');
        $this->db->join('satuan sat', 'b.satuan_id = sat.id', 'left');
        $this->db->join('gudang g', 'pd.id_gudang = g.id', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_supplier)) {
            $this->db->where('p.id_supplier', $id_supplier);
        }
        
        if (!empty($id_barang)) {
            $this->db->where('pd.id_barang', $id_barang);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('p.tanggal_pembelian >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('p.tanggal_pembelian <=', $tanggal_sampai);
        }
        
        $this->db->where('p.status !=', 'dibatalkan');
        $this->db->order_by('p.tanggal_pembelian', 'DESC');
        $this->db->order_by('p.nomor_pembelian', 'ASC');
        $this->db->order_by('b.nama_barang', 'ASC');
        
        return $this->db->get();
    }

    /**
     * Laporan Pembelian per Supplier (Summary)
     */
    function get_laporan_pembelian_supplier($id_supplier = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            s.alamat,
            s.no_telepon as telepon,
            s.email,
            COUNT(p.id) as total_transaksi,
            SUM(p.total_item) as total_item,
            SUM(p.total_qty) as total_qty,
            SUM(p.total_akhir) as total_pembelian,
            COALESCE(SUM(pp.total_dibayar), 0) as total_dibayar,
            SUM(p.total_akhir) - COALESCE(SUM(pp.total_dibayar), 0) as sisa_pembayaran,
            AVG(p.total_akhir) as rata_rata_pembelian,
            MIN(p.tanggal_pembelian) as pembelian_pertama,
            MAX(p.tanggal_pembelian) as pembelian_terakhir
        ');
        $this->db->from('supplier s');
        $this->db->join('pembelian p', 's.id = p.id_supplier');
        $this->db->join('(SELECT id_pembelian, SUM(jumlah_bayar) as total_dibayar FROM pembelian_pembayaran WHERE status = "verified" GROUP BY id_pembelian) pp', 'p.id = pp.id_pembelian', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_supplier)) {
            $this->db->where('s.id', $id_supplier);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('p.tanggal_pembelian >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('p.tanggal_pembelian <=', $tanggal_sampai);
        }
        
        $this->db->where('p.status !=', 'dibatalkan');
        $this->db->group_by('s.id, s.kode, s.nama, s.alamat, s.no_telepon, s.email');
        $this->db->order_by('total_pembelian', 'DESC');
        
        return $this->db->get();
    }

    /**
     * Laporan Pembelian per Barang (Summary)
     */
    function get_laporan_pembelian_barang($id_barang = null, $id_supplier = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            sat.nama_satuan,
            SUM(pd.qty) as total_qty,
            AVG(pd.harga_satuan) as harga_rata_rata,
            MIN(pd.harga_satuan) as harga_terendah,
            MAX(pd.harga_satuan) as harga_tertinggi,
            SUM(pd.total_akhir) as total_pembelian,
            COUNT(DISTINCT p.id_supplier) as jumlah_supplier,
            COUNT(p.id) as jumlah_transaksi,
            MIN(p.tanggal_pembelian) as pembelian_pertama,
            MAX(p.tanggal_pembelian) as pembelian_terakhir
        ');
        $this->db->from('barang b');
        $this->db->join('pembelian_detail pd', 'b.id = pd.id_barang');
        $this->db->join('pembelian p', 'pd.id_pembelian = p.id');
        $this->db->join('satuan sat', 'b.satuan_id = sat.id', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_barang)) {
            $this->db->where('b.id', $id_barang);
        }
        
        if (!empty($id_supplier)) {
            $this->db->where('p.id_supplier', $id_supplier);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('p.tanggal_pembelian >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('p.tanggal_pembelian <=', $tanggal_sampai);
        }
        
        $this->db->where('p.status !=', 'dibatalkan');
        $this->db->group_by('b.id, b.kode_barang, b.nama_barang, b.merk, b.tipe, sat.nama_satuan');
        $this->db->order_by('total_pembelian', 'DESC');
        
        return $this->db->get();
    }

    /**
     * Laporan Outstanding Purchase Order
     */
    function get_laporan_outstanding_po($id_supplier = null)
    {
        $this->db->select('
            p.nomor_pembelian,
            p.tanggal_pembelian,
            p.tanggal_jatuh_tempo,
            p.status,
            p.total_akhir,
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            COALESCE(pp.total_dibayar, 0) as total_dibayar,
            p.total_akhir - COALESCE(pp.total_dibayar, 0) as sisa_pembayaran,
            CASE 
                WHEN COALESCE(pp.total_dibayar, 0) = 0 THEN "belum_bayar"
                WHEN COALESCE(pp.total_dibayar, 0) >= p.total_akhir THEN "lunas"
                ELSE "sebagian"
            END as status_pembayaran,
            DATEDIFF(CURDATE(), p.tanggal_pembelian) as umur_po,
            CASE 
                WHEN p.tanggal_jatuh_tempo IS NOT NULL AND CURDATE() > p.tanggal_jatuh_tempo THEN "Overdue"
                WHEN p.tanggal_jatuh_tempo IS NOT NULL AND DATEDIFF(p.tanggal_jatuh_tempo, CURDATE()) <= 7 THEN "Akan Jatuh Tempo"
                ELSE "Normal"
            END as status_jatuh_tempo
        ');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        $this->db->join('(SELECT id_pembelian, SUM(jumlah_bayar) as total_dibayar FROM pembelian_pembayaran WHERE status = "verified" GROUP BY id_pembelian) pp', 'p.id = pp.id_pembelian', 'left');
        
        // Filter hanya PO yang belum selesai
        $this->db->where_in('p.status', array('disetujui', 'dipesan', 'diterima'));
        
        // Filter berdasarkan parameter
        if (!empty($id_supplier)) {
            $this->db->where('p.id_supplier', $id_supplier);
        }
        
        $this->db->order_by('p.tanggal_pembelian', 'ASC');
        
        return $this->db->get();
    }

    /**
     * Get laporan aging pembelian (berdasarkan umur)
     */
    function get_laporan_aging_pembelian($id_supplier = null)
    {
        $this->db->select('
            p.nomor_pembelian,
            p.tanggal_pembelian,
            p.total_akhir,
            p.sisa_pembayaran,
            s.nama_supplier,
            DATEDIFF(CURDATE(), p.tanggal_pembelian) as umur_hari,
            CASE 
                WHEN DATEDIFF(CURDATE(), p.tanggal_pembelian) <= 30 THEN "0-30 Hari"
                WHEN DATEDIFF(CURDATE(), p.tanggal_pembelian) <= 60 THEN "31-60 Hari"
                WHEN DATEDIFF(CURDATE(), p.tanggal_pembelian) <= 90 THEN "61-90 Hari"
                ELSE "Lebih dari 90 Hari"
            END as kategori_aging
        ');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        $this->db->where('p.sisa_pembayaran >', 0);
        $this->db->where('p.status !=', 'dibatalkan');
        
        if (!empty($id_supplier)) {
            $this->db->where('p.id_supplier', $id_supplier);
        }
        
        $this->db->order_by('umur_hari', 'DESC');
        
        return $this->db->get();
    }

    /**
     * Get top supplier berdasarkan nilai pembelian
     */
    function get_top_supplier($limit = 10, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            s.nama_supplier,
            s.kode_supplier,
            COUNT(p.id) as total_transaksi,
            SUM(p.total_akhir) as total_pembelian,
            AVG(p.total_akhir) as rata_rata_pembelian
        ');
        $this->db->from('supplier s');
        $this->db->join('pembelian p', 's.id = p.id_supplier');
        
        if (!empty($tanggal_dari)) {
            $this->db->where('p.tanggal_pembelian >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('p.tanggal_pembelian <=', $tanggal_sampai);
        }
        
        $this->db->where('p.status !=', 'dibatalkan');
        $this->db->group_by('s.id, s.nama_supplier, s.kode_supplier');
        $this->db->order_by('total_pembelian', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get();
    }

    /**
     * Get top barang berdasarkan qty pembelian
     */
    function get_top_barang($limit = 10, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            b.nama_barang,
            b.kode_barang,
            b.merk,
            sat.nama_satuan,
            SUM(pd.qty) as total_qty,
            SUM(pd.total_akhir) as total_pembelian,
            AVG(pd.harga_satuan) as harga_rata_rata
        ');
        $this->db->from('barang b');
        $this->db->join('pembelian_detail pd', 'b.id = pd.id_barang');
        $this->db->join('pembelian p', 'pd.id_pembelian = p.id');
        $this->db->join('satuan sat', 'b.id_satuan = sat.id', 'left');
        
        if (!empty($tanggal_dari)) {
            $this->db->where('p.tanggal_pembelian >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('p.tanggal_pembelian <=', $tanggal_sampai);
        }
        
        $this->db->where('p.status !=', 'dibatalkan');
        $this->db->group_by('b.id, b.nama_barang, b.kode_barang, b.merk, sat.nama_satuan');
        $this->db->order_by('total_qty', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get();
    }

    /**
     * Get statistik pembelian bulanan
     */
    function get_statistik_bulanan($tahun = null)
    {
        if (empty($tahun)) {
            $tahun = date('Y');
        }
        
        $this->db->select('
            MONTH(tanggal_pembelian) as bulan,
            MONTHNAME(tanggal_pembelian) as nama_bulan,
            COUNT(*) as total_transaksi,
            SUM(total_akhir) as total_pembelian,
            AVG(total_akhir) as rata_rata_pembelian
        ');
        $this->db->from('pembelian');
        $this->db->where('YEAR(tanggal_pembelian)', $tahun);
        $this->db->where('status !=', 'dibatalkan');
        $this->db->group_by('MONTH(tanggal_pembelian), MONTHNAME(tanggal_pembelian)');
        $this->db->order_by('bulan', 'ASC');
        
        return $this->db->get();
    }
}