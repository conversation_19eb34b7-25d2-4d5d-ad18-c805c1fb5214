<?php
// Helper function untuk format currency
if (!function_exists('format_currency')) {
    function format_currency($amount) {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }
}

// Helper function untuk format date
if (!function_exists('format_date')) {
    function format_date($date) {
        return date('d/m/Y', strtotime($date));
    }
}
?>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Informasi Filter</h5>
            <div class="row">
                <?php if (!empty($filter['tanggal_dari']) || !empty($filter['tanggal_sampai'])): ?>
                <div class="col-md-4">
                    <strong>Periode:</strong> 
                    <?= !empty($filter['tanggal_dari']) ? format_date($filter['tanggal_dari']) : 'Awal' ?> 
                    s/d 
                    <?= !empty($filter['tanggal_sampai']) ? format_date($filter['tanggal_sampai']) : 'Akhir' ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($info_pelanggan)): ?>
                <div class="col-md-4">
                    <strong>Pelanggan:</strong> <?= $info_pelanggan ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($info_barang)): ?>
                <div class="col-md-4">
                    <strong>Barang:</strong> <?= $info_barang ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($filter['status'])): ?>
                <div class="col-md-4">
                    <strong>Status:</strong> <?= ucfirst($filter['status']) ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($laporan && $laporan->num_rows() > 0): ?>
    <?php 
    $data = $laporan->result();
    $total_records = count($data);
    ?>
    
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> Ditemukan <strong><?= number_format($total_records) ?></strong> record data
    </div>

    <div class="table-responsive">
        <?php switch($jenis_laporan): 
            case 'penjualan_periode': ?>
                <table class="table table-bordered table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th width="5%">No</th>
                            <th>Nomor Faktur</th>
                            <th>Tanggal</th>
                            <th>Pelanggan</th>
                            <th>Status</th>
                            <th>Status Pembayaran</th>
                            <th>Total Item</th>
                            <th>Total Qty</th>
                            <th>Subtotal</th>
                            <th>Diskon</th>
                            <th>Pajak</th>
                            <th>Total Faktur</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_item = 0;
                        $total_qty = 0;
                        $total_subtotal = 0;
                        $total_diskon = 0;
                        $total_pajak = 0;
                        $total_faktur = 0;
                        
                        foreach($data as $row): 
                            $total_item += $row->total_item;
                            $total_qty += $row->total_qty;
                            $total_subtotal += $row->subtotal;
                            $total_diskon += $row->diskon;
                            $total_pajak += $row->pajak;
                            $total_faktur += $row->total_faktur;
                        ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td><?= $row->nomor_faktur ?></td>
                            <td><?= format_date($row->tanggal_faktur) ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td>
                                <span class="badge badge-<?= $row->status == 'final' ? 'success' : ($row->status == 'draft' ? 'warning' : 'danger') ?>">
                                    <?= ucfirst($row->status) ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?= $row->status_pembayaran == 'lunas' ? 'success' : ($row->status_pembayaran == 'sebagian' ? 'warning' : 'danger') ?>">
                                    <?= ucfirst(str_replace('_', ' ', $row->status_pembayaran)) ?>
                                </span>
                            </td>
                            <td class="text-center"><?= number_format($row->total_item) ?></td>
                            <td class="text-center"><?= number_format($row->total_qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->subtotal) ?></td>
                            <td class="text-right"><?= format_currency($row->diskon) ?></td>
                            <td class="text-right"><?= format_currency($row->pajak) ?></td>
                            <td class="text-right"><strong><?= format_currency($row->total_faktur) ?></strong></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="thead-dark">
                        <tr>
                            <th colspan="6">TOTAL</th>
                            <th class="text-center"><?= number_format($total_item) ?></th>
                            <th class="text-center"><?= number_format($total_qty, 2) ?></th>
                            <th class="text-right"><?= format_currency($total_subtotal) ?></th>
                            <th class="text-right"><?= format_currency($total_diskon) ?></th>
                            <th class="text-right"><?= format_currency($total_pajak) ?></th>
                            <th class="text-right"><strong><?= format_currency($total_faktur) ?></strong></th>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'detail_penjualan': ?>
                <table class="table table-bordered table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th width="5%">No</th>
                            <th>Nomor Faktur</th>
                            <th>Tanggal</th>
                            <th>Pelanggan</th>
                            <th>Kode Barang</th>
                            <th>Nama Barang</th>
                            <th>Merk</th>
                            <th>Satuan</th>
                            <th>Qty</th>
                            <th>Harga Satuan</th>
                            <th>Diskon</th>
                            <th>Subtotal</th>
                            <th>Pajak</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_qty = 0;
                        $total_diskon = 0;
                        $total_subtotal = 0;
                        $total_pajak = 0;
                        $total_akhir = 0;
                        
                        foreach($data as $row): 
                            $total_qty += $row->qty;
                            $total_diskon += $row->diskon_nilai;
                            $total_subtotal += $row->subtotal;
                            $total_pajak += $row->pajak_nilai;
                            $total_akhir += $row->total;
                        ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td><?= $row->nomor_faktur ?></td>
                            <td><?= format_date($row->tanggal_faktur) ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td><?= $row->kode_barang ?></td>
                            <td><?= $row->nama_barang ?></td>
                            <td><?= $row->merk ?></td>
                            <td><?= $row->nama_satuan ?></td>
                            <td class="text-center"><?= number_format($row->qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_satuan) ?></td>
                            <td class="text-right"><?= format_currency($row->diskon_nilai) ?></td>
                            <td class="text-right"><?= format_currency($row->subtotal) ?></td>
                            <td class="text-right"><?= format_currency($row->pajak_nilai) ?></td>
                            <td class="text-right"><strong><?= format_currency($row->total) ?></strong></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="thead-dark">
                        <tr>
                            <th colspan="8">TOTAL</th>
                            <th class="text-center"><?= number_format($total_qty, 2) ?></th>
                            <th></th>
                            <th class="text-right"><?= format_currency($total_diskon) ?></th>
                            <th class="text-right"><?= format_currency($total_subtotal) ?></th>
                            <th class="text-right"><?= format_currency($total_pajak) ?></th>
                            <th class="text-right"><strong><?= format_currency($total_akhir) ?></strong></th>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'penjualan_pelanggan': ?>
                <table class="table table-bordered table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th width="5%">No</th>
                            <th>Kode Pelanggan</th>
                            <th>Nama Pelanggan</th>
                            <th>Total Transaksi</th>
                            <th>Total Item</th>
                            <th>Total Qty</th>
                            <th>Total Penjualan</th>
                            <th>Total Dibayar</th>
                            <th>Sisa Pembayaran</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_transaksi = 0;
                        $total_item = 0;
                        $total_qty = 0;
                        $total_penjualan = 0;
                        $total_dibayar = 0;
                        $total_sisa = 0;
                        
                        foreach($data as $row): 
                            $total_transaksi += $row->total_transaksi;
                            $total_item += $row->total_item;
                            $total_qty += $row->total_qty;
                            $total_penjualan += $row->total_penjualan;
                            $total_dibayar += $row->total_dibayar;
                            $total_sisa += $row->sisa_pembayaran;
                        ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td><?= $row->kode_pelanggan ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td class="text-center"><?= number_format($row->total_transaksi) ?></td>
                            <td class="text-center"><?= number_format($row->total_item) ?></td>
                            <td class="text-center"><?= number_format($row->total_qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->total_penjualan) ?></td>
                            <td class="text-right"><?= format_currency($row->total_dibayar) ?></td>
                            <td class="text-right"><?= format_currency($row->sisa_pembayaran) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="thead-dark">
                        <tr>
                            <th colspan="3">TOTAL</th>
                            <th class="text-center"><?= number_format($total_transaksi) ?></th>
                            <th class="text-center"><?= number_format($total_item) ?></th>
                            <th class="text-center"><?= number_format($total_qty, 2) ?></th>
                            <th class="text-right"><?= format_currency($total_penjualan) ?></th>
                            <th class="text-right"><?= format_currency($total_dibayar) ?></th>
                            <th class="text-right"><?= format_currency($total_sisa) ?></th>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'penjualan_barang': ?>
                <table class="table table-bordered table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th width="5%">No</th>
                            <th>Kode Barang</th>
                            <th>Nama Barang</th>
                            <th>Merk</th>
                            <th>Satuan</th>
                            <th>Total Qty</th>
                            <th>Harga Rata-rata</th>
                            <th>Harga Terendah</th>
                            <th>Harga Tertinggi</th>
                            <th>Total Penjualan</th>
                            <th>Jumlah Pelanggan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_qty = 0;
                        $total_penjualan = 0;
                        $total_pelanggan = 0;
                        
                        foreach($data as $row): 
                            $total_qty += $row->total_qty;
                            $total_penjualan += $row->total_penjualan;
                            $total_pelanggan += $row->jumlah_pelanggan;
                        ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td><?= $row->kode_barang ?></td>
                            <td><?= $row->nama_barang ?></td>
                            <td><?= $row->merk ?></td>
                            <td><?= $row->nama_satuan ?></td>
                            <td class="text-center"><?= number_format($row->total_qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_rata_rata) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_terendah) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_tertinggi) ?></td>
                            <td class="text-right"><?= format_currency($row->total_penjualan) ?></td>
                            <td class="text-center"><?= number_format($row->jumlah_pelanggan) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="thead-dark">
                        <tr>
                            <th colspan="5">TOTAL</th>
                            <th class="text-center"><?= number_format($total_qty, 2) ?></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th class="text-right"><?= format_currency($total_penjualan) ?></th>
                            <th class="text-center"><?= number_format($total_pelanggan) ?></th>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'outstanding_invoice': ?>
                <table class="table table-bordered table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th width="5%">No</th>
                            <th>Nomor Faktur</th>
                            <th>Tanggal Faktur</th>
                            <th>Pelanggan</th>
                            <th>Status</th>
                            <th>Total Faktur</th>
                            <th>Jatuh Tempo</th>
                            <th>Umur Invoice (Hari)</th>
                            <th>Status Pembayaran</th>
                            <th>Sisa Pembayaran</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_faktur = 0;
                        $total_sisa = 0;
                        
                        foreach($data as $row): 
                            $total_faktur += $row->total_faktur;
                            $total_sisa += $row->sisa_pembayaran;
                            $umur_invoice = floor((strtotime(date('Y-m-d')) - strtotime($row->tanggal_faktur)) / (60 * 60 * 24));
                        ?>
                        <tr class="<?= $row->status_jatuh_tempo == 'Overdue' ? 'table-danger' : ($row->status_jatuh_tempo == 'Akan Jatuh Tempo' ? 'table-warning' : '') ?>">
                            <td><?= $no++ ?></td>
                            <td><?= $row->nomor_faktur ?></td>
                            <td><?= format_date($row->tanggal_faktur) ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td>
                                <span class="badge badge-<?= $row->status == 'final' ? 'success' : 'warning' ?>">
                                    <?= ucfirst($row->status) ?>
                                </span>
                            </td>
                            <td class="text-right"><?= format_currency($row->total_faktur) ?></td>
                            <td><?= !empty($row->jatuh_tempo) ? format_date($row->jatuh_tempo) : '-' ?></td>
                            <td class="text-center">
                                <span class="badge badge-<?= $umur_invoice > 90 ? 'danger' : ($umur_invoice > 60 ? 'warning' : 'info') ?>">
                                    <?= $umur_invoice ?> hari
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?= $row->status_pembayaran == 'lunas' ? 'success' : ($row->status_pembayaran == 'sebagian' ? 'warning' : 'danger') ?>">
                                    <?= ucfirst(str_replace('_', ' ', $row->status_pembayaran)) ?>
                                </span>
                            </td>
                            <td class="text-right"><strong><?= format_currency($row->sisa_pembayaran) ?></strong></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="thead-dark">
                        <tr>
                            <th colspan="5">TOTAL</th>
                            <th class="text-right"><?= format_currency($total_faktur) ?></th>
                            <th colspan="3"></th>
                            <th class="text-right"><strong><?= format_currency($total_sisa) ?></strong></th>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

        <?php endswitch; ?>
    </div>

<?php else: ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> Tidak ada data yang ditemukan berdasarkan filter yang dipilih.
    </div>
<?php endif; ?>