<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Settlement Kas dan Bank</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement/kas_bank') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Bank</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Kode Bank</th>
                                        <td><?= $bank->kode_bank ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nama Bank</th>
                                        <td><?= $bank->nama_bank ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nomor Rekening</th>
                                        <td><?= $bank->nomor_rekening ?></td>
                                    </tr>
                                    <tr>
                                        <th>Atas Nama</th>
                                        <td><?= $bank->atas_nama ?></td>
                                    </tr>
                                    <tr>
                                        <th>Cabang</th>
                                        <td><?= $bank->cabang ?? '-' ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-info">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Rekonsiliasi</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Tanggal</th>
                                        <td><?= date('d F Y', strtotime($tanggal)) ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jumlah Transaksi Bank</th>
                                        <td><?= count($transaksi_bank) ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jumlah Transaksi Sistem</th>
                                        <td><?= count($transaksi_sistem) ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status</th>
                                        <td>
                                            <?php
                                            $total_masuk_bank = 0;
                                            $total_keluar_bank = 0;
                                            $total_masuk_sistem = 0;
                                            $total_keluar_sistem = 0;
                                            
                                            foreach ($transaksi_bank as $row) {
                                                if ($row->jumlah > 0) {
                                                    $total_masuk_bank += $row->jumlah;
                                                } else {
                                                    $total_keluar_bank += abs($row->jumlah);
                                                }
                                            }
                                            
                                            foreach ($transaksi_sistem as $row) {
                                                $total_masuk_sistem += $row->kas_masuk;
                                                $total_keluar_sistem += $row->kas_keluar;
                                            }
                                            
                                            $selisih_masuk = $total_masuk_bank - $total_masuk_sistem;
                                            $selisih_keluar = $total_keluar_bank - $total_keluar_sistem;
                                            
                                            if ($selisih_masuk == 0 && $selisih_keluar == 0) {
                                                echo '<span class="badge badge-success">Sesuai</span>';
                                            } else {
                                                echo '<span class="badge badge-danger">Tidak Sesuai</span>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Ringkasan Transaksi</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-box bg-info">
                                            <span class="info-box-icon"><i class="fas fa-money-bill-wave"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Total Masuk</span>
                                                <span class="info-box-number">
                                                    Bank: <?= 'Rp ' . number_format($total_masuk_bank, 2, ',', '.') ?><br>
                                                    Sistem: <?= 'Rp ' . number_format($total_masuk_sistem, 2, ',', '.') ?>
                                                </span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    Selisih: <?= 'Rp ' . number_format($selisih_masuk, 2, ',', '.') ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-box bg-danger">
                                            <span class="info-box-icon"><i class="fas fa-money-bill-wave"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Total Keluar</span>
                                                <span class="info-box-number">
                                                    Bank: <?= 'Rp ' . number_format($total_keluar_bank, 2, ',', '.') ?><br>
                                                    Sistem: <?= 'Rp ' . number_format($total_keluar_sistem, 2, ',', '.') ?>
                                                </span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    Selisih: <?= 'Rp ' . number_format($selisih_keluar, 2, ',', '.') ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Transaksi Bank (Rekening Koran)</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Tanggal</th>
                                                <th>Nomor Referensi</th>
                                                <th>Deskripsi</th>
                                                <th>Masuk</th>
                                                <th>Keluar</th>
                                                <th>Saldo Akhir</th>
                                                <th>Status Rekonsiliasi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            if (!empty($transaksi_bank)):
                                                foreach ($transaksi_bank as $row): 
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= date('d-m-Y H:i', strtotime($row->tanggal_transaksi)) ?></td>
                                                <td><?= $row->nomor_referensi ?></td>
                                                <td><?= $row->deskripsi ?></td>
                                                <td class="text-right"><?= $row->jumlah > 0 ? 'Rp ' . number_format($row->jumlah, 2, ',', '.') : '-' ?></td>
                                                <td class="text-right"><?= $row->jumlah < 0 ? 'Rp ' . number_format(abs($row->jumlah), 2, ',', '.') : '-' ?></td>
                                                <td class="text-right"><?= 'Rp ' . number_format($row->saldo_akhir, 2, ',', '.') ?></td>
                                                <td class="text-center">
                                                    <?php if ($row->status_rekonsiliasi == 'sudah'): ?>
                                                        <span class="badge badge-success">Sudah Direkonsiliasi</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-warning">Belum Direkonsiliasi</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php 
                                                endforeach;
                                            else:
                                            ?>
                                            <tr>
                                                <td colspan="8" class="text-center">Tidak ada data transaksi bank</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Transaksi Sistem</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Tanggal</th>
                                                <th>Nomor Referensi</th>
                                                <th>Deskripsi</th>
                                                <th>Jenis Transaksi</th>
                                                <th>Masuk</th>
                                                <th>Keluar</th>
                                                <th>Dibuat Oleh</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            if (!empty($transaksi_sistem)):
                                                foreach ($transaksi_sistem as $row): 
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= date('d-m-Y H:i', strtotime($row->tanggal_transaksi)) ?></td>
                                                <td><?= $row->nomor_referensi ?></td>
                                                <td><?= $row->deskripsi ?></td>
                                                <td><?= $row->jenis_transaksi ?></td>
                                                <td class="text-right"><?= $row->kas_masuk > 0 ? 'Rp ' . number_format($row->kas_masuk, 2, ',', '.') : '-' ?></td>
                                                <td class="text-right"><?= $row->kas_keluar > 0 ? 'Rp ' . number_format($row->kas_keluar, 2, ',', '.') : '-' ?></td>
                                                <td><?= $row->created_by ?? '-' ?></td>
                                            </tr>
                                            <?php 
                                                endforeach;
                                            else:
                                            ?>
                                            <tr>
                                                <td colspan="8" class="text-center">Tidak ada data transaksi sistem</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($selisih_masuk != 0 || $selisih_keluar != 0): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                            <p>Terdapat selisih antara transaksi bank dan transaksi sistem. Silakan lakukan rekonsiliasi untuk memperbaiki data.</p>
                            <a href="<?= site_url('Kas/rekonsiliasi/' . $bank->id_bank . '/' . $tanggal) ?>" class="btn btn-warning"><i class="fas fa-sync-alt"></i> Rekonsiliasi</a>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <h5><i class="icon fas fa-check"></i> Informasi</h5>
                            <p>Transaksi bank dan transaksi sistem telah sesuai.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>