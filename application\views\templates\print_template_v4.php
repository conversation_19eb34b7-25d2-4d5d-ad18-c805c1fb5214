<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'Dokumen Print' ?></title>
    <style>
        /* Reset dan pengaturan dasar */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: #fff;
        }

        /* Pengaturan untuk media cetak */
        @media print {
            body {
                margin: 0;
                padding: 10mm;
                font-size: 11px;
            }
            
            .print-controls {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-print {
                display: none !important;
            }
        }

        /* Header perusahaan */
        .company-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            border: 0.5px solid #ddd;
            background: #fff;
        }

        .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .company-info {
            font-size: 11px;
            color: #666;
            line-height: 1.3;
        }

        .document-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border: 0.5px solid #ccc;
            background: #f9f9f9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Bagian informasi */
        .info-section {
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            margin-bottom: 15px;
            gap: 15px;
        }

        .info-box {
            flex: 1;
            border: 0.5px solid #ddd;
            padding: 12px;
            background: #fff;
        }

        .info-box h4 {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            border-bottom: 0.5px solid #eee;
            padding-bottom: 5px;
        }

        .info-item {
            display: flex;
            margin-bottom: 5px;
            font-size: 11px;
        }

        .info-label {
            width: 100px;
            font-weight: 500;
            color: #555;
        }

        .info-value {
            flex: 1;
            color: #333;
        }

        .info-separator {
            margin: 0 8px;
            color: #999;
        }

        /* Badge status */
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            font-size: 10px;
            font-weight: bold;
            border: 0.5px solid #ddd;
            background: #f5f5f5;
            color: #333;
        }

        /* Container tabel */
        .table-container {
            margin-bottom: 20px;
        }

        .table-title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 10px;
            padding: 8px 12px;
            border: 0.5px solid #ddd;
            background: #f8f8f8;
            color: #333;
        }

        /* Tabel data */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 11px;
        }

        .data-table th {
            background: #f5f5f5;
            border: 0.5px solid #ddd;
            padding: 8px 6px;
            text-align: left;
            font-weight: bold;
            color: #333;
            font-size: 11px;
        }

        .data-table td {
            border: 0.5px solid #ddd;
            padding: 6px;
            vertical-align: top;
            color: #333;
        }

        .data-table tr:nth-child(even) {
            background: #fafafa;
        }

        .data-table tr:hover {
            background: #f0f0f0;
        }

        /* Baris total */
        .total-row {
            font-weight: bold;
            background: #f8f8f8 !important;
        }

        .total-row td {
            border-top: 1px solid #999;
            border-bottom: 1px solid #999;
        }

        /* Perataan teks */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }

        /* Bagian ringkasan */
        .summary-section {
            display: flex;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .summary-left {
            flex: 1;
        }

        .summary-right {
            width: 300px;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }

        .summary-table td {
            border: 0.5px solid #ddd;
            padding: 6px 10px;
            font-size: 11px;
        }

        .summary-table .summary-label {
            background: #f8f8f8;
            font-weight: 500;
            width: 60%;
        }

        .summary-table .summary-value {
            text-align: right;
            font-weight: normal;
        }

        .summary-table .total-final {
            font-weight: bold;
            background: #f0f0f0;
            border-top: 1px solid #999;
        }

        /* Bagian catatan */
        .notes-section {
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .notes-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .notes-content {
            border: 0.5px solid #ddd;
            padding: 12px;
            background: #fafafa;
            font-size: 11px;
            line-height: 1.4;
            color: #555;
            min-height: 60px;
        }

        /* Bagian tanda tangan */
        .signature-section {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            gap: 40px;
        }

        .signature-box {
            flex: 1;
            text-align: center;
            border: 0.5px solid #ddd;
            padding: 15px 10px;
            background: #fff;
        }

        .signature-title {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 50px;
            color: #333;
        }

        .signature-name {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 3px;
            color: #333;
        }

        .signature-position {
            font-size: 10px;
            color: #666;
        }

        /* Footer */
        .print-footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 0.5px solid #ddd;
            text-align: center;
            font-size: 10px;
            color: #666;
        }

        /* Kontrol cetak */
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .btn-print, .btn-close {
            display: inline-block;
            padding: 8px 15px;
            margin: 0 5px;
            background: #f8f8f8;
            border: 0.5px solid #ddd;
            color: #333;
            text-decoration: none;
            font-size: 12px;
            cursor: pointer;
        }

        .btn-print:hover, .btn-close:hover {
            background: #e8e8e8;
        }

        /* Responsif untuk layar */
        @media screen and (max-width: 768px) {
            .info-row {
                flex-direction: column;
            }
            
            .summary-section {
                flex-direction: column;
            }
            
            .summary-right {
                width: 100%;
                margin-top: 15px;
            }
            
            .signature-section {
                flex-direction: column;
                gap: 20px;
            }
        }

        /* Status kosong */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-style: italic;
        }

        /* Kelas utilitas */
        .font-bold { font-weight: bold; }
        .text-small { font-size: 10px; }
        .mb-10 { margin-bottom: 10px; }
        .mt-10 { margin-top: 10px; }
        .border-top { border-top: 0.5px solid #ddd; }
        .border-bottom { border-bottom: 0.5px solid #ddd; }
        .bg-light { background: #f8f8f8; }
        .text-muted { color: #666; }
        
        /* Icon dokumen sederhana */
        .icon-document:before {
            content: "📄";
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <!-- Kontrol Cetak -->
    <div class="print-controls no-print">
        <button class="btn-print" onclick="window.print()">🖨️ Cetak</button>
        <button class="btn-close" onclick="window.close()">❌ Tutup</button>
    </div>

    <!-- Header Perusahaan -->
    <div class="company-header">
        <div class="company-name"><?= isset($company_name) ? $company_name : 'NAMA PERUSAHAAN' ?></div>
        <div class="company-info">
            <?= isset($company_address) ? $company_address : 'Alamat Perusahaan' ?><br>
            Telp: <?= isset($company_phone) ? $company_phone : '(021) 1234567' ?> | 
            Email: <?= isset($company_email) ? $company_email : '<EMAIL>' ?>
        </div>
    </div>

    <!-- Judul Dokumen -->
    <?php if (isset($document_title)): ?>
    <div class="document-title">
        <span class="icon-document"></span><?= $document_title ?>
        <?php if (isset($document_number)): ?>
            <br><small style="font-size: 12px; font-weight: normal;"><?= $document_number ?></small>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Area Konten Utama -->
    <div class="content-area">
        <?= isset($content) ? $content : '' ?>
    </div>

    <!-- Footer -->
    <div class="print-footer">
        <div>Dicetak pada: <?= date('d/m/Y H:i:s') ?></div>
        <?php if (isset($footer_text)): ?>
            <div><?= $footer_text ?></div>
        <?php endif; ?>
    </div>

    <script>
        // Auto print jika diperlukan
        <?php if (isset($auto_print) && $auto_print): ?>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
        <?php endif; ?>

        // Tutup jendela setelah print (opsional)
        <?php if (isset($close_after_print) && $close_after_print): ?>
        window.onafterprint = function() {
            setTimeout(function() {
                window.close();
            }, 1000);
        };
        <?php endif; ?>

        // Shortcut keyboard
        document.addEventListener('keydown', function(e) {
            // Ctrl+P untuk print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape untuk tutup
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>