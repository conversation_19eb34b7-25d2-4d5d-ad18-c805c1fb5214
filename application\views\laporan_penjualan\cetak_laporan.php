<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .header h2 {
            margin: 5px 0 0 0;
            font-size: 14px;
            font-weight: normal;
        }
        
        .filter-info {
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        
        .filter-info h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: bold;
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .filter-item {
            flex: 1;
            min-width: 200px;
        }
        
        .filter-item strong {
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        table th,
        table td {
            border: 1px solid #333;
            padding: 6px 8px;
            text-align: left;
            vertical-align: top;
        }
        
        table th {
            background-color: #333;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-left {
            text-align: left;
        }
        
        .total-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .footer {
            margin-top: 30px;
            text-align: right;
        }
        
        .signature {
            margin-top: 50px;
            text-align: right;
        }
        
        .signature-box {
            display: inline-block;
            text-align: center;
            min-width: 200px;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 60px;
            padding-top: 5px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .badge-success {
            background-color: #28a745;
            color: white;
        }
        
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .badge-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .badge-info {
            background-color: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <?php
    // Helper function untuk format currency
    if (!function_exists('format_currency')) {
        function format_currency($amount) {
            return 'Rp ' . number_format($amount, 0, ',', '.');
        }
    }

    // Helper function untuk format date
    if (!function_exists('format_date')) {
        function format_date($date) {
            return date('d/m/Y', strtotime($date));
        }
    }

    // Tentukan judul laporan
    $judul_laporan = '';
    switch($jenis_laporan) {
        case 'penjualan_periode':
            $judul_laporan = 'Laporan Penjualan Periode';
            break;
        case 'detail_penjualan':
            $judul_laporan = 'Laporan Detail Penjualan';
            break;
        case 'penjualan_pelanggan':
            $judul_laporan = 'Laporan Penjualan per Pelanggan';
            break;
        case 'penjualan_barang':
            $judul_laporan = 'Laporan Penjualan per Barang';
            break;
        case 'outstanding_invoice':
            $judul_laporan = 'Laporan Outstanding Invoice';
            break;
        default:
            $judul_laporan = 'Laporan Penjualan';
    }
    ?>

    <div class="header">
        <h1><?= $judul_laporan ?></h1>
        <h2>Dicetak pada: <?= date('d/m/Y H:i:s') ?></h2>
    </div>

    <div class="filter-info">
        <h3>Informasi Filter</h3>
        <div class="filter-row">
            <?php if (!empty($filter['tanggal_dari']) || !empty($filter['tanggal_sampai'])): ?>
            <div class="filter-item">
                <strong>Periode:</strong> 
                <?= !empty($filter['tanggal_dari']) ? format_date($filter['tanggal_dari']) : 'Awal' ?> 
                s/d 
                <?= !empty($filter['tanggal_sampai']) ? format_date($filter['tanggal_sampai']) : 'Akhir' ?>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($info_pelanggan)): ?>
            <div class="filter-item">
                <strong>Pelanggan:</strong> <?= $info_pelanggan ?>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($info_barang)): ?>
            <div class="filter-item">
                <strong>Barang:</strong> <?= $info_barang ?>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($filter['status'])): ?>
            <div class="filter-item">
                <strong>Status:</strong> <?= ucfirst($filter['status']) ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($laporan && $laporan->num_rows() > 0): ?>
        <?php 
        $data = $laporan->result();
        $total_records = count($data);
        ?>

        <p><strong>Total Record:</strong> <?= number_format($total_records) ?> data</p>

        <?php switch($jenis_laporan): 
            case 'penjualan_periode': ?>
                <table>
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th>Nomor Faktur</th>
                            <th>Tanggal</th>
                            <th>Pelanggan</th>
                            <th>Status</th>
                            <th>Status Pembayaran</th>
                            <th>Total Item</th>
                            <th>Total Qty</th>
                            <th>Subtotal</th>
                            <th>Diskon</th>
                            <th>Pajak</th>
                            <th>Total Faktur</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_item = 0;
                        $total_qty = 0;
                        $total_subtotal = 0;
                        $total_diskon = 0;
                        $total_pajak = 0;
                        $total_faktur = 0;
                        
                        foreach($data as $row): 
                            $total_item += $row->total_item;
                            $total_qty += $row->total_qty;
                            $total_subtotal += $row->subtotal;
                            $total_diskon += $row->diskon;
                            $total_pajak += $row->pajak;
                            $total_faktur += $row->total_faktur;
                        ?>
                        <tr>
                            <td class="text-center"><?= $no++ ?></td>
                            <td><?= $row->nomor_faktur ?></td>
                            <td class="text-center"><?= format_date($row->tanggal_faktur) ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td class="text-center"><?= ucfirst($row->status) ?></td>
                            <td class="text-center"><?= ucfirst(str_replace('_', ' ', $row->status_pembayaran)) ?></td>
                            <td class="text-center"><?= number_format($row->total_item) ?></td>
                            <td class="text-center"><?= number_format($row->total_qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->subtotal) ?></td>
                            <td class="text-right"><?= format_currency($row->diskon) ?></td>
                            <td class="text-right"><?= format_currency($row->pajak) ?></td>
                            <td class="text-right"><?= format_currency($row->total_faktur) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="6" class="text-center"><strong>TOTAL</strong></td>
                            <td class="text-center"><strong><?= number_format($total_item) ?></strong></td>
                            <td class="text-center"><strong><?= number_format($total_qty, 2) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_subtotal) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_diskon) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_pajak) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_faktur) ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'detail_penjualan': ?>
                <table>
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th>Nomor Faktur</th>
                            <th>Tanggal</th>
                            <th>Pelanggan</th>
                            <th>Kode Barang</th>
                            <th>Nama Barang</th>
                            <th>Merk</th>
                            <th>Satuan</th>
                            <th>Qty</th>
                            <th>Harga Satuan</th>
                            <th>Diskon</th>
                            <th>Subtotal</th>
                            <th>Pajak</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_qty = 0;
                        $total_diskon = 0;
                        $total_subtotal = 0;
                        $total_pajak = 0;
                        $total_akhir = 0;
                        
                        foreach($data as $row): 
                            $total_qty += $row->qty;
                            $total_diskon += $row->diskon_nilai;
                            $total_subtotal += $row->subtotal;
                            $total_pajak += $row->pajak_nilai;
                            $total_akhir += $row->total;
                        ?>
                        <tr>
                            <td class="text-center"><?= $no++ ?></td>
                            <td><?= $row->nomor_faktur ?></td>
                            <td class="text-center"><?= format_date($row->tanggal_faktur) ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td><?= $row->kode_barang ?></td>
                            <td><?= $row->nama_barang ?></td>
                            <td><?= $row->merk ?></td>
                            <td class="text-center"><?= $row->nama_satuan ?></td>
                            <td class="text-center"><?= number_format($row->qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_satuan) ?></td>
                            <td class="text-right"><?= format_currency($row->diskon_nilai) ?></td>
                            <td class="text-right"><?= format_currency($row->subtotal) ?></td>
                            <td class="text-right"><?= format_currency($row->pajak_nilai) ?></td>
                            <td class="text-right"><?= format_currency($row->total) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="8" class="text-center"><strong>TOTAL</strong></td>
                            <td class="text-center"><strong><?= number_format($total_qty, 2) ?></strong></td>
                            <td></td>
                            <td class="text-right"><strong><?= format_currency($total_diskon) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_subtotal) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_pajak) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_akhir) ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'penjualan_pelanggan': ?>
                <table>
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th>Kode Pelanggan</th>
                            <th>Nama Pelanggan</th>
                            <th>Total Transaksi</th>
                            <th>Total Item</th>
                            <th>Total Qty</th>
                            <th>Total Penjualan</th>
                            <th>Total Dibayar</th>
                            <th>Sisa Pembayaran</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_transaksi = 0;
                        $total_item = 0;
                        $total_qty = 0;
                        $total_penjualan = 0;
                        $total_dibayar = 0;
                        $total_sisa = 0;
                        
                        foreach($data as $row): 
                            $total_transaksi += $row->total_transaksi;
                            $total_item += $row->total_item;
                            $total_qty += $row->total_qty;
                            $total_penjualan += $row->total_penjualan;
                            $total_dibayar += $row->total_dibayar;
                            $total_sisa += $row->sisa_pembayaran;
                        ?>
                        <tr>
                            <td class="text-center"><?= $no++ ?></td>
                            <td><?= $row->kode_pelanggan ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td class="text-center"><?= number_format($row->total_transaksi) ?></td>
                            <td class="text-center"><?= number_format($row->total_item) ?></td>
                            <td class="text-center"><?= number_format($row->total_qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->total_penjualan) ?></td>
                            <td class="text-right"><?= format_currency($row->total_dibayar) ?></td>
                            <td class="text-right"><?= format_currency($row->sisa_pembayaran) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="3" class="text-center"><strong>TOTAL</strong></td>
                            <td class="text-center"><strong><?= number_format($total_transaksi) ?></strong></td>
                            <td class="text-center"><strong><?= number_format($total_item) ?></strong></td>
                            <td class="text-center"><strong><?= number_format($total_qty, 2) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_penjualan) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_dibayar) ?></strong></td>
                            <td class="text-right"><strong><?= format_currency($total_sisa) ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'penjualan_barang': ?>
                <table>
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th>Kode Barang</th>
                            <th>Nama Barang</th>
                            <th>Merk</th>
                            <th>Satuan</th>
                            <th>Total Qty</th>
                            <th>Harga Rata-rata</th>
                            <th>Harga Terendah</th>
                            <th>Harga Tertinggi</th>
                            <th>Total Penjualan</th>
                            <th>Jumlah Pelanggan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_qty = 0;
                        $total_penjualan = 0;
                        $total_pelanggan = 0;
                        
                        foreach($data as $row): 
                            $total_qty += $row->total_qty;
                            $total_penjualan += $row->total_penjualan;
                            $total_pelanggan += $row->jumlah_pelanggan;
                        ?>
                        <tr>
                            <td class="text-center"><?= $no++ ?></td>
                            <td><?= $row->kode_barang ?></td>
                            <td><?= $row->nama_barang ?></td>
                            <td><?= $row->merk ?></td>
                            <td class="text-center"><?= $row->nama_satuan ?></td>
                            <td class="text-center"><?= number_format($row->total_qty, 2) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_rata_rata) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_terendah) ?></td>
                            <td class="text-right"><?= format_currency($row->harga_tertinggi) ?></td>
                            <td class="text-right"><?= format_currency($row->total_penjualan) ?></td>
                            <td class="text-center"><?= number_format($row->jumlah_pelanggan) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="5" class="text-center"><strong>TOTAL</strong></td>
                            <td class="text-center"><strong><?= number_format($total_qty, 2) ?></strong></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-right"><strong><?= format_currency($total_penjualan) ?></strong></td>
                            <td class="text-center"><strong><?= number_format($total_pelanggan) ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

            <?php case 'outstanding_invoice': ?>
                <table>
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th>Nomor Faktur</th>
                            <th>Tanggal Faktur</th>
                            <th>Pelanggan</th>
                            <th>Status</th>
                            <th>Total Faktur</th>
                            <th>Jatuh Tempo</th>
                            <th>Umur Invoice (Hari)</th>
                            <th>Status Pembayaran</th>
                            <th>Sisa Pembayaran</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $no = 1;
                        $total_faktur = 0;
                        $total_sisa = 0;
                        
                        foreach($data as $row): 
                            $total_faktur += $row->total_faktur;
                            $total_sisa += $row->sisa_pembayaran;
                            $umur_invoice = floor((strtotime(date('Y-m-d')) - strtotime($row->tanggal_faktur)) / (60 * 60 * 24));
                        ?>
                        <tr>
                            <td class="text-center"><?= $no++ ?></td>
                            <td><?= $row->nomor_faktur ?></td>
                            <td class="text-center"><?= format_date($row->tanggal_faktur) ?></td>
                            <td><?= $row->nama_pelanggan ?></td>
                            <td class="text-center"><?= ucfirst($row->status) ?></td>
                            <td class="text-right"><?= format_currency($row->total_faktur) ?></td>
                            <td class="text-center"><?= !empty($row->jatuh_tempo) ? format_date($row->jatuh_tempo) : '-' ?></td>
                            <td class="text-center"><?= $umur_invoice ?> hari</td>
                            <td class="text-center"><?= ucfirst(str_replace('_', ' ', $row->status_pembayaran)) ?></td>
                            <td class="text-right"><?= format_currency($row->sisa_pembayaran) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="5" class="text-center"><strong>TOTAL</strong></td>
                            <td class="text-right"><strong><?= format_currency($total_faktur) ?></strong></td>
                            <td colspan="3"></td>
                            <td class="text-right"><strong><?= format_currency($total_sisa) ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
                <?php break; ?>

        <?php endswitch; ?>

    <?php else: ?>
        <div style="text-align: center; padding: 50px; background-color: #f8f9fa; border: 1px solid #dee2e6;">
            <h3>Tidak ada data yang ditemukan</h3>
            <p>Silakan ubah filter dan coba lagi.</p>
        </div>
    <?php endif; ?>

    <div class="signature">
        <div class="signature-box">
            <div>Mengetahui,</div>
            <div class="signature-line">
                Manager
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>