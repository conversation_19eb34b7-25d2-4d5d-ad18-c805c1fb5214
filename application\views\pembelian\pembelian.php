<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-shopping-cart text-blue"></i> Data Pembelian Supplier</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Tambah Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <?= $this->session->flashdata('pesan') ?>
                        
                        <table id="tbl_pembelian" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Pembelian</th>
                                    <th>Tanggal</th>
                                    <th>Supplier</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>Pembayaran</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Total Harga</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Header -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Pembelian Supplier</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden-true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Simpan</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Batal</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div class="modal fade" id="modal-hapus" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Hapus</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus pembelian ini?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="btn-hapus">Hapus</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="modal_detail" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

<!-- Modal Pembayaran -->
<div class="modal fade" id="modal_pembayaran" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Form Pembayaran</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-pembayaran-body">
                <!-- Form pembayaran akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button><button type="button" id="btnSavePembayaran" onclick="savePembayaran()" class="btn btn-primary">Simpan Pembayaran</button></div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;
    var id_pembelian;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_pembelian").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Pembelian Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('pembelian/ajax_list') ?>",
                "type": "POST"
            },

            //Set column definition initialisation properties.
            "columnDefs": [{
                "targets": [-1], //last column
                "orderable": false, //set not orderable
            }],
        });
    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Tambah Pembelian Supplier'); // set a title to modal header

        // Load form add
        $.ajax({
            url: "<?php echo site_url('pembelian/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show'); // show bootstrap modal after loading form
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Edit Pembelian Supplier'); // set a title to modal header

        // Load form edit
        $.ajax({
            url: "<?php echo site_url('pembelian/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);

                // Load data untuk edit
                $.ajax({
                    url: "<?php echo site_url('pembelian/edit') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_pembelian"]').val(data.nomor_pembelian);
                        $('[name="tanggal_pembelian"]').val(data.tanggal_pembelian);
                        $('[name="id_supplier"]').val(data.id_supplier).trigger('change');
                        $('[name="jenis_pembelian"]').val(data.jenis_pembelian).trigger('change');
                        $('[name="status"]').val(data.status).trigger('change');
                        $('[name="tanggal_jatuh_tempo"]').val(data.tanggal_jatuh_tempo);
                        $('[name="syarat_pembayaran"]').val(data.syarat_pembayaran);
                        $('[name="metode_pembayaran"]').val(data.metode_pembayaran).trigger('change');
                        $('[name="nomor_po_supplier"]').val(data.nomor_po_supplier);
                        $('[name="alamat_pengiriman"]').val(data.alamat_pengiriman);
                        $('[name="keterangan"]').val(data.keterangan);

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat mengambil data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function detail(id) {
        $('#modal_detail').modal('show');
        $('#modal_detail .modal-content').html('<div class="modal-body text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

        $.ajax({
            url: "<?php echo site_url('pembelian/detail_modal') ?>/" + id,
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal_detail .modal-content').html(data);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#modal_detail .modal-content').html('<div class="modal-body text-center text-danger"><i class="fa fa-exclamation-triangle"></i> Error loading detail</div>');
            }
        });
    }

    function save() {
        $('#btnSave').text('menyimpan...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('pembelian/insert') ?>";
        } else {
            url = "<?php echo site_url('pembelian/update') ?>";
        }

        // ajax adding data to database
        var formData = new FormData($('#form')[0]);
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload();
                    
                    Swal.fire({
                        title: 'Data pembelian berhasil disimpan!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                    
                    // Show SweetAlert notification with detailed errors
                    var errorMessages = data.error_string.join('<br>');
                    Swal.fire({
                        title: 'Gagal Menyimpan!',
                        html: 'Terjadi kesalahan:<br>' + errorMessages,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    
                    // Reinisialisasi Select2 setelah validasi gagal
                    $('.select2').select2();
                }
                $('#btnSave').text('Simpan'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('Simpan'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            }
        });
    }

    function hapus(id) {
        id_pembelian = id;
        $('#modal-hapus').modal('show');
    }

    $('#btn-hapus').click(function() {
        $.ajax({
            url: '<?= site_url('pembelian/delete') ?>',
            type: 'POST',
            data: {
                id: id_pembelian
            },
            dataType: 'json',
            success: function(response) {
                $('#modal-hapus').modal('hide');
                if (response.status == 'success') {
                    Swal.fire({
                        title: 'Terhapus!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    table.ajax.reload();
                } else {
                    Swal.fire({
                        title: 'Gagal!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function() {
                $('#modal-hapus').modal('hide');
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menghapus data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    });

    function updateStatus(id, status) {
        var statusText = '';
        switch(status) {
            case 'disetujui': statusText = 'disetujui'; break;
            case 'dipesan': statusText = 'dipesan'; break;
            case 'diterima': statusText = 'diterima'; break;
            case 'selesai': statusText = 'diselesaikan'; break;
            case 'dibatalkan': statusText = 'dibatalkan'; break;
        }

        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin mengubah status pembelian menjadi ' + statusText + '?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Ubah!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: '<?= site_url('pembelian/update_status') ?>',
                    type: 'POST',
                    data: {
                        id: id,
                        status: status
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status == 'success') {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                if (result.value) {
                                    setTimeout(function() {
                                        $('#modal_detail').modal('hide');
                                        table.ajax.reload();
                                    }, 1000);
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan sistem!',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function printPO(id) {
        window.open('<?= site_url('pembelian/print_po') ?>/' + id, '_blank');
    }

    function showPayment(id) {
        id_pembelian = id; // Simpan id pembelian saat modal pembayaran dibuka
        $('#modal_pembayaran').modal('show');
        $('#modal-pembayaran-body').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

        $.ajax({
            url: "<?php echo site_url('pembelian/form_pembayaran') ?>/" + id,
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-pembayaran-body').html(data);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#modal-pembayaran-body').html('<div class="text-center text-danger"><i class="fa fa-exclamation-triangle"></i> Error loading form</div>');
            }
        });
    }

    function savePembayaran() {
        $('#btnSavePembayaran').text('Menyimpan...').attr('disabled', true);

        var formData = new FormData($('#form_pembayaran')[0]);
        $.ajax({
            url: "<?php echo site_url('pembelian/save_pembayaran') ?>",
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(response) {
                if (response.status == 'success') {
                    $('#modal_pembayaran').modal('hide');
                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    table.ajax.reload();
                    if(id_pembelian) {
                        detail(id_pembelian); // Muat ulang detail modal
                        // Buka kembali tab pembayaran
                        setTimeout(function() {
                            $('#detailTabs a[href="#payment"]').tab('show');
                        }, 500); // Beri sedikit waktu agar modal selesai dimuat ulang
                    }
                } else {
                    Swal.fire({
                        title: 'Gagal!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
                $('#btnSavePembayaran').text('Simpan Pembayaran').attr('disabled', false);
            },
            error: function() {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan sistem!',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSavePembayaran').text('Simpan Pembayaran').attr('disabled', false);
            }
        });
    }

    // Fungsi untuk refresh table
    function refreshTable() {
        table.ajax.reload(null, false);
        Swal.fire({
            title: 'Info',
            text: 'Data telah diperbarui',
            icon: 'info',
            confirmButtonText: 'OK',
            timer: 2000,
            timerProgressBar: true
        });
    }

    // Fungsi untuk filter table
    function filterTable() {
        table.ajax.reload();
    }

    // Fungsi untuk reset filter
    function resetFilter() {
        $('#filter_status').val('').trigger('change');
        $('#filter_jenis').val('').trigger('change');
        $('#filter_tanggal_dari').val('');
        $('#filter_tanggal_sampai').val('');
        table.ajax.reload();
    }

    // Fungsi untuk export data
    function exportData(format) {
        var filter_status = $('#filter_status').val();
        var filter_jenis = $('#filter_jenis').val();
        var filter_tanggal_dari = $('#filter_tanggal_dari').val();
        var filter_tanggal_sampai = $('#filter_tanggal_sampai').val();
        
        var url = '<?= site_url('pembelian/export') ?>/' + format + 
                  '?filter_status=' + filter_status +
                  '&filter_jenis=' + filter_jenis +
                  '&filter_tanggal_dari=' + filter_tanggal_dari +
                  '&filter_tanggal_sampai=' + filter_tanggal_sampai;
        
        window.open(url, '_blank');
    }

    // Event handler untuk keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl + N untuk tambah data baru
        if (e.ctrlKey && e.keyCode == 78) {
            e.preventDefault();
            add();
        }
        // F5 untuk refresh
        if (e.keyCode == 116) {
            e.preventDefault();
            refreshTable();
        }
    });

    // Tooltip initialization
    $(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>