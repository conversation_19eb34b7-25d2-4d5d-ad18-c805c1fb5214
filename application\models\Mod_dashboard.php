<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Create BY : Aryo
 * Youtube : Aryo Coding
 */
class Mod_dashboard extends CI_Model
{

	function __construct()
	{
		parent::__construct();
		$this->load->database();
	}
	
	function get_akses_menu($link, $level)
	{

		$this->db->where('a.id_level', $level);
		$this->db->where('b.link', $link);
		$this->db->join('tbl_menu b', 'b.id_menu=a.id_menu');
		return $this->db->get('tbl_akses_menu a');
	}

	function get_akses_submenu($link, $level)
	{

		$this->db->where('a.id_level', $level);
		$this->db->where('b.link', $link);
		$this->db->join('tbl_submenu b', 'b.id_submenu=a.id_submenu');
		return $this->db->get('tbl_akses_submenu a');
	}

	function JmlUser()
	{
		$this->db->from('tbl_user');
		return $this->db->count_all_results();
	}

	function Jmlbarang()
	{
		$this->db->from('barang');
		return $this->db->count_all_results();
	}

	function get_low_stock_items($limit = 5)
	{
		// Pastikan field dan tabel sesuai: barang (id, nama_barang, satuan_id, aktif), satuan (id, nama_satuan)
		$this->db->select('b.id as id_barang, b.nama_barang, SUM(COALESCE(sb.qty_terakhir, 0)) as stok, b.stok_minimum, s.nama_satuan');
		$this->db->from('barang b');
		$this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
		$this->db->join('stok_barang sb', 'b.id = sb.id_barang', 'left');
		$this->db->where('b.aktif', 1);
		$this->db->group_by('b.id, b.nama_barang, b.stok_minimum, s.nama_satuan');
		$this->db->having('SUM(COALESCE(sb.qty_terakhir, 0)) <=', 10);
		$this->db->order_by('stok', 'ASC');
		$this->db->limit($limit);
		return $this->db->get()->result();
	}

	function get_recent_transactions($limit = 5)
	{
		// Mengambil harga dari tabel master barang (harga_beli) bukan dari barang_masuk_detail
		$this->db->select('bm.id as id_penerimaan, bm.tanggal, bm.status, s.nama as nama_supplier,
						  (SELECT SUM(b.harga_beli * d.qty_diterima) 
						   FROM barang_masuk_detail d 
						   JOIN barang b ON d.id_barang = b.id 
						   WHERE d.id_barang_masuk = bm.id) as total');
		$this->db->from('barang_masuk bm');
		$this->db->join('supplier s', 'bm.id_supplier = s.id', 'left');
		$this->db->order_by('bm.tanggal', 'DESC');
		$this->db->limit($limit);
		return $this->db->get()->result();
	}

	function count_suppliers()
	{
		// Pastikan field dan tabel sesuai: supplier (id_supplier, status_aktif)
		$this->db->from('supplier');
		$this->db->where('status_aktif', 1);
		return $this->db->count_all_results();
	}
	
	function total_stock_value()
	{
		// Pastikan field dan tabel sesuai: barang (harga_jual, aktif), stok_barang (qty_terakhir)
		$this->db->select('SUM(COALESCE(sb.qty_terakhir, 0) * b.harga_jual) as total_value');
		$this->db->from('barang b');
		$this->db->join('stok_barang sb', 'b.id = sb.id_barang', 'left');
		$this->db->where('b.aktif', 1);
		$result = $this->db->get()->row();
		return $result ? $result->total_value : 0;
	}

	function get_monthly_transactions()
	{
		$this->db->select("DATE_FORMAT(tanggal, '%Y-%m') as month, COUNT(*) as count");
		$this->db->from('barang_masuk');
		$this->db->where('YEAR(tanggal)', date('Y'));
		$this->db->group_by("DATE_FORMAT(tanggal, '%Y-%m')");
		$this->db->order_by("DATE_FORMAT(tanggal, '%Y-%m')");
		return $this->db->get()->result();
	}

	function get_category_distribution()
	{
		// Karena tabel kategori tidak ada, kita buat distribusi berdasarkan merk atau tipe
		$this->db->select('COALESCE(b.merk, "Tidak Diketahui") as nama_kategori, COUNT(b.id) as count');
		$this->db->from('barang b');
		$this->db->where('b.aktif', 1);
		$this->db->group_by('b.merk');
		$this->db->order_by('count', 'DESC');
		$this->db->limit(5);
		return $this->db->get()->result();
	}

	function get_total_quantity()
	{
		$this->db->select('SUM(COALESCE(sb.qty_terakhir, 0)) as total_qty');
		$this->db->from('barang b');
		$this->db->join('stok_barang sb', 'b.id = sb.id_barang', 'left');
		$result = $this->db->get()->row();
		return $result->total_qty ?? 0;
	}

	public function get_menu() {
		return array();
	}

	public function get_submenu() {
		return array();
	}

	public function get_user() {
		return array();
	}

	public function get_access() {
		return array();
	}
}
