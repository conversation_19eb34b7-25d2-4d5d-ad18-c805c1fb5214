<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Datatables Library for CodeIgniter 3
 * 
 * Library untuk memudahkan penggunaan server-side processing DataTables
 */
class Datatables {
    
    private $CI;
    private $table;
    private $select = array();
    private $joins = array();
    private $columns = array();
    private $where = array();
    private $add_columns = array();
    private $edit_columns = array();
    private $unset_columns = array();
    private $order = array();
    private $group_by = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->database();
    }
    
    /**
     * Select table yang akan digunakan
     * 
     * @param string $table Nama tabel
     * @param string $alias Alias tabel (opsional)
     * @return Datatables
     */
    public function from($table, $alias = NULL) {
        if ($alias) {
            $this->table = $table . ' AS ' . $alias;
        } else {
            $this->table = $table;
        }
        return $this;
    }
    
    /**
     * Select kolom yang akan ditampilkan
     * 
     * @param string $columns Daftar kolom yang dipisahkan koma
     * @return Datatables
     */
    public function select($columns) {
        $cols = explode(',', $columns);
        $this->select = array_merge($this->select, $cols);
        
        // Tambahkan ke daftar columns untuk filtering dan ordering
        foreach ($cols as $col) {
            $col = trim($col);
            if (strpos($col, ' as ') !== false) {
                $col = trim(substr($col, strpos($col, ' as ') + 4));
            }
            if (strpos($col, '.') !== false) {
                $col = substr($col, strpos($col, '.') + 1);
            }
            $this->columns[] = $col;
        }
        
        return $this;
    }
    
    /**
     * Join tabel
     * 
     * @param string $table Nama tabel yang akan di-join
     * @param string $condition Kondisi join
     * @param string $type Tipe join (left, right, inner, dll)
     * @return Datatables
     */
    public function join($table, $condition, $type = '') {
        $this->joins[] = array($table, $condition, $type);
        return $this;
    }
    
    /**
     * Where condition
     * 
     * @param mixed $key Field atau array kondisi
     * @param mixed $value Nilai (jika $key string)
     * @return Datatables
     */
    public function where($key, $value = NULL) {
        if (is_array($key)) {
            $this->where = array_merge($this->where, $key);
        } else {
            $this->where[$key] = $value;
        }
        return $this;
    }
    
    /**
     * Order by
     * 
     * @param string $column Nama kolom
     * @param string $direction Arah sorting (asc/desc)
     * @return Datatables
     */
    public function order_by($column, $direction = 'asc') {
        $this->order[] = array($column, $direction);
        return $this;
    }
    
    /**
     * Group by
     * 
     * @param string $column Nama kolom
     * @return Datatables
     */
    public function group_by($column) {
        $this->group_by[] = $column;
        return $this;
    }
    
    /**
     * Tambah kolom custom
     * 
     * @param string $column Nama kolom
     * @param mixed $content Konten kolom (string atau callback)
     * @return Datatables
     */
    public function add_column($column, $content) {
        $this->add_columns[$column] = $content;
        return $this;
    }
    
    /**
     * Edit kolom
     * 
     * @param string $column Nama kolom
     * @param mixed $content Konten kolom (string atau callback)
     * @return Datatables
     */
    public function edit_column($column, $content) {
        $this->edit_columns[$column] = $content;
        return $this;
    }
    
    /**
     * Hapus kolom dari output
     * 
     * @param string $column Nama kolom
     * @return Datatables
     */
    public function unset_column($column) {
        $this->unset_columns[] = $column;
        return $this;
    }
    
    /**
     * Set kolom-kolom yang akan digunakan untuk pencarian dan pengurutan
     * 
     * @param array $columns Array nama kolom
     * @return Datatables
     */
    public function set_columns($columns) {
        $this->columns = $columns;
        return $this;
    }
    
    /**
     * Generate output untuk DataTables
     * 
     * @return string JSON output
     */
    public function generate() {
        $this->get_paging();
        $this->get_ordering();
        $this->get_filtering();
        
        if (count($this->select) > 0) {
            $this->CI->db->select(implode(',', $this->select));
        }
        
        $this->CI->db->from($this->table);
        
        // Apply joins
        foreach ($this->joins as $join) {
            $this->CI->db->join($join[0], $join[1], $join[2]);
        }
        
        // Apply where conditions
        foreach ($this->where as $key => $value) {
            $this->CI->db->where($key, $value);
        }
        
        // Apply group by
        foreach ($this->group_by as $group) {
            $this->CI->db->group_by($group);
        }
        
        // Get filtered count
        $filtered_query = clone $this->CI->db;
        $filtered_count = $filtered_query->get()->num_rows();
        
        // Get total count
        $this->CI->db->from($this->table);
        foreach ($this->joins as $join) {
            $this->CI->db->join($join[0], $join[1], $join[2]);
        }
        foreach ($this->where as $key => $value) {
            $this->CI->db->where($key, $value);
        }
        foreach ($this->group_by as $group) {
            $this->CI->db->group_by($group);
        }
        $total_count = $this->CI->db->get()->num_rows();
        
        // Apply limit and offset
        if (isset($_POST['start']) && $_POST['length'] != -1) {
            $this->CI->db->limit($_POST['length'], $_POST['start']);
        }
        
        // Get data
        $query = $this->CI->db->get();
        $data = $query->result_array();
        
        // Process data
        $data = $this->process_data($data);
        
        // Prepare output
        $output = array(
            'draw' => intval($_POST['draw']),
            'recordsTotal' => $total_count,
            'recordsFiltered' => $filtered_count,
            'data' => $data
        );
        
        return json_encode($output);
    }
    
    /**
     * Process data untuk output
     * 
     * @param array $data Data dari database
     * @return array Data yang sudah diproses
     */
    private function process_data($data) {
        // Add custom columns
        foreach ($this->add_columns as $column => $content) {
            foreach ($data as $key => $row) {
                $data[$key][$column] = $this->exec_replace($content, $row);
            }
        }
        
        // Edit columns
        foreach ($this->edit_columns as $column => $content) {
            foreach ($data as $key => $row) {
                if (isset($row[$column])) {
                    $data[$key][$column] = $this->exec_replace($content, $row);
                }
            }
        }
        
        // Unset columns
        foreach ($this->unset_columns as $column) {
            foreach ($data as $key => $row) {
                unset($data[$key][$column]);
            }
        }
        
        return $data;
    }
    
    /**
     * Get paging parameters
     */
    private function get_paging() {
        // Nothing to do here, handled in generate()
    }
    
    /**
     * Get ordering parameters
     */
    private function get_ordering() {
        if (isset($_POST['order']) && isset($_POST['order'][0]['column']) && isset($this->columns[$_POST['order'][0]['column']])) {
            $this->CI->db->order_by($this->columns[$_POST['order'][0]['column']], $_POST['order'][0]['dir']);
        } else if (count($this->order) > 0) {
            foreach ($this->order as $order) {
                $this->CI->db->order_by($order[0], $order[1]);
            }
        }
    }
    
    /**
     * Get filtering parameters
     */
    private function get_filtering() {
        if (isset($_POST['search']) && $_POST['search']['value'] != '') {
            $search = $_POST['search']['value'];
            
            // Apply search to all columns
            if (count($this->columns) > 0) {
                $this->CI->db->group_start();
                for ($i = 0; $i < count($this->columns); $i++) {
                    if (isset($_POST['columns'][$i]) && $_POST['columns'][$i]['searchable'] == 'true' && isset($this->columns[$i])) {
                        $this->CI->db->or_like($this->columns[$i], $search);
                    }
                }
                $this->CI->db->group_end();
            }
        }
        
        // Column specific search
        for ($i = 0; $i < count($this->columns); $i++) {
            if (isset($_POST['columns'][$i]) && $_POST['columns'][$i]['searchable'] == 'true' && 
                isset($_POST['columns'][$i]['search']['value']) && $_POST['columns'][$i]['search']['value'] != '' && 
                isset($this->columns[$i])) {
                $this->CI->db->like($this->columns[$i], $_POST['columns'][$i]['search']['value']);
            }
        }
    }
    
    /**
     * Replace placeholder dengan nilai
     * 
     * @param mixed $content Content dengan placeholder
     * @param array $row Data row
     * @return string Content yang sudah direplace
     */
    private function exec_replace($content, $row) {
        if (is_callable($content)) {
            return call_user_func($content, $row);
        }
        
        if (is_string($content)) {
            foreach ($row as $key => $val) {
                $content = str_replace('$' . $key, $val, $content);
            }
        }
        
        return $content;
    }
}