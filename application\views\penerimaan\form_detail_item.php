<?php if (empty($purchased_items)) : ?>
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle"></i> 
        <?php if (isset($error_message)) : ?>
            <?= $error_message ?>
        <?php else : ?>
            Tidak ada item pembelian yang tersedia untuk diterima.
        <?php endif; ?>
        
        <?php if (isset($id_pembelian) && $id_pembelian) : ?>
            <div class="mt-3">
                <p><strong>Kemungkinan penyebab:</strong></p>
                <ul>
                    <li>Pembelian ini belum memiliki item yang ditambahkan. Silakan tambahkan item pada pembelian terlebih dahulu.</li>
                    <li>Semua item dalam pembelian ini sudah diterima sepenuhnya.</li>
                    <li>Status pembelian tidak memungkinkan untuk penerimaan.</li>
                </ul>
                <p><strong>Solusi:</strong></p>
                <ol>
                    <li>Pastikan pembelian memiliki item. Jika belum, tambahkan item pada pembelian terlebih dahulu.</li>
                    <li>Pastikan status pembelian adalah "Dipesan" atau "Disetujui".</li>
                    <li>Jika semua sudah benar, silakan hubungi administrator sistem.</li>
                </ol>
            </div>
        <?php endif; ?>
    </div>
<?php else : ?>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr class="bg-info">
                    <th>Kode Barang</th>
                    <th>Nama Barang</th>
                    <th>Qty Pembelian</th>
                    <th>Qty Sudah Diterima</th>
                    <th>Qty Sisa</th>
                    <th>Gudang</th>
                    <th>Qty Diterima</th>
                    <th>Qty Ditolak</th>
                    <th>Alasan Penolakan</th>
                    <th>Keterangan</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($purchased_items as $index => $item) : ?>
                    <tr>
                        <td>
                            <?= $item->kode_barang ?? 'N/A' ?>
                            <input type="hidden" name="id_pembelian_detail[]" value="<?= $item->id ?>">
                            <input type="hidden" name="id_barang[]" value="<?= $item->id_barang ?>">
                        </td>
                        <td>
                            <?= $item->nama_barang ?? 'N/A' ?> 
                            <?= isset($item->merk) && $item->merk ? '- ' . $item->merk : '' ?> 
                            <?= isset($item->tipe) && $item->tipe ? '- ' . $item->tipe : '' ?>
                        </td>
                        <td class="text-right">
                            <?= number_format(isset($item->qty_pembelian) ? floatval($item->qty_pembelian) : 0, 2) ?> 
                            <?= $item->nama_satuan ?? '' ?>
                            <input type="hidden" name="qty_pembelian[]" value="<?= isset($item->qty_pembelian) ? $item->qty_pembelian : 0 ?>">
                        </td>
                        <td class="text-right">
                            <?= number_format(isset($item->qty_sudah_diterima) ? floatval($item->qty_sudah_diterima) : 0, 2) ?> 
                            <?= $item->nama_satuan ?? '' ?>
                        </td>
                        <td class="text-right">
                            <?= number_format(isset($item->qty_sisa) ? floatval($item->qty_sisa) : 0, 2) ?> 
                            <?= $item->nama_satuan ?? '' ?>
                        </td>
                        <td>
                            <select class="form-control form-control-sm" name="id_gudang[]" required>
                                <option value="">-- Pilih Gudang --</option>
                                <?php if (isset($gudang_list) && is_array($gudang_list)) : ?>
                                    <?php foreach ($gudang_list as $gudang) : ?>
                                        <option value="<?= $gudang->id ?>"><?= $gudang->nama_gudang ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm qty-diterima" name="qty_diterima[]" 
                                min="0" 
                                max="<?= isset($item->qty_sisa) ? floatval($item->qty_sisa) : 0 ?>" 
                                step="1" 
                                value="<?= isset($item->qty_dalam_penerimaan_ini) ? floatval($item->qty_dalam_penerimaan_ini) : 0 ?>" 
                                required>
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm qty-ditolak" name="qty_ditolak[]" 
                                min="0" 
                                max="<?= isset($item->qty_sisa) ? floatval($item->qty_sisa) : 0 ?>" 
                                step="1" 
                                value="0">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm" name="alasan_penolakan[]" placeholder="Alasan jika ditolak">
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm" name="keterangan_item[]" placeholder="Keterangan">
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <script>
        $(document).ready(function() {
            // Validasi qty diterima dan ditolak
            $('.qty-diterima, .qty-ditolak').on('change', function() {
                try {
                    var row = $(this).closest('tr');
                    var qty_sisa_text = row.find('td:eq(4)').text().trim();
                    var qty_sisa = parseFloat(qty_sisa_text.replace(/[^0-9.-]+/g, '')) || 0;
                    var qty_diterima = parseFloat(row.find('.qty-diterima').val()) || 0;
                    var qty_ditolak = parseFloat(row.find('.qty-ditolak').val()) || 0;
                    
                    // Jika total melebihi qty sisa
                    if ((qty_diterima + qty_ditolak) > qty_sisa) {
                        Swal.fire({
                            title: 'Peringatan!',
                            text: 'Total qty diterima dan ditolak tidak boleh melebihi qty sisa',
                            icon: 'warning',
                            confirmButtonText: 'OK'
                        });
                        
                        // Reset nilai
                        if ($(this).hasClass('qty-diterima')) {
                            $(this).val(Math.max(0, qty_sisa - qty_ditolak));
                        } else {
                            $(this).val(Math.max(0, qty_sisa - qty_diterima));
                        }
                    }
                    
                    // Jika qty ditolak > 0, alasan penolakan harus diisi
                    if (qty_ditolak > 0) {
                        row.find('input[name="alasan_penolakan[]"]').attr('required', true);
                    } else {
                        row.find('input[name="alasan_penolakan[]"]').removeAttr('required');
                    }
                } catch (e) {
                    console.error('Error in qty validation:', e);
                }
            });
        });
    </script>
<?php endif; ?>