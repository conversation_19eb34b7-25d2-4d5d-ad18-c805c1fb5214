<form action="#" id="form_item">
    <input type="hidden" name="id_item" id="id_item">
    <input type="hidden" name="id_pengiriman" id="id_pengiriman" value="<?= $id_pengiriman ?>">
    <input type="hidden" name="id_pesanan" id="id_pesanan" value="<?= $id_pesanan ?>">
    
    <div class="form-group">
        <label>Barang dari <PERSON></label>
        <select name="id_barang" id="id_barang" class="form-control select2" style="width: 100%;" onchange="loadWarehouses()">
            <option value="">-- Pilih Barang --</option>
            <?php if (!empty($ordered_items)): ?>
                <?php foreach ($ordered_items as $item): ?>
                    <?php 
                    // Calculate display values
                    $total_shipped = $item->qty_sudah_dikirim;
                    $in_current_shipment = isset($item->qty_dalam_pengiriman_ini) ? $item->qty_dalam_pengiriman_ini : 0;
                    $true_remaining = isset($item->sisa_sebenarnya) ? $item->sisa_sebenarnya : ($item->qty_pesanan - $total_shipped);
                    
                    // Prepare display text
                    $status_text = "";
                    if ($in_current_shipment > 0) {
                        $status_text = " <span class='text-primary'>(Dalam pengiriman ini: {$in_current_shipment})</span>";
                    }
                    ?>
                    <option value="<?= $item->id_barang ?>" 
                            data-qty-ordered="<?= $item->qty_pesanan ?>" 
                            data-qty-shipped="<?= $total_shipped ?>"
                            data-in-current="<?= $in_current_shipment ?>"
                            data-remaining="<?= $true_remaining ?>">
                        <?= $item->kode_barang ?> - <?= $item->nama_barang ?> 
                        (<?= $item->nama_satuan ?>) - 
                        <strong>Sisa: <?= $true_remaining ?> dari <?= $item->qty_pesanan ?></strong>
                        <?= $status_text ?>
                        <?php if ($in_current_shipment > 0): ?>
                            <span class='text-warning'> - PERHATIAN: Sudah ada <?= $in_current_shipment ?> dalam pengiriman ini!</span>
                        <?php endif; ?>
                    </option>
                <?php endforeach; ?>
            <?php else: ?>
                <?php if (empty($id_pesanan)): ?>
                    <option value="" disabled>Silakan pilih pesanan terlebih dahulu</option>
                <?php else: ?>
                    <option value="" disabled>Tidak ada barang yang tersisa untuk dikirim</option>
                <?php endif; ?>
            <?php endif; ?>
        </select>
        <small class="form-text text-muted">Menampilkan barang dengan sisa kuantitas yang belum dikirim dan yang sudah dalam pengiriman ini</small>
    </div>
    
    <div class="form-group">
        <label>Gudang (Stok Tersedia)</label>
        <select name="id_gudang" id="id_gudang" class="form-control select2" style="width: 100%;" onchange="updateMaxQty()">
            <option value="">-- Pilih Gudang --</option>
            <!-- Warehouses will be loaded dynamically -->
        </select>
        <small class="form-text text-muted">Hanya menampilkan gudang dengan stok tersedia</small>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>Stok Tersedia</label>
                <input type="text" class="form-control" id="stok_tersedia" readonly>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Sisa Pesanan</label>
                <input type="text" class="form-control" id="sisa_pesanan" readonly>
            </div>
        </div>
    </div>
    
    <div class="form-group">
        <label>Qty Dikirim</label>
        <input type="number" class="form-control" id="qty_dikirim" name="qty_dikirim" min="1" step="1">
        <small class="form-text text-muted" id="max_qty_info">Maksimal pengiriman: <span id="max_qty">0</span></small>
    </div>
    
    <div class="form-group">
        <label>Berat Satuan (kg)</label>
        <input type="number" class="form-control" id="berat_satuan" name="berat_satuan" min="0.01" step="1">
    </div>
    
    <div class="form-group">
        <label>Keterangan</label>
        <textarea class="form-control" id="keterangan_item" name="keterangan"></textarea>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('.select2').select2();
        
        // Set max value for qty_dikirim when input changes
        $('#qty_dikirim').on('input', function() {
            var max = parseInt($('#max_qty').text());
            var currentVal = parseInt($(this).val());
            
            if (currentVal > max) {
                $(this).val(max);
                // Show warning
                Swal.fire({
                    title: 'Peringatan!',
                    text: 'Quantity tidak boleh melebihi maksimal yang diizinkan (' + max + ')',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
            }
            
            // Also prevent negative values
            if (currentVal < 0) {
                $(this).val(0);
            }
        });
        
        // Additional validation on blur
        $('#qty_dikirim').on('blur', function() {
            var max = parseInt($('#max_qty').text());
            var currentVal = parseInt($(this).val());
            
            if (isNaN(currentVal) || currentVal <= 0) {
                $(this).val(1);
            } else if (currentVal > max) {
                $(this).val(max);
            }
        });
    });
    
    function loadWarehouses() {
        var id_barang = $('#id_barang').val();
        if (!id_barang) {
            $('#id_gudang').html('<option value="">-- Pilih Gudang --</option>');
            $('#id_gudang').trigger('change');
            $('#stok_tersedia').val('');
            $('#sisa_pesanan').val('');
            $('#max_qty').text('0');
            $('#qty_dikirim').val('');
            return;
        }
        
        // Clear previous values
        $('#stok_tersedia').val('');
        $('#sisa_pesanan').val('');
        $('#max_qty').text('0');
        $('#qty_dikirim').val('');
        
        // Get remaining qty and in-current-shipment qty from the selected option
        var remaining = $('#id_barang option:selected').data('remaining');
        var inCurrent = $('#id_barang option:selected').data('in-current') || 0;
        
        // Display the true remaining quantity (sisa pesanan yang belum dikirim sama sekali)
        $('#sisa_pesanan').val(remaining);
        
        // Load berat barang dari master barang
        loadBeratBarang(id_barang);
        
        // Show loading indicator
        $('#id_gudang').html('<option value="">Loading gudang...</option>');
        
        // Load warehouses with stock
        $.ajax({
            url: "<?= site_url('pengiriman/get_warehouses_with_stock') ?>",
            type: "POST",
            data: {
                id_barang: id_barang,
                id_pengiriman: $('#id_pengiriman').val()
            },
            dataType: "JSON",
            success: function(response) {
                var html = '<option value="">-- Pilih Gudang --</option>';
                
                if (response.status && response.data && response.data.length > 0) {
                    $.each(response.data, function(i, warehouse) {
                        // Calculate adjusted stock (accounting for items already in this shipment from this warehouse)
                        var adjustedStock = warehouse.stok_tersedia;
                        if (warehouse.qty_dalam_pengiriman_ini) {
                            // Show both the total stock and the adjusted stock
                            html += '<option value="' + warehouse.id + '" data-stock="' + adjustedStock + '">' + 
                                    warehouse.kode_gudang + ' - ' + warehouse.nama_gudang + 
                                    ' (Stok tersedia: ' + adjustedStock + ', Dalam pengiriman: ' + warehouse.qty_dalam_pengiriman_ini + ')</option>';
                        } else {
                            html += '<option value="' + warehouse.id + '" data-stock="' + adjustedStock + '">' + 
                                    warehouse.kode_gudang + ' - ' + warehouse.nama_gudang + 
                                    ' (Stok: ' + adjustedStock + ')</option>';
                        }
                    });
                } else {
                    html = '<option value="" disabled>Tidak ada gudang dengan stok tersedia</option>';
                    
                    // Show alert to user
                    Swal.fire({
                        title: 'Peringatan!',
                        text: 'Tidak ada stok tersedia untuk barang ini di gudang manapun. Silakan pilih barang lain atau tambahkan stok terlebih dahulu.',
                        icon: 'warning',
                        confirmButtonText: 'OK'
                    });
                }
                
                $('#id_gudang').html(html);
                $('#id_gudang').trigger('change');
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#id_gudang').html('<option value="">-- Error loading gudang --</option>');
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat data gudang: ' + textStatus,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
    
    function updateMaxQty() {
        var id_gudang = $('#id_gudang').val();
        var id_barang = $('#id_barang').val();
        var id_item = $('#id_item').val(); // Get id_item for editing
        
        if (!id_gudang || !id_barang) {
            $('#stok_tersedia').val('');
            $('#max_qty').text('0');
            return;
        }
        
        // Instead of using the data attribute, get real-time data from server
        // This will account for items already in the current shipment
        $.ajax({
            url: "<?= site_url('pengiriman/get_stock_and_remaining') ?>",
            type: "POST",
            data: {
                id_barang: id_barang,
                id_gudang: id_gudang,
                id_pesanan: $('#id_pesanan').val(),
                id_pengiriman: $('#id_pengiriman').val(),
                id_item: id_item // Pass id_item for editing
            },
            dataType: "JSON",
            success: function(response) {
                if (response.status) {
                    // Update displayed values
                    $('#stok_tersedia').val(response.stok_tersedia);
                    $('#sisa_pesanan').val(response.sisa_pesanan);
                    $('#max_qty').text(response.max_qty);
                    
                    // Set max value for qty_dikirim
                    $('#qty_dikirim').attr('max', response.max_qty);
                    
                    // Only set default value if max_qty > 0
                    if (response.max_qty > 0) {
                        $('#qty_dikirim').val(response.max_qty);
                    } else {
                        $('#qty_dikirim').val('');
                        // Show warning if no quantity can be shipped
                        Swal.fire({
                            title: 'Peringatan!',
                            text: 'Tidak ada quantity yang bisa dikirim untuk barang ini dari gudang yang dipilih. Kemungkinan sudah melebihi sisa pesanan atau stok tidak mencukupi.',
                            icon: 'warning',
                            confirmButtonText: 'OK'
                        });
                    }
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil informasi stok: ' + textStatus,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }
    
    function loadBeratBarang(id_barang) {
        if (!id_barang) {
            $('#berat_satuan').val('');
            return;
        }
        
        // Get berat barang dari master barang
        $.ajax({
            url: "<?= site_url('pengiriman/get_berat_barang') ?>",
            type: "POST",
            data: {
                id_barang: id_barang
            },
            dataType: "JSON",
            success: function(response) {
                if (response.status && response.berat) {
                    $('#berat_satuan').val(response.berat);
                } else {
                    // Set default berat jika tidak ada di master
                    $('#berat_satuan').val('0.50');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                // Set default berat jika error
                $('#berat_satuan').val('0.50');
            }
        });
    }
</script>