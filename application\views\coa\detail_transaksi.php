<div class="modal-header">
    <h4 class="modal-title">Detail Transaksi</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
<div class="row">
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Nomor Transaksi</th>
                <td><?= $transaksi->nomor_transaksi ?></td>
            </tr>
            <tr>
                <th>Tanggal</th>
                <td><?= date('d/m/Y', strtotime($transaksi->tanggal_transaksi)) ?></td>
            </tr>
            <tr>
                <th>Tipe</th>
                <td><?= $transaksi->tipe_transaksi ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Status</th>
                <td>
                    <?php
                    switch ($transaksi->status) {
                        case 'draft':
                            echo '<span class="badge badge-warning">Draft</span>';
                            break;
                        case 'posting':
                            echo '<span class="badge badge-success">Posting</span>';
                            break;
                        case 'batal':
                            echo '<span class="badge badge-danger">Batal</span>';
                            break;
                        default:
                            echo '<span class="badge badge-secondary">' . ucfirst($transaksi->status) . '</span>';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <th>Total Debit</th>
                <td>Rp <?= number_format($transaksi->total_debit, 0, ',', '.') ?></td>
            </tr>
            <tr>
                <th>Total Kredit</th>
                <td>Rp <?= number_format($transaksi->total_kredit, 0, ',', '.') ?></td>
            </tr>
        </table>
    </div>
</div>

<?php if (!empty($transaksi->deskripsi)): ?>
<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title">Deskripsi</h3>
            </div>
            <div class="card-body">
                <?= nl2br($transaksi->deskripsi) ?>
                <?php if (!empty($transaksi->referensi)): ?>
                    <p class="mt-2"><strong>Referensi:</strong> <?= $transaksi->referensi ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title">Detail Jurnal</h3>
                <?php if ($transaksi->status == 'draft'): ?>
                <div class="card-tools">
                    <button type="button" class="btn btn-sm btn-primary" onclick="addDetailItem()">
                        <i class="fas fa-plus"></i> Tambah Item
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-sm">
                        <thead>
                            <tr class="bg-info">
                                <th>No</th>
                                <th>Kode Akun</th>
                                <th>Nama Akun</th>
                                <th>Debit</th>
                                <th>Kredit</th>
                                <th>Keterangan</th>
                                <?php if ($transaksi->status == 'draft'): ?>
                                <th>Aksi</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($detail)): ?>
                            <tr>
                                <td colspan="<?= ($transaksi->status == 'draft') ? '7' : '6' ?>" class="text-center">Belum ada detail transaksi</td>
                            </tr>
                            <?php else: ?>
                            <?php $no = 1; $total_debit = 0; $total_kredit = 0; foreach ($detail as $d): ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td><?= $d->kode_akun ?></td>
                                <td><?= $d->nama_akun ?></td>
                                <td class="text-right"><?= number_format($d->debit, 0, ',', '.') ?></td>
                                <td class="text-right"><?= number_format($d->kredit, 0, ',', '.') ?></td>
                                <td><?= $d->keterangan ?: '-' ?></td>
                                <?php if ($transaksi->status == 'draft'): ?>
                                <td>
                                    <button type="button" class="btn btn-xs btn-info" onclick="editDetailItem(<?= $d->id ?>)"><i class="fas fa-edit"></i></button>
                                    <button type="button" class="btn btn-xs btn-danger" onclick="deleteDetailItem(<?= $d->id ?>)"><i class="fas fa-trash"></i></button>
                                </td>
                                <?php endif; ?>
                            </tr>
                            <?php 
                                $total_debit += $d->debit;
                                $total_kredit += $d->kredit;
                            ?>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                        <tfoot>
                            <tr class="bg-light">
                                <th colspan="3" class="text-right">Total:</th>
                                <th class="text-right">Rp <?= number_format($total_debit ?? $transaksi->total_debit, 0, ',', '.') ?></th>
                                <th class="text-right">Rp <?= number_format($total_kredit ?? $transaksi->total_kredit, 0, ',', '.') ?></th>
                                <th colspan="<?= ($transaksi->status == 'draft') ? '2' : '1' ?>"></th>
                            </tr>
                            <?php if ($total_debit != $total_kredit): ?>
                            <tr class="bg-warning">
                                <th colspan="3" class="text-right">Selisih:</th>
                                <th colspan="<?= ($transaksi->status == 'draft') ? '4' : '3' ?>" class="text-center">
                                    Rp <?= number_format(abs($total_debit - $total_kredit), 0, ',', '.') ?>
                                    <small class="text-danger">(Transaksi tidak seimbang)</small>
                                </th>
                            </tr>
                            <?php endif; ?>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title">Informasi Tambahan</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered table-sm">
                            <tr>
                                <th style="width: 150px">Dibuat oleh</th>
                                <td><?= $transaksi->created_by ?: '-' ?></td>
                            </tr>
                            <tr>
                                <th>Tanggal dibuat</th>
                                <td><?= $transaksi->created_at ? date('d/m/Y H:i:s', strtotime($transaksi->created_at)) : '-' ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered table-sm">
                            <tr>
                                <th style="width: 150px">Diupdate oleh</th>
                                <td><?= $transaksi->updated_by ?: '-' ?></td>
                            </tr>
                            <tr>
                                <th>Tanggal update</th>
                                <td><?= $transaksi->updated_at ? date('d/m/Y H:i:s', strtotime($transaksi->updated_at)) : '-' ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Modal Form Detail Item -->
<div class="modal fade" id="modal_form_detail_item" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Detail Transaksi</h4>
                <button type="button" class="close" onclick="closeItemModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="#" id="form_detail_item">
                    <input type="hidden" name="id" id="detail_item_id">
                    <input type="hidden" name="id_transaksi" value="<?= $transaksi->id ?>">
                    
                    <div class="form-group" id="akun_group">
                        <label>Akun</label>
                        <select name="id_akun" id="id_akun" class="form-control select2" style="width: 100%;">
                            <option value="">-- Pilih Akun --</option>
                            <?php foreach ($akun_list as $akun): ?>
                            <option value="<?= $akun->id ?>" data-saldo-normal="<?= $akun->saldo_normal ?>"><?= $akun->kode_akun ?> - <?= $akun->nama_akun ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Debit</label>
                        <input type="number" class="form-control" id="debit" name="debit" min="0" step="1" value="0" onchange="updateKredit()">
                    </div>
                    
                    <div class="form-group">
                        <label>Kredit</label>
                        <input type="number" class="form-control" id="kredit" name="kredit" min="0" step="1" value="0" onchange="updateDebit()">
                    </div>
                    
                    <div class="form-group">
                        <label>Keterangan</label>
                        <textarea class="form-control" id="keterangan_detail_item" name="keterangan" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeItemModal()">Batal</button>
                <button type="button" class="btn btn-primary" id="btnSaveDetailItem" onclick="saveDetailItem()">Simpan</button>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
    <button type="button" class="btn btn-info" onclick="printTransaksi()">Print</button>
    <?php if ($transaksi->status == 'draft'): ?>
    <button type="button" class="btn btn-success" onclick="postingTransaksi()"><i class="fas fa-check"></i> Posting Transaksi</button>
    <?php elseif ($transaksi->status == 'posting'): ?>
    <button type="button" class="btn btn-warning" onclick="batalTransaksi()"><i class="fas fa-times"></i> Batalkan Transaksi</button>
    <?php endif; ?>
</div>

<script>
var save_method_detail_item;
var current_transaksi_id = <?= $transaksi->id ?>;

$(document).ready(function() {
    // Fix for nested modals - improve z-index handling
    $(document).on('show.bs.modal', '.modal', function() {
        var zIndex = 1040 + (10 * $('.modal:visible').length);
        $(this).css('z-index', zIndex);
        setTimeout(function() {
            $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
        }, 0);
    });
    
    // Initialize Select2 in the detail modal
    $('#modal_form_detail_item .select2').select2({
        dropdownParent: $('#modal_form_detail_item'),
        placeholder: "-- Pilih --",
        allowClear: true
    });
    
    // Set default value based on saldo normal when selecting account
    $('#id_akun').on('change', function() {
        var saldoNormal = $(this).find(':selected').data('saldo-normal');
        if (saldoNormal === 'Debit') {
            $('#debit').val(0);
            $('#kredit').val(0);
        } else if (saldoNormal === 'Kredit') {
            $('#debit').val(0);
            $('#kredit').val(0);
        }
    });
});

function addDetailItem() {
    save_method_detail_item = 'add';
    $('#form_detail_item')[0].reset();
    $('#detail_item_id').val('');
    $('#id_akun').val('').trigger('change');
    $('#debit').val(0);
    $('#kredit').val(0);
    $('#keterangan_detail_item').val('');
    $('.modal-title').text('Tambah Detail Transaksi');
    $('#modal_form_detail_item').modal('show');
}

function editDetailItem(id) {
    save_method_detail_item = 'update';
    $('#form_detail_item')[0].reset();
    $('.modal-title').text('Edit Detail Transaksi');
    
    // Get detail data
    $.ajax({
        url: "<?= site_url('coa/get_detail_transaksi') ?>",
        type: "POST",
        data: {id: id},
        dataType: "JSON",
        success: function(data) {
            $('#detail_item_id').val(data.id);
            $('#id_akun').val(data.id_akun).trigger('change');
            $('#debit').val(data.debit);
            $('#kredit').val(data.kredit);
            $('#keterangan_detail_item').val(data.keterangan);
            $('#modal_form_detail_item').modal('show');
        },
        error: function() {
            Swal.fire({
                title: 'Error!',
                text: 'Gagal mengambil data detail transaksi.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function deleteDetailItem(id) {
    Swal.fire({
        title: 'Konfirmasi Hapus',
        text: "Anda yakin ingin menghapus item ini?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('coa/delete_detail_transaksi') ?>",
                type: "POST",
                data: {
                    id: id,
                    id_transaksi: current_transaksi_id
                },
                dataType: "JSON",
                success: function(response) {
                    if (response.status === 'success') {
                        Swal.fire({
                            title: 'Terhapus!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload detail
                            detail_transaksi(current_transaksi_id);
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menghapus data.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function updateDebit() {
    var kredit = parseFloat($('#kredit').val()) || 0;
    if (kredit > 0) {
        $('#debit').val(0);
    }
}

function updateKredit() {
    var debit = parseFloat($('#debit').val()) || 0;
    if (debit > 0) {
        $('#kredit').val(0);
    }
}

function saveDetailItem() {
    $('#btnSaveDetailItem').text('saving...'); 
    $('#btnSaveDetailItem').attr('disabled', true);
    
    var url;
    if (save_method_detail_item == 'add') {
        url = "<?= site_url('coa/add_detail_transaksi') ?>";
    } else {
        url = "<?= site_url('coa/update_detail_transaksi') ?>";
    }
    
    // Validate form
    var id_akun = $('#id_akun').val();
    var debit = parseFloat($('#debit').val()) || 0;
    var kredit = parseFloat($('#kredit').val()) || 0;
    
    if (!id_akun) {
        Swal.fire({
            title: 'Error!',
            text: 'Pilih akun terlebih dahulu.',
            icon: 'error',
            confirmButtonText: 'OK'
        });
        $('#btnSaveDetailItem').text('Simpan');
        $('#btnSaveDetailItem').attr('disabled', false);
        return;
    }
    
    if (debit === 0 && kredit === 0) {
        Swal.fire({
            title: 'Error!',
            text: 'Isi nilai debit atau kredit.',
            icon: 'error',
            confirmButtonText: 'OK'
        });
        $('#btnSaveDetailItem').text('Simpan');
        $('#btnSaveDetailItem').attr('disabled', false);
        return;
    }
    
    // Submit form
    $.ajax({
        url: url,
        type: "POST",
        data: $('#form_detail_item').serialize(),
        dataType: "JSON",
        success: function(response) {
            if (response.status === 'success') {
                $('#modal_form_detail_item').modal('hide');
                Swal.fire({
                    title: 'Berhasil!',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Reload detail
                    detail_transaksi(current_transaksi_id);
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
            $('#btnSaveDetailItem').text('Simpan');
            $('#btnSaveDetailItem').attr('disabled', false);
        },
        error: function() {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menyimpan data.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            $('#btnSaveDetailItem').text('Simpan');
            $('#btnSaveDetailItem').attr('disabled', false);
        }
    });
}

function closeItemModal() {
    $('#modal_form_detail_item').modal('hide');
}

function printTransaksi() {
    window.open('<?= site_url('coa/print_transaksi') ?>/' + current_transaksi_id, '_blank');
}

function postingTransaksi() {
    // Check if debit and credit are balanced
    var total_debit = <?= $total_debit ?? 0 ?>;
    var total_kredit = <?= $total_kredit ?? 0 ?>;
    
    if (total_debit !== total_kredit) {
        Swal.fire({
            title: 'Tidak Seimbang!',
            text: 'Total debit dan kredit harus sama untuk posting transaksi.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return;
    }
    
    // Confirm posting
    Swal.fire({
        title: 'Konfirmasi Posting',
        text: "Transaksi yang sudah diposting tidak dapat diedit atau dihapus. Lanjutkan?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, Posting!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('coa/posting_transaksi') ?>",
                type: "POST",
                data: {
                    id: current_transaksi_id
                },
                dataType: "JSON",
                success: function(response) {
                    if (response.status === 'success') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload detail and table
                            $('#modal_detail').modal('hide');
                            table.ajax.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat posting transaksi.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function batalTransaksi() {
    // Confirm cancellation
    Swal.fire({
        title: 'Konfirmasi Pembatalan',
        text: "Transaksi yang dibatalkan tidak akan dihitung dalam laporan. Lanjutkan?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Batalkan!',
        cancelButtonText: 'Tidak'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?= site_url('coa/batal_transaksi') ?>",
                type: "POST",
                data: {
                    id: current_transaksi_id
                },
                dataType: "JSON",
                success: function(response) {
                    if (response.status === 'success') {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload detail and table
                            $('#modal_detail').modal('hide');
                            table.ajax.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat membatalkan transaksi.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}
</script>