<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Settlement Kas dan Bank</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            Settlement Kas dan Bank digunakan untuk memastikan bahwa saldo kas dan bank di sistem sesuai dengan saldo sebenarnya.
                            Proses ini akan membandingkan data transaksi kas dan bank di sistem dengan rekening koran atau catatan kas untuk mengidentifikasi perbedaan atau kesalahan.
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card card-outline card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Filter Data</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="form-filter" class="form-horizontal">
                                    <div class="form-group row">
                                        <label for="tanggal_awal" class="col-sm-2 col-form-label">Tanggal Transaksi</label>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?= $tanggal_awal ?>">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">s/d</span>
                                                </div>
                                                <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?= $tanggal_akhir ?>">
                                            </div>
                                        </div>
                                        <label for="id_bank" class="col-sm-2 col-form-label">Bank</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="id_bank" name="id_bank">
                                                <option value="">Semua Bank</option>
                                                <?php foreach ($bank as $row): ?>
                                                <option value="<?= $row->id_bank ?>"><?= $row->nama_bank ?> - <?= $row->nomor_rekening ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="offset-sm-2 col-sm-10">
                                            <button type="button" id="btn-filter" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                                            <button type="button" id="btn-reset" class="btn btn-default"><i class="fas fa-undo"></i> Reset</button>
                                            <button type="button" id="btn-export" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="table-settlement" class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Bank</th>
                                        <th>Nomor Rekening</th>
                                        <th>Tanggal</th>
                                        <th>Jumlah Transaksi Bank</th>
                                        <th>Total Masuk Bank</th>
                                        <th>Total Keluar Bank</th>
                                        <th>Jumlah Transaksi Sistem</th>
                                        <th>Total Masuk Sistem</th>
                                        <th>Total Keluar Sistem</th>
                                        <th>Selisih Masuk</th>
                                        <th>Selisih Keluar</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#table-settlement').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?= site_url('settlement/kas_bank_data') ?>",
            "type": "POST",
            "data": function(data) {
                data.tanggal_awal = $('#tanggal_awal').val();
                data.tanggal_akhir = $('#tanggal_akhir').val();
                data.id_bank = $('#id_bank').val();
            },
            "dataSrc": "data"
        },
        "columns": [
            { "data": null, "orderable": false, "render": function(data, type, row, meta) {
                return meta.row + 1;
            }},
            { "data": "nama_bank" },
            { "data": "nomor_rekening" },
            { "data": "tanggal" },
            { "data": "jumlah_transaksi_bank" },
            { "data": "total_masuk_bank", "render": function(data, type, row) {
                return formatRupiah(data);
            }},
            { "data": "total_keluar_bank", "render": function(data, type, row) {
                return formatRupiah(data);
            }},
            { "data": "jumlah_transaksi_sistem" },
            { "data": "total_masuk_sistem", "render": function(data, type, row) {
                return formatRupiah(data);
            }},
            { "data": "total_keluar_sistem", "render": function(data, type, row) {
                return formatRupiah(data);
            }},
            { "data": "selisih_masuk", "render": function(data, type, row) {
                if (data == 0) {
                    return '<span class="text-success">' + formatRupiah(data) + '</span>';
                } else {
                    return '<span class="text-danger">' + formatRupiah(data) + '</span>';
                }
            }},
            { "data": "selisih_keluar", "render": function(data, type, row) {
                if (data == 0) {
                    return '<span class="text-success">' + formatRupiah(data) + '</span>';
                } else {
                    return '<span class="text-danger">' + formatRupiah(data) + '</span>';
                }
            }},
            { "data": null, "orderable": false, "render": function(data, type, row) {
                var html = '<div class="btn-group">';
                html += '<a href="<?= site_url('settlement/kas_bank_detail/') ?>' + row.id_bank + '/' + row.tanggal + '" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>';
                html += '</div>';
                return html;
            }}
        ],
        "order": [[3, 'desc']],
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    $('#btn-filter').click(function() {
        table.ajax.reload();
    });

    $('#btn-reset').click(function() {
        $('#form-filter')[0].reset();
        table.ajax.reload();
    });

    $('#btn-export').click(function() {
        var tanggal_awal = $('#tanggal_awal').val();
        var tanggal_akhir = $('#tanggal_akhir').val();
        var id_bank = $('#id_bank').val();
        
        window.location.href = '<?= site_url('settlement/export/kas_bank') ?>?tanggal_awal=' + tanggal_awal + '&tanggal_akhir=' + tanggal_akhir + '&id_bank=' + id_bank;
    });

    function formatRupiah(angka) {
        var number_string = angka.toString(),
            split = number_string.split('.'),
            sisa = split[0].length % 3,
            rupiah = split[0].substr(0, sisa),
            ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }
});
</script>