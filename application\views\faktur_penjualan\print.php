<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Faktur Penjualan',
    'document_number' => $faktur->nomor_faktur,
    'document_title' => 'FAKTUR PENJUALAN'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Faktur',
        'items' => [
            ['label' => 'No. Faktur', 'value' => '<strong>' . $faktur->nomor_faktur . '</strong>'],
            ['label' => 'Tanggal Faktur', 'value' => format_date_indonesia($faktur->tanggal_faktur)],
            ['label' => 'Jatuh Tempo', 'value' => $faktur->jatuh_tempo ? format_date_indonesia($faktur->jatuh_tempo) : '-'],
            ['label' => 'Status Pembayaran', 'value' => get_status_badge($faktur->status_pembayaran, [
                'belum_bayar' => 'Belum Bayar',
                'sebagian' => 'Bayar Sebagian',
                'lunas' => 'Lunas'
            ])]
        ]
    ],
    [
        'title' => '<span class="icon-user"></span>Informasi Pelanggan',
        'items' => [
            ['label' => 'Nama Pelanggan', 'value' => '<strong>' . $faktur->nama_pelanggan . '</strong> (' . $faktur->kode_pelanggan . ')'],
            ['label' => 'Alamat', 'value' => $faktur->alamat_pelanggan ?: '-'],
            ['label' => 'Telepon', 'value' => $faktur->telepon ?: '-'],
            ['label' => 'Email', 'value' => $faktur->email ?: '-']
        ]
    ]
];

// Add shipping info if available
if (!empty($faktur->nomor_pengiriman)) {
    $info_sections[] = [
        'title' => '<span class="icon-truck"></span>Informasi Pengiriman',
        'items' => [
            ['label' => 'No. Pengiriman', 'value' => $faktur->nomor_pengiriman],
            ['label' => 'Tanggal Kirim', 'value' => format_date_indonesia($faktur->tanggal_pengiriman)],
            ['label' => 'Tanggal Diterima', 'value' => $faktur->tanggal_diterima ? format_date_indonesia($faktur->tanggal_diterima, true) : '-'],
            ['label' => 'Penerima', 'value' => $faktur->penerima ?: '-']
        ]
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '4%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Deskripsi', 'width' => '25%'],
    ['label' => 'Qty', 'width' => '8%', 'align' => 'center'],
    ['label' => 'Harga Satuan', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Subtotal', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Diskon', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Pajak', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Total', 'width' => '12%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$no = 1;

if (!empty($faktur_detail)) {
    foreach ($faktur_detail as $item) {
        $deskripsi = $item->nama_barang;
        if ($item->merk || $item->tipe) {
            $deskripsi .= '<br><small class="color-muted">' . trim($item->merk . ' ' . $item->tipe) . '</small>';
        }
        if ($item->keterangan) {
            $deskripsi .= '<br><small class="color-muted">Ket: ' . $item->keterangan . '</small>';
        }
        
        $diskon_display = '-';
        if ($item->diskon_nilai > 0) {
            $diskon_display = number_format($item->diskon_persen, 1) . '%<br>';
            $diskon_display .= '<small>' . format_currency($item->diskon_nilai) . '</small>';
        }
        
        $pajak_display = '-';
        if ($item->pajak_nilai > 0) {
            $pajak_display = number_format($item->pajak_persen, 1) . '%<br>';
            $pajak_display .= '<small>' . format_currency($item->pajak_nilai) . '</small>';
        }
        
        $table_data[] = [
            $no++,
            $item->kode_barang,
            $deskripsi,
            number_format($item->qty, 0) . ' ' . ($item->nama_satuan ?: ''),
            format_currency($item->harga_satuan),
            format_currency($item->subtotal),
            $diskon_display,
            $pajak_display,
            format_currency($item->total)
        ];
    }
}

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Item Faktur', $table_headers, $table_data);

// Summary section
$content .= '<div class="summary-section">';

// Left side - Payment history and notes
$content .= '<div class="summary-left">';

// Payment history
if (!empty($pembayaran_list)) {
    $payment_headers = [
        ['label' => 'Tanggal', 'width' => '25%'],
        ['label' => 'Metode', 'width' => '25%'],
        ['label' => 'Referensi', 'width' => '25%'],
        ['label' => 'Jumlah', 'width' => '25%', 'align' => 'right']
    ];
    
    $payment_data = [];
    $total_pembayaran = 0;
    
    foreach ($pembayaran_list as $payment) {
        $metode_labels = [
            'tunai' => 'Tunai',
            'transfer' => 'Transfer Bank',
            'cek' => 'Cek/Giro',
            'kartu_kredit' => 'Kartu Kredit',
            'kartu_debit' => 'Kartu Debit'
        ];
        
        $metode = isset($metode_labels[$payment->metode_pembayaran]) 
            ? $metode_labels[$payment->metode_pembayaran] 
            : ucfirst($payment->metode_pembayaran);
            
        $payment_data[] = [
            format_date_indonesia($payment->tanggal_pembayaran),
            $metode,
            $payment->referensi_pembayaran ?: '-',
            format_currency($payment->jumlah_pembayaran)
        ];
        
        $total_pembayaran += $payment->jumlah_pembayaran;
    }
    
    $content .= create_data_table('<span class="icon-money"></span>Riwayat Pembayaran', $payment_headers, $payment_data);
}

// Notes
if (!empty($faktur->keterangan)) {
    $content .= create_notes_section('Catatan', $faktur->keterangan);
}

// Payment info
$content .= create_notes_section('Informasi Pembayaran', 
    'Pembayaran dapat dilakukan melalui transfer bank ke rekening berikut:' . "\n" .
    'Bank BCA - No. Rek: ********** - a.n. PT Toko Elektronik' . "\n" .
    'Bank Mandiri - No. Rek: ********** - a.n. PT Toko Elektronik' . "\n\n" .
    'Harap cantumkan nomor faktur pada keterangan pembayaran.');

$content .= '</div>';

// Right side - Summary
$content .= '<div class="summary-right">';

$summary_items = [
    ['label' => 'Subtotal', 'value' => format_currency($faktur->subtotal)],
    ['label' => 'Diskon', 'value' => format_currency($faktur->diskon)],
    ['label' => 'Pajak', 'value' => format_currency($faktur->pajak)],
    ['label' => 'Total Faktur', 'value' => format_currency($faktur->total_faktur), 'class' => 'total-final']
];

if (!empty($pembayaran_list)) {
    $summary_items[] = ['label' => 'Total Pembayaran', 'value' => format_currency($total_pembayaran)];
    $summary_items[] = ['label' => 'Sisa Tagihan', 'value' => format_currency($faktur->total_faktur - $total_pembayaran), 'class' => 'total-final'];
}

$content .= create_summary_table($summary_items);

$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Diterima oleh',
        'name' => $faktur->nama_pelanggan,
        'position' => 'Pelanggan'
    ],
    [
        'title' => 'Hormat kami',
        'name' => '(............................)',
        'position' => 'Toko Elektronik'
    ]
];

$content .= create_signature_section($signatures);

// Footer note
$content .= '<div class="notes-section mt-20">';
$content .= '<div class="text-center color-muted text-small">';
$content .= 'Faktur ini sah dan diproses oleh komputer.<br>';
$content .= 'Silakan hubungi kami untuk informasi lebih lanjut.<br>';
$content .= 'Terima kasih atas kepercayaan Anda berbelanja di Toko Elektronik.';
$content .= '</div>';
$content .= '</div>';

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>