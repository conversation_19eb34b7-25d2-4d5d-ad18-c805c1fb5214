<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Barang Masuk
 * Mengatur data barang masuk dan detailnya
 */
class Mod_barang_masuk extends CI_Model
{
    var $table = 'barang_masuk';
    var $table_detail = 'barang_masuk_detail';
    var $column_search = array(
        'bm.nomor_penerimaan', 
        'bm.tanggal', 
        's.nama', 
        'bm.jenis', 
        'bm.status', 
        'bm.ref_nomor'
    );
    var $column_order = array(
        'bm.id', 
        'bm.nomor_penerimaan', 
        'bm.tanggal', 
        's.nama', 
        'bm.jenis', 
        'bm.status',
        'bm.total_item',
        'bm.total_qty'
    );
    var $order = array('bm.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            bm.id,
            bm.nomor_penerimaan,
            bm.tanggal,
            bm.id_supplier,
            bm.jenis,
            bm.ref_nomor,
            bm.status,
            bm.total_item,
            bm.total_qty,
            bm.keterangan,
            bm.created_by,
            bm.finalized_by,
            bm.finalized_at,
            s.kode as kode_supplier,
            s.nama as nama_supplier
        ');
        $this->db->from('barang_masuk bm');
        $this->db->join('supplier s', 'bm.id_supplier = s.id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from('barang_masuk bm');
        $this->db->join('supplier s', 'bm.id_supplier = s.id', 'left');
        return $this->db->count_all_results();
    }

    // Insert header barang masuk
    function insert($data)
    {
        $insert = $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // Update header barang masuk
    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    // Get header barang masuk
    function get($id)
    {
        $this->db->select('
            bm.*,
            s.kode as kode_supplier,
            s.nama as nama_supplier
        ');
        $this->db->from('barang_masuk bm');
        $this->db->join('supplier s', 'bm.id_supplier = s.id', 'left');
        $this->db->where('bm.id', $id);
        return $this->db->get()->row();
    }

    // Delete header barang masuk (cascade delete detail)
    function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Generate nomor penerimaan otomatis - Format: BM-YYYYMMDD-XXXX
    function generate_nomor()
    {
        $prefix = 'BM';
        $date = date('Ymd');
        
        $this->db->select('nomor_penerimaan');
        $this->db->from($this->table);
        $this->db->like('nomor_penerimaan', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_penerimaan', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $last_nomor = $query->row()->nomor_penerimaan;
            $last_sequence = (int)substr($last_nomor, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }

        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Cek apakah nomor sudah ada
    function check_nomor_exists($nomor, $id = null)
    {
        $this->db->where('nomor_penerimaan', $nomor);
        if ($id) {
            $this->db->where('id !=', $id);
        }
        $query = $this->db->get($this->table);
        return $query->num_rows() > 0;
    }

    // Get dropdown supplier
    function get_supplier_dropdown()
    {
        $this->db->select('id, kode, nama');
        $this->db->from('supplier');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // Get dropdown barang
    function get_barang_dropdown()
    {
        $this->db->select('id, kode_barang, nama_barang, satuan_id');
        $this->db->from('barang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_barang', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // Get dropdown gudang
    function get_gudang_dropdown()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // Get dropdown satuan
    function get_satuan_dropdown()
    {
        $this->db->select('id, kode_satuan, nama_satuan');
        $this->db->from('satuan');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_satuan', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // ===== DETAIL BARANG MASUK METHODS =====

    // Get detail barang masuk
    function get_detail($id_barang_masuk)
    {
        $this->db->select('
            bmd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            g.kode_gudang,
            g.nama_gudang,
            s.kode_satuan,
            s.nama_satuan
        ');
        $this->db->from('barang_masuk_detail bmd');
        $this->db->join('barang b', 'bmd.id_barang = b.id', 'left');
        $this->db->join('gudang g', 'bmd.id_gudang = g.id', 'left');
        $this->db->join('satuan s', 'bmd.id_satuan = s.id', 'left');
        $this->db->where('bmd.id_barang_masuk', $id_barang_masuk);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Insert detail barang masuk
    function insert_detail($data)
    {
        return $this->db->insert($this->table_detail, $data);
    }

    // Update detail barang masuk
    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    // Delete detail barang masuk
    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    // Get single detail
    function get_detail_by_id($id)
    {
        $this->db->select('
            bmd.*,
            b.kode_barang,
            b.nama_barang,
            b.satuan_id as default_satuan_id
        ');
        $this->db->from('barang_masuk_detail bmd');
        $this->db->join('barang b', 'bmd.id_barang = b.id', 'left');
        $this->db->where('bmd.id', $id);
        return $this->db->get()->row();
    }

    // Finalisasi barang masuk
    function finalize_barang_masuk($id, $user_final)
    {
        // Ambil data barang masuk sebelum update
        $barang_masuk = $this->get($id);

        $data = array(
            'status' => 'final',
            'finalized_by' => $user_final,
            'finalized_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id);
        $this->db->where('status', 'draft'); // Hanya bisa finalisasi jika masih draft
        $this->db->update($this->table, $data);
        
        $affected_rows = $this->db->affected_rows();

        if ($affected_rows > 0 && $barang_masuk->jenis == 'pembelian' && !empty($barang_masuk->keterangan)) {
            // Cek apakah keterangan mengandung nomor penerimaan pembelian
            if (preg_match('/TPB-\d{8}-\d{4}/', $barang_masuk->keterangan, $matches)) {
                $nomor_penerimaan_pembelian = $matches[0];
                $this->load->model('Mod_penerimaan_pembelian');
                $this->Mod_penerimaan_pembelian->update_status_by_nomor_penerimaan($nomor_penerimaan_pembelian, 'selesai');
            }
        }

        return $affected_rows > 0;
    }

    // Get barang detail untuk form
    function get_barang_detail($id_barang)
    {
        $this->db->select('id, kode_barang, nama_barang, merk, tipe, satuan_id');
        $this->db->from('barang');
        $this->db->where('id', $id_barang);
        $this->db->where('aktif', 1);
        return $this->db->get()->row();
    }
}
