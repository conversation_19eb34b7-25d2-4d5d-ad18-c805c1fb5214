<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Chart of Accounts (COA)
 * Mengatur data akun dan transaksi akuntansi
 */
class Mod_coa extends CI_Model
{
    // Tabel untuk kategori akun
    var $table_kategori = 'coa_kategori';
    var $column_search_kategori = array('kode', 'nama', 'tipe', 'deskripsi');
    var $column_order_kategori = array('id', 'kode', 'nama', 'tipe', 'urutan', 'aktif');
    var $order_kategori = array('urutan' => 'asc');

    // Tabel untuk akun
    var $table_akun = 'coa_akun';
    var $column_search_akun = array('a.kode_akun', 'a.nama_akun', 'k.nama', 'a.deskripsi');
    var $column_order_akun = array('a.id', 'a.kode_akun', 'a.nama_akun', 'k.nama', 'a.level', 'a.saldo_normal', 'a.saldo_awal', 'a.aktif');
    var $order_akun = array('a.kode_akun' => 'asc');

    // Tabel untuk transaksi
    var $table_transaksi = 'coa_transaksi';
    var $column_search_transaksi = array('nomor_transaksi', 'tanggal_transaksi', 'tipe_transaksi', 'deskripsi', 'referensi');
    var $column_order_transaksi = array('id', 'nomor_transaksi', 'tanggal_transaksi', 'tipe_transaksi', 'total_debit', 'total_kredit', 'status');
    var $order_transaksi = array('tanggal_transaksi' => 'desc', 'id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /* ========================= KATEGORI AKUN ========================= */
    
    private function _get_datatables_query_kategori()
    {
        $this->db->from($this->table_kategori);

        $i = 0;
        foreach ($this->column_search_kategori as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search_kategori) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_kategori[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_kategori)) {
            $order = $this->order_kategori;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_kategori()
    {
        $this->_get_datatables_query_kategori();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_kategori()
    {
        $this->_get_datatables_query_kategori();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_kategori()
    {
        $this->db->from($this->table_kategori);
        return $this->db->count_all_results();
    }

    public function get_kategori_by_id($id)
    {
        $this->db->from($this->table_kategori);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_all_kategori()
    {
        $this->db->from($this->table_kategori);
        $this->db->where('aktif', 1);
        $this->db->order_by('urutan', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    /* ========================= AKUN ========================= */
    
    private function _get_datatables_query_akun()
    {
        $this->db->select('a.*, k.nama as nama_kategori, k.tipe as tipe_kategori, p.nama_akun as nama_parent');
        $this->db->from($this->table_akun . ' a');
        $this->db->join($this->table_kategori . ' k', 'a.id_kategori = k.id', 'left');
        $this->db->join($this->table_akun . ' p', 'a.id_parent = p.id', 'left');

        $i = 0;
        foreach ($this->column_search_akun as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search_akun) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_akun[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_akun)) {
            $order = $this->order_akun;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_akun()
    {
        $this->_get_datatables_query_akun();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_akun()
    {
        $this->_get_datatables_query_akun();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_akun()
    {
        $this->db->from($this->table_akun);
        return $this->db->count_all_results();
    }

    public function get_akun_by_id($id)
    {
        $this->db->select('a.*, k.nama as nama_kategori, k.tipe as tipe_kategori, p.nama_akun as nama_parent');
        $this->db->from($this->table_akun . ' a');
        $this->db->join($this->table_kategori . ' k', 'a.id_kategori = k.id', 'left');
        $this->db->join($this->table_akun . ' p', 'a.id_parent = p.id', 'left');
        $this->db->where('a.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_akun_by_kode($kode)
    {
        $this->db->from($this->table_akun);
        $this->db->where('kode_akun', $kode);
        $this->db->where('aktif', 1);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_all_akun()
    {
        $this->db->select('a.*, k.nama as nama_kategori, k.tipe as tipe_kategori');
        $this->db->from($this->table_akun . ' a');
        $this->db->join($this->table_kategori . ' k', 'a.id_kategori = k.id', 'left');
        $this->db->where('a.aktif', 1);
        $this->db->order_by('a.kode_akun', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_akun_for_dropdown()
    {
        $this->db->select('a.id, a.kode_akun, a.nama_akun, a.level, a.saldo_normal, a.dapat_diinput');
        $this->db->from($this->table_akun . ' a');
        $this->db->where('a.aktif', 1);
        $this->db->where('a.dapat_diinput', 1);
        $this->db->order_by('a.kode_akun', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_parent_akun($id_kategori, $level = 0)
    {
        $this->db->select('id, kode_akun, nama_akun, level');
        $this->db->from($this->table_akun);
        $this->db->where('id_kategori', $id_kategori);
        $this->db->where('level', $level);
        $this->db->where('aktif', 1);
        $this->db->order_by('kode_akun', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function generate_kode_akun($id_kategori, $level, $id_parent = null)
    {
        // Dapatkan kode kategori
        $kategori = $this->get_kategori_by_id($id_kategori);
        $prefix = $kategori->kode;
        
        if ($level == 0) {
            return $prefix . '-0000';
        }
        
        // Jika ada parent, dapatkan kode parent
        if ($id_parent) {
            $parent = $this->get_akun_by_id($id_parent);
            $parent_code = $parent->kode_akun;
            $parts = explode('-', $parent_code);
            $prefix = $parts[0];
        }
        
        // Cari kode terakhir dengan prefix yang sama dan level yang sama
        $this->db->select('kode_akun');
        $this->db->from($this->table_akun);
        $this->db->where('id_kategori', $id_kategori);
        $this->db->where('level', $level);
        if ($id_parent) {
            $this->db->where('id_parent', $id_parent);
        }
        $this->db->order_by('kode_akun', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_code = $query->row()->kode_akun;
            $parts = explode('-', $last_code);
            $last_number = (int)$parts[1];
            $new_number = $last_number + 100;
        } else {
            // Jika belum ada kode dengan level ini
            if ($level == 1) {
                $new_number = 1000;
            } else if ($level == 2) {
                $new_number = 1100;
            } else {
                $new_number = 1101;
            }
        }
        
        return $prefix . '-' . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    /* ========================= TRANSAKSI ========================= */
    
    private function _get_datatables_query_transaksi()
    {
        $this->db->from($this->table_transaksi);

        $i = 0;
        foreach ($this->column_search_transaksi as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search_transaksi) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_transaksi[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_transaksi)) {
            $order = $this->order_transaksi;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_transaksi()
    {
        $this->_get_datatables_query_transaksi();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_transaksi()
    {
        $this->_get_datatables_query_transaksi();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_transaksi()
    {
        $this->db->from($this->table_transaksi);
        return $this->db->count_all_results();
    }

    public function get_transaksi_by_id($id)
    {
        $this->db->from($this->table_transaksi);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_transaksi_detail($id_transaksi)
    {
        $this->db->select('td.*, a.kode_akun, a.nama_akun, a.saldo_normal');
        $this->db->from('coa_transaksi_detail td');
        $this->db->join($this->table_akun . ' a', 'td.id_akun = a.id', 'left');
        $this->db->where('td.id_transaksi', $id_transaksi);
        $this->db->order_by('td.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    // Generate nomor transaksi - Format: JRN-YYYYMMDD-XXXX (Jurnal)
    public function generate_nomor_transaksi($tipe)
    {
        $prefix = '';
        switch ($tipe) {
            case 'Jurnal Umum':
                $prefix = 'JRN';
                break;
            case 'Penerimaan Kas':
                $prefix = 'KMS';
                break;
            case 'Pengeluaran Kas':
                $prefix = 'KKL';
                break;
            case 'Penyesuaian':
                $prefix = 'PSN';
                break;
            case 'Penutupan':
                $prefix = 'PTN';
                break;
            default:
                $prefix = 'JRN';
        }
        
        $date = date('Ymd');
        
        $this->db->select('nomor_transaksi');
        $this->db->from($this->table_transaksi);
        $this->db->like('nomor_transaksi', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_transaksi', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_transaksi;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    /* ========================= CRUD OPERATIONS ========================= */
    
    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($table, $id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($table, $data);
        return $this->db->affected_rows();
    }

    public function delete($table, $id)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }

    public function save_transaksi_detail($data)
    {
        $this->db->insert('coa_transaksi_detail', $data);
        return $this->db->insert_id();
    }

    public function update_transaksi_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update('coa_transaksi_detail', $data);
        return $this->db->affected_rows();
    }

    public function delete_transaksi_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete('coa_transaksi_detail');
        return $this->db->affected_rows();
    }

    public function delete_all_transaksi_detail($id_transaksi)
    {
        $this->db->where('id_transaksi', $id_transaksi);
        $this->db->delete('coa_transaksi_detail');
        return $this->db->affected_rows();
    }

    public function update_total_transaksi($id_transaksi)
    {
        $this->db->select('SUM(debit) as total_debit, SUM(kredit) as total_kredit');
        $this->db->from('coa_transaksi_detail');
        $this->db->where('id_transaksi', $id_transaksi);
        $query = $this->db->get();
        $result = $query->row();
        
        $data = array(
            'total_debit' => $result->total_debit ? $result->total_debit : 0,
            'total_kredit' => $result->total_kredit ? $result->total_kredit : 0,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id_transaksi);
        $this->db->update($this->table_transaksi, $data);
        return $this->db->affected_rows();
    }

    /* ========================= LAPORAN ========================= */
    
    public function get_buku_besar($id_akun, $tanggal_awal, $tanggal_akhir)
    {
        $this->db->select('t.id, t.nomor_transaksi, t.tanggal_transaksi, t.tipe_transaksi, t.deskripsi, t.referensi, td.debit, td.kredit, td.keterangan');
        $this->db->from('coa_transaksi_detail td');
        $this->db->join('coa_transaksi t', 'td.id_transaksi = t.id', 'left');
        $this->db->where('td.id_akun', $id_akun);
        $this->db->where('t.status', 'posting');
        $this->db->where('t.tanggal_transaksi >=', $tanggal_awal);
        $this->db->where('t.tanggal_transaksi <=', $tanggal_akhir);
        $this->db->order_by('t.tanggal_transaksi', 'asc');
        $this->db->order_by('t.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_saldo_awal($id_akun, $tanggal)
    {
        // Ambil saldo awal dari tabel akun
        $this->db->select('a.saldo_awal, a.tanggal_saldo_awal, a.saldo_normal');
        $this->db->from($this->table_akun . ' a');
        $this->db->where('a.id', $id_akun);
        $akun = $this->db->get()->row();
        
        $saldo_awal = 0;
        
        // Jika ada tanggal saldo awal dan tanggal tersebut sebelum tanggal yang diminta
        if ($akun && $akun->tanggal_saldo_awal && $akun->tanggal_saldo_awal <= $tanggal) {
            $saldo_awal = $akun->saldo_awal;
            
            // Hitung transaksi antara tanggal saldo awal dan tanggal yang diminta
            $this->db->select('SUM(td.debit) as total_debit, SUM(td.kredit) as total_kredit');
            $this->db->from('coa_transaksi_detail td');
            $this->db->join('coa_transaksi t', 'td.id_transaksi = t.id', 'left');
            $this->db->where('td.id_akun', $id_akun);
            $this->db->where('t.status', 'posting');
            $this->db->where('t.tanggal_transaksi >', $akun->tanggal_saldo_awal);
            $this->db->where('t.tanggal_transaksi <', $tanggal);
            $transaksi = $this->db->get()->row();
            
            if ($transaksi) {
                if ($akun->saldo_normal == 'Debit') {
                    $saldo_awal = $saldo_awal + $transaksi->total_debit - $transaksi->total_kredit;
                } else {
                    $saldo_awal = $saldo_awal + $transaksi->total_kredit - $transaksi->total_debit;
                }
            }
        } else {
            // Jika tidak ada tanggal saldo awal, hitung semua transaksi sebelum tanggal yang diminta
            $this->db->select('SUM(td.debit) as total_debit, SUM(td.kredit) as total_kredit');
            $this->db->from('coa_transaksi_detail td');
            $this->db->join('coa_transaksi t', 'td.id_transaksi = t.id', 'left');
            $this->db->where('td.id_akun', $id_akun);
            $this->db->where('t.status', 'posting');
            $this->db->where('t.tanggal_transaksi <', $tanggal);
            $transaksi = $this->db->get()->row();
            
            if ($transaksi) {
                if ($akun->saldo_normal == 'Debit') {
                    $saldo_awal = $transaksi->total_debit - $transaksi->total_kredit;
                } else {
                    $saldo_awal = $transaksi->total_kredit - $transaksi->total_debit;
                }
            }
        }
        
        return $saldo_awal;
    }

    public function get_neraca_saldo($tanggal)
    {
        $this->db->select('a.id, a.kode_akun, a.nama_akun, a.saldo_normal, k.tipe as tipe_kategori');
        $this->db->from($this->table_akun . ' a');
        $this->db->join($this->table_kategori . ' k', 'a.id_kategori = k.id', 'left');
        $this->db->where('a.aktif', 1);
        $this->db->where('a.dapat_diinput', 1);
        $this->db->order_by('a.kode_akun', 'asc');
        $akun_list = $this->db->get()->result();
        
        $result = array();
        
        foreach ($akun_list as $akun) {
            $saldo = $this->get_saldo_awal($akun->id, $tanggal);
            
            if ($saldo != 0) {
                $debit = 0;
                $kredit = 0;
                
                if ($akun->saldo_normal == 'Debit') {
                    $debit = $saldo > 0 ? $saldo : 0;
                    $kredit = $saldo < 0 ? abs($saldo) : 0;
                } else {
                    $kredit = $saldo > 0 ? $saldo : 0;
                    $debit = $saldo < 0 ? abs($saldo) : 0;
                }
                
                $result[] = array(
                    'id' => $akun->id,
                    'kode_akun' => $akun->kode_akun,
                    'nama_akun' => $akun->nama_akun,
                    'tipe_kategori' => $akun->tipe_kategori,
                    'saldo_normal' => $akun->saldo_normal,
                    'debit' => $debit,
                    'kredit' => $kredit
                );
            }
        }
        
        return $result;
    }

    public function get_laba_rugi($tanggal_awal, $tanggal_akhir)
    {
        // Ambil semua akun pendapatan dan beban
        $this->db->select('a.id, a.kode_akun, a.nama_akun, a.saldo_normal, k.tipe as tipe_kategori');
        $this->db->from($this->table_akun . ' a');
        $this->db->join($this->table_kategori . ' k', 'a.id_kategori = k.id', 'left');
        $this->db->where('a.aktif', 1);
        $this->db->where('a.dapat_diinput', 1);
        $this->db->where_in('k.tipe', array('Pendapatan', 'Beban'));
        $this->db->order_by('a.kode_akun', 'asc');
        $akun_list = $this->db->get()->result();
        
        $result = array(
            'pendapatan' => array(),
            'beban' => array(),
            'total_pendapatan' => 0,
            'total_beban' => 0,
            'laba_rugi' => 0
        );
        
        foreach ($akun_list as $akun) {
            // Hitung transaksi dalam periode
            $this->db->select('SUM(td.debit) as total_debit, SUM(td.kredit) as total_kredit');
            $this->db->from('coa_transaksi_detail td');
            $this->db->join('coa_transaksi t', 'td.id_transaksi = t.id', 'left');
            $this->db->where('td.id_akun', $akun->id);
            $this->db->where('t.status', 'posting');
            $this->db->where('t.tanggal_transaksi >=', $tanggal_awal);
            $this->db->where('t.tanggal_transaksi <=', $tanggal_akhir);
            $transaksi = $this->db->get()->row();
            
            $saldo = 0;
            
            if ($transaksi) {
                if ($akun->saldo_normal == 'Debit') {
                    $saldo = $transaksi->total_debit - $transaksi->total_kredit;
                } else {
                    $saldo = $transaksi->total_kredit - $transaksi->total_debit;
                }
            }
            
            if ($saldo != 0) {
                $item = array(
                    'id' => $akun->id,
                    'kode_akun' => $akun->kode_akun,
                    'nama_akun' => $akun->nama_akun,
                    'saldo' => $saldo
                );
                
                if ($akun->tipe_kategori == 'Pendapatan') {
                    $result['pendapatan'][] = $item;
                    $result['total_pendapatan'] += $saldo;
                } else {
                    $result['beban'][] = $item;
                    $result['total_beban'] += $saldo;
                }
            }
        }
        
        $result['laba_rugi'] = $result['total_pendapatan'] - $result['total_beban'];
        
        return $result;
    }

    public function get_neraca($tanggal)
    {
        // Ambil semua akun aset, liabilitas, dan ekuitas
        $this->db->select('a.id, a.kode_akun, a.nama_akun, a.saldo_normal, k.tipe as tipe_kategori');
        $this->db->from($this->table_akun . ' a');
        $this->db->join($this->table_kategori . ' k', 'a.id_kategori = k.id', 'left');
        $this->db->where('a.aktif', 1);
        $this->db->where('a.dapat_diinput', 1);
        $this->db->where_in('k.tipe', array('Aset', 'Liabilitas', 'Ekuitas'));
        $this->db->order_by('a.kode_akun', 'asc');
        $akun_list = $this->db->get()->result();

        $result = array(
            'aset' => array(),
            'liabilitas' => array(),
            'ekuitas' => array(),
            'total_aset' => 0,
            'total_liabilitas' => 0,
            'total_ekuitas' => 0
        );

        foreach ($akun_list as $akun) {
            $saldo = $this->get_saldo_awal($akun->id, $tanggal);

            if ($saldo != 0) {
                $item = array(
                    'id' => $akun->id,
                    'kode_akun' => $akun->kode_akun,
                    'nama_akun' => $akun->nama_akun,
                    'saldo' => $saldo
                );

                if ($akun->tipe_kategori == 'Aset') {
                    $result['aset'][] = $item;
                    $result['total_aset'] += $saldo;
                } else if ($akun->tipe_kategori == 'Liabilitas') {
                    $result['liabilitas'][] = $item;
                    $result['total_liabilitas'] += $saldo;
                } else {
                    $result['ekuitas'][] = $item;
                    $result['total_ekuitas'] += $saldo;
                }
            }
        }

        return $result;
    }

    /* ========================= AUTO JOURNAL INTEGRATION ========================= */

    /**
     * Create automatic journal entry
     * @param array $data Journal data
     * @return int|false Journal ID or false on failure
     */
    public function create_auto_journal($data)
    {
        // Validate required fields
        if (!isset($data['tanggal_transaksi']) || !isset($data['deskripsi']) || !isset($data['entries'])) {
            return false;
        }

        // Start transaction
        $this->db->trans_start();

        // Generate nomor transaksi
        $nomor_transaksi = $this->generate_nomor_transaksi('Jurnal Umum');

        // Create journal header
        $journal_data = array(
            'nomor_transaksi' => $nomor_transaksi,
            'tanggal_transaksi' => $data['tanggal_transaksi'],
            'tipe_transaksi' => 'Jurnal Umum',
            'deskripsi' => $data['deskripsi'],
            'referensi' => isset($data['referensi']) ? $data['referensi'] : null,
            'status' => 'posting', // Auto posting for system generated journals
            'created_by' => 'SYSTEM',
            'created_at' => date('Y-m-d H:i:s')
        );

        $journal_id = $this->insert('coa_transaksi', $journal_data);

        if (!$journal_id) {
            $this->db->trans_rollback();
            return false;
        }

        // Create journal entries
        $total_debit = 0;
        $total_kredit = 0;

        foreach ($data['entries'] as $entry) {
            // Get account info
            $akun = $this->get_akun_by_kode($entry['kode_akun']);
            if (!$akun) {
                $this->db->trans_rollback();
                return false;
            }

            $detail_data = array(
                'id_transaksi' => $journal_id,
                'id_akun' => $akun->id,
                'debit' => isset($entry['debit']) ? $entry['debit'] : 0,
                'kredit' => isset($entry['kredit']) ? $entry['kredit'] : 0,
                'keterangan' => isset($entry['keterangan']) ? $entry['keterangan'] : ''
            );

            $this->db->insert('coa_transaksi_detail', $detail_data);

            $total_debit += $detail_data['debit'];
            $total_kredit += $detail_data['kredit'];
        }

        // Update journal totals
        $this->db->where('id', $journal_id);
        $this->db->update('coa_transaksi', array(
            'total_debit' => $total_debit,
            'total_kredit' => $total_kredit
        ));

        // Complete transaction
        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            return false;
        }

        return $journal_id;
    }



    /**
     * Create journal for sales transaction
     * @param array $sales_data Sales transaction data
     * @return int|false Journal ID or false on failure
     */
    public function create_sales_journal($sales_data)
    {
        $entries = array();

        // Debit: Piutang Usaha or Kas (depending on payment method)
        if ($sales_data['payment_method'] == 'tunai') {
            $entries[] = array(
                'kode_akun' => '1-1101', // Kas
                'debit' => $sales_data['total_amount'],
                'keterangan' => 'Penjualan tunai - ' . $sales_data['nomor_transaksi']
            );
        } else {
            $entries[] = array(
                'kode_akun' => '1-1200', // Piutang Usaha
                'debit' => $sales_data['total_amount'],
                'keterangan' => 'Penjualan kredit - ' . $sales_data['nomor_transaksi']
            );
        }

        // Credit: Penjualan
        $entries[] = array(
            'kode_akun' => '4-1100', // Penjualan
            'kredit' => $sales_data['total_amount'],
            'keterangan' => 'Penjualan - ' . $sales_data['nomor_transaksi']
        );

        $journal_data = array(
            'tanggal_transaksi' => $sales_data['tanggal'],
            'deskripsi' => 'Jurnal Penjualan - ' . $sales_data['nomor_transaksi'],
            'referensi' => $sales_data['nomor_transaksi'],
            'entries' => $entries
        );

        return $this->create_auto_journal($journal_data);
    }

    /**
     * Create COGS journal for sales transaction
     * @param array $cogs_data COGS data
     * @return int|false Journal ID or false on failure
     */
    public function create_cogs_journal($cogs_data)
    {
        if (!isset($cogs_data['total_cogs']) || $cogs_data['total_cogs'] <= 0) {
            return false;
        }

        $entries = array();

        // Debit: Beban Pokok Penjualan
        $entries[] = array(
            'kode_akun' => '5-1100', // Pembelian (HPP)
            'debit' => $cogs_data['total_cogs'],
            'keterangan' => 'HPP - ' . $cogs_data['nomor_transaksi']
        );

        // Credit: Persediaan
        $entries[] = array(
            'kode_akun' => '1-1500', // Persediaan
            'kredit' => $cogs_data['total_cogs'],
            'keterangan' => 'Pengurangan persediaan - ' . $cogs_data['nomor_transaksi']
        );

        $journal_data = array(
            'tanggal_transaksi' => $cogs_data['tanggal'],
            'deskripsi' => 'Jurnal HPP - ' . $cogs_data['nomor_transaksi'],
            'referensi' => $cogs_data['nomor_transaksi'],
            'entries' => $entries
        );

        return $this->create_auto_journal($journal_data);
    }

    /**
     * Create journal for purchase transaction
     * @param array $purchase_data Purchase transaction data
     * @return int|false Journal ID or false on failure
     */
    public function create_purchase_journal($purchase_data)
    {
        $entries = array();

        // Debit: Persediaan
        $entries[] = array(
            'kode_akun' => '1-1500', // Persediaan
            'debit' => $purchase_data['total_amount'],
            'keterangan' => 'Pembelian - ' . $purchase_data['nomor_transaksi']
        );

        // Credit: Hutang Usaha or Kas (depending on payment method)
        if ($purchase_data['payment_method'] == 'tunai') {
            $entries[] = array(
                'kode_akun' => '1-1101', // Kas
                'kredit' => $purchase_data['total_amount'],
                'keterangan' => 'Pembelian tunai - ' . $purchase_data['nomor_transaksi']
            );
        } else {
            $entries[] = array(
                'kode_akun' => '2-1100', // Hutang Usaha
                'kredit' => $purchase_data['total_amount'],
                'keterangan' => 'Pembelian kredit - ' . $purchase_data['nomor_transaksi']
            );
        }

        $journal_data = array(
            'tanggal_transaksi' => $purchase_data['tanggal'],
            'deskripsi' => 'Jurnal Pembelian - ' . $purchase_data['nomor_transaksi'],
            'referensi' => $purchase_data['nomor_transaksi'],
            'entries' => $entries
        );

        return $this->create_auto_journal($journal_data);
    }

    /**
     * Create journal for payment transaction
     * @param array $payment_data Payment transaction data
     * @return int|false Journal ID or false on failure
     */
    public function create_payment_journal($payment_data)
    {
        $entries = array();

        if ($payment_data['type'] == 'receivable') {
            // Payment from customer (Piutang)
            // Debit: Kas
            $entries[] = array(
                'kode_akun' => '1-1101', // Kas
                'debit' => $payment_data['amount'],
                'keterangan' => 'Pembayaran piutang - ' . $payment_data['referensi']
            );

            // Credit: Piutang Usaha
            $entries[] = array(
                'kode_akun' => '1-1200', // Piutang Usaha
                'kredit' => $payment_data['amount'],
                'keterangan' => 'Pembayaran piutang - ' . $payment_data['referensi']
            );
        } else if ($payment_data['type'] == 'payable') {
            // Payment to supplier (Hutang)
            // Debit: Hutang Usaha
            $entries[] = array(
                'kode_akun' => '2-1100', // Hutang Usaha
                'debit' => $payment_data['amount'],
                'keterangan' => 'Pembayaran hutang - ' . $payment_data['referensi']
            );

            // Credit: Kas
            $entries[] = array(
                'kode_akun' => '1-1101', // Kas
                'kredit' => $payment_data['amount'],
                'keterangan' => 'Pembayaran hutang - ' . $payment_data['referensi']
            );
        }

        $journal_data = array(
            'tanggal_transaksi' => $payment_data['tanggal'],
            'deskripsi' => 'Jurnal Pembayaran - ' . $payment_data['referensi'],
            'referensi' => $payment_data['referensi'],
            'entries' => $entries
        );

        return $this->create_auto_journal($journal_data);
    }

    /**
     * Create journal for inventory adjustment
     * @param array $adjustment_data Inventory adjustment data
     * @return int|false Journal ID or false on failure
     */
    public function create_inventory_adjustment_journal($adjustment_data)
    {
        if (!isset($adjustment_data['adjustment_value']) || $adjustment_data['adjustment_value'] == 0) {
            return false;
        }

        $entries = array();

        if ($adjustment_data['adjustment_value'] > 0) {
            // Positive adjustment (increase inventory)
            // Debit: Persediaan
            $entries[] = array(
                'kode_akun' => '1-1500', // Persediaan
                'debit' => abs($adjustment_data['adjustment_value']),
                'keterangan' => 'Penyesuaian persediaan (+) - ' . $adjustment_data['referensi']
            );

            // Credit: Pendapatan Lainnya (or specific adjustment account)
            $entries[] = array(
                'kode_akun' => '4-2400', // Pendapatan Lainnya
                'kredit' => abs($adjustment_data['adjustment_value']),
                'keterangan' => 'Penyesuaian persediaan (+) - ' . $adjustment_data['referensi']
            );
        } else {
            // Negative adjustment (decrease inventory)
            // Debit: Beban Lainnya
            $entries[] = array(
                'kode_akun' => '5-4500', // Beban Lainnya
                'debit' => abs($adjustment_data['adjustment_value']),
                'keterangan' => 'Penyesuaian persediaan (-) - ' . $adjustment_data['referensi']
            );

            // Credit: Persediaan
            $entries[] = array(
                'kode_akun' => '1-1500', // Persediaan
                'kredit' => abs($adjustment_data['adjustment_value']),
                'keterangan' => 'Penyesuaian persediaan (-) - ' . $adjustment_data['referensi']
            );
        }

        $journal_data = array(
            'tanggal_transaksi' => $adjustment_data['tanggal'],
            'deskripsi' => 'Jurnal Penyesuaian Persediaan - ' . $adjustment_data['referensi'],
            'referensi' => $adjustment_data['referensi'],
            'entries' => $entries
        );

        return $this->create_auto_journal($journal_data);
    }
}