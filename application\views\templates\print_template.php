<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Print Document' ?> - <?= $document_number ?? '' ?></title>
    <style>
        /* Reset dan Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
            background: #fff;
            margin: 0;
            padding: 20px;
        }
        
        /* Print Styles */
        @media print {
            body {
                margin: 0;
                padding: 15px;
                font-size: 11px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            @page {
                margin: 1.5cm;
                size: A4;
            }
        }
        
        /* Header Styles */
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .company-info {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        
        .document-title {
            font-size: 20px;
            font-weight: bold;
            color: #34495e;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 15px;
        }
        
        /* Info Section Styles */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }
        
        .info-box {
            flex: 1;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .info-box-title {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-item {
            margin-bottom: 10px;
            display: flex;
            align-items: flex-start;
        }
        
        .info-label {
            font-weight: 600;
            color: #34495e;
            min-width: 130px;
            margin-right: 10px;
        }
        
        .info-separator {
            margin-right: 10px;
            color: #7f8c8d;
        }
        
        .info-value {
            flex: 1;
            color: #2c3e50;
        }
        
        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-draft { background: #f39c12; color: white; }
        .status-diproses { background: #3498db; color: white; }
        .status-dikirim { background: #17a2b8; color: white; }
        .status-diterima { background: #27ae60; color: white; }
        .status-selesai { background: #28a745; color: white; }
        .status-dibatalkan { background: #e74c3c; color: white; }
        .status-lunas { background: #27ae60; color: white; }
        .status-belum_bayar { background: #e67e22; color: white; }
        .status-sebagian { background: #f39c12; color: white; }
        
        /* Table Styles */
        .table-container {
            margin: 30px 0;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #34495e;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .data-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            font-weight: bold;
            padding: 12px 10px;
            text-align: center;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #2c3e50;
        }
        
        .data-table td {
            padding: 10px;
            border-bottom: 1px solid #ecf0f1;
            vertical-align: top;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tbody tr:hover {
            background-color: #e8f4f8;
        }
        
        .total-row {
            background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%) !important;
            font-weight: bold;
            border-top: 2px solid #34495e;
        }
        
        .total-row td {
            padding: 12px 10px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        /* Text Alignment */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        
        /* Summary Section */
        .summary-section {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 20px;
        }
        
        .summary-left {
            flex: 1;
        }
        
        .summary-right {
            width: 350px;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #34495e;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .summary-table th {
            background: #34495e;
            color: white;
            padding: 10px;
            text-align: left;
            font-weight: bold;
        }
        
        .summary-table td {
            padding: 10px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
            background: #fff;
        }
        
        .summary-table .total-final {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        
        /* Notes Section */
        .notes-section {
            margin-top: 25px;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .notes-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .notes-content {
            color: #34495e;
            line-height: 1.6;
        }
        
        /* Signature Section */
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }
        
        .signature-box {
            flex: 1;
            text-align: center;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .signature-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 50px;
            font-size: 13px;
        }
        
        .signature-line {
            border-top: 2px solid #34495e;
            margin-top: 15px;
            padding-top: 8px;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        /* Footer */
        .print-footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #7f8c8d;
            border-top: 1px solid #ecf0f1;
            padding-top: 15px;
            line-height: 1.4;
        }
        
        /* Print Controls */
        .print-controls {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .btn-print {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 0 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .btn-print:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }
        
        .btn-close {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        }
        
        .btn-close:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #6c7b7d 100%);
        }
        
        /* Responsive Design */
        @media screen and (max-width: 768px) {
            .info-section {
                flex-direction: column;
            }
            
            .summary-section {
                flex-direction: column;
            }
            
            .summary-right {
                width: 100%;
            }
            
            .signature-section {
                flex-direction: column;
            }
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        /* Utility Classes */
        .font-bold { font-weight: bold; }
        .font-normal { font-weight: normal; }
        .text-small { font-size: 10px; }
        .text-large { font-size: 14px; }
        .color-primary { color: #3498db; }
        .color-success { color: #27ae60; }
        .color-warning { color: #f39c12; }
        .color-danger { color: #e74c3c; }
        .color-muted { color: #7f8c8d; }
        
        .mb-10 { margin-bottom: 10px; }
        .mb-15 { margin-bottom: 15px; }
        .mb-20 { margin-bottom: 20px; }
        .mt-10 { margin-top: 10px; }
        .mt-15 { margin-top: 15px; }
        .mt-20 { margin-top: 20px; }
        
        .p-10 { padding: 10px; }
        .p-15 { padding: 15px; }
        .p-20 { padding: 20px; }
        
        .border-top { border-top: 1px solid #ecf0f1; }
        .border-bottom { border-bottom: 1px solid #ecf0f1; }
        
        /* Icons (using Unicode symbols) */
        .icon-document::before { content: "📄 "; }
        .icon-info::before { content: "ℹ️ "; }
        .icon-warehouse::before { content: "🏪 "; }
        .icon-package::before { content: "📦 "; }
        .icon-user::before { content: "👤 "; }
        .icon-calendar::before { content: "📅 "; }
        .icon-money::before { content: "💰 "; }
        .icon-truck::before { content: "🚚 "; }
        .icon-check::before { content: "✅ "; }
        .icon-warning::before { content: "⚠️ "; }
        .icon-print::before { content: "🖨️ "; }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <button class="btn-print" onclick="window.print()">
            <span class="icon-print"></span>Cetak Dokumen
        </button>
        <button class="btn-print btn-close" onclick="window.close()">
            Tutup
        </button>
    </div>

    <!-- Header -->
    <div class="print-header">
        <div class="company-name"><?= $company_name ?? 'TOKO ELEKTRONIK' ?></div>
        <div class="company-info">
            <?= $company_address ?? 'Jl. Contoh No. 123, Kota Contoh' ?><br>
            Telp: <?= $company_phone ?? '(021) 1234567' ?> | 
            Email: <?= $company_email ?? '<EMAIL>' ?>
        </div>
        <div class="document-title"><?= $document_title ?? 'DOKUMEN' ?></div>
    </div>

    <!-- Content akan diisi oleh view yang menggunakan template ini -->
    <?= $content ?? '' ?>

    <!-- Footer -->
    <div class="print-footer">
        <div>
            <strong>Dokumen ini dicetak pada:</strong> <?= date('d/m/Y H:i:s') ?><br>
            <strong>Nomor Dokumen:</strong> <?= $document_number ?? '-' ?><br>
            Sistem Manajemen Toko Elektronik - <?= date('Y') ?>
        </div>
    </div>

    <script>
        // Auto print functionality
        if (window.location.search.includes('auto_print=1')) {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }
        
        // Close window after printing (optional)
        window.onafterprint = function() {
            if (window.location.search.includes('auto_close=1')) {
                setTimeout(function() {
                    window.close();
                }, 1000);
            }
        };
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+P for print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape to close
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>