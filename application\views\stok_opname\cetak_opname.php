<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Cetak Stok Opname',
    'document_number' => $opname->nomor_opname,
    'document_title' => 'LAPORAN STOK OPNAME'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Opname',
        'items' => [
            ['label' => 'Nomor Opname', 'value' => '<strong>' . $opname->nomor_opname . '</strong>'],
            ['label' => 'Tanggal Opname', 'value' => format_date_indonesia($opname->tanggal_opname)],
            ['label' => 'Status', 'value' => get_status_badge($opname->status)],
            ['label' => 'Keterangan', 'value' => !empty($opname->keterangan) ? $opname->keterangan : '-']
        ]
    ],
    [
        'title' => '<span class="icon-warehouse"></span>Informasi Gudang',
        'items' => [
            ['label' => 'Gudang', 'value' => $opname->nama_gudang . ' (' . $opname->kode_gudang . ')'],
            ['label' => 'Petugas', 'value' => $opname->user_input ?? '-'],
            ['label' => 'Dibuat Oleh', 'value' => $opname->user_input ?? '-'],
            ['label' => 'Tanggal Dibuat', 'value' => $opname->created_at ? format_date_indonesia($opname->created_at, true) : '-']
        ]
    ]
];

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '25%'],
    ['label' => 'Satuan', 'width' => '8%', 'align' => 'center'],
    ['label' => 'Stok Sistem', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Stok Fisik', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Selisih', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Keterangan', 'width' => '20%']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_sistem = 0;
$total_fisik = 0;
$total_selisih = 0;

foreach ($detail_list as $detail) {
    $selisih = $detail->qty_fisik - $detail->qty_sistem;
    $total_sistem += $detail->qty_sistem;
    $total_fisik += $detail->qty_fisik;
    $total_selisih += $selisih;
    
    // Color coding untuk selisih
    $selisih_display = number_format($selisih, 0, ',', '.');
    if ($selisih > 0) {
        $selisih_display = '<span class="color-success">+' . $selisih_display . '</span>';
    } elseif ($selisih < 0) {
        $selisih_display = '<span class="color-danger">' . $selisih_display . '</span>';
    }
    
    $table_data[] = [
        $no++,
        $detail->kode_barang,
        $detail->nama_barang,
        $detail->nama_satuan ?? '',
        number_format($detail->qty_sistem, 0, ',', '.'),
        number_format($detail->qty_fisik, 0, ',', '.'),
        $selisih_display,
        !empty($detail->keterangan) ? $detail->keterangan : '-'
    ];
}

// Table options with total row
$total_selisih_display = number_format($total_selisih, 0, ',', '.');
if ($total_selisih > 0) {
    $total_selisih_display = '<span class="color-success">+' . $total_selisih_display . '</span>';
} elseif ($total_selisih < 0) {
    $total_selisih_display = '<span class="color-danger">' . $total_selisih_display . '</span>';
}

$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL:</strong>', 'colspan' => '4', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_sistem, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_fisik, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . $total_selisih_display . '</strong>', 'align' => 'right'],
        ['value' => '']
    ]
];

// Prepare signatures
$signatures = [
    [
        'title' => 'Petugas Opname',
        'name' => $opname->user_input ?? '(............................)',
        'position' => 'Gudang ' . $opname->nama_gudang
    ],
    [
        'title' => 'Supervisor', 
        'name' => '(............................)',
        'position' => 'Supervisor Gudang'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Manager'
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Stok Opname', $table_headers, $table_data, $table_options);

// Add summary if there are discrepancies
if ($total_selisih != 0) {
    $summary_items = [
        ['label' => 'Total Stok Sistem', 'value' => number_format($total_sistem, 0, ',', '.')],
        ['label' => 'Total Stok Fisik', 'value' => number_format($total_fisik, 0, ',', '.')],
        ['label' => 'Total Selisih', 'value' => $total_selisih_display, 'class' => 'total-final']
    ];
    
    $content .= '<div class="summary-section">';
    $content .= '<div class="summary-left">';
    $content .= create_notes_section('Catatan Penting', 
        'Selisih positif (+) menunjukkan stok fisik lebih banyak dari sistem. ' .
        'Selisih negatif (-) menunjukkan stok fisik kurang dari sistem. ' .
        'Harap lakukan penyesuaian stok sesuai dengan hasil opname ini.');
    $content .= '</div>';
    $content .= '<div class="summary-right">';
    $content .= create_summary_table($summary_items);
    $content .= '</div>';
    $content .= '</div>';
}

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>