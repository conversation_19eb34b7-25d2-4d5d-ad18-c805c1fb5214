<div class="modal-header">
    <h4 class="modal-title">
        <i class="fas fa-undo"></i> Detail Retur: <?= $retur->nomor_retur ?>
    </h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-md-6">
            <table class="table table-bordered table-sm">
                <tr>
                    <th class="bg-light" width="35%">Nomor Retur</th>
                    <td width="65%"><?= $retur->nomor_retur ?></td>
                </tr>
                <tr>
                    <th class="bg-light">Tanggal Retur</th>
                    <td><?= date('d/m/Y', strtotime($retur->tanggal_retur)) ?></td>
                </tr>
                <tr>
                    <th class="bg-light">Pelanggan</th>
                    <td><?= $retur->nama_pelanggan ?> (<?= $retur->kode_pelanggan ?>)</td>
                </tr>
                <tr>
                    <th class="bg-light">Status</th>
                    <td>
                        <?php
                        switch ($retur->status) {
                            case 'draft':
                                echo '<span class="badge badge-warning">Draft</span>';
                                break;
                            case 'diproses':
                                echo '<span class="badge badge-primary">Diproses</span>';
                                break;
                            case 'selesai':
                                echo '<span class="badge badge-success">Selesai</span>';
                                break;
                            case 'dibatalkan':
                                echo '<span class="badge badge-danger">Dibatalkan</span>';
                                break;
                            default:
                                echo '<span class="badge badge-secondary">' . ucfirst($retur->status) . '</span>';
                        }
                        ?>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <table class="table table-bordered table-sm">
                <tr>
                    <th class="bg-light" width="35%">Faktur</th>
                    <td width="65%"><?= $retur->nomor_faktur ?? '-' ?></td>
                </tr>
                <tr>
                    <th class="bg-light">Pengiriman</th>
                    <td><?= $retur->nomor_pengiriman ?? '-' ?></td>
                </tr>
                <tr>
                    <th class="bg-light">Alamat Pelanggan</th>
                    <td><?= $retur->alamat_pelanggan ?? '-' ?></td>
                </tr>
                <tr>
                    <th class="bg-light">Telepon</th>
                    <td><?= $retur->no_telepon ?? '-' ?></td>
                </tr>
                <tr>
                    <th class="bg-light">Alasan Retur</th>
                    <td><?= $retur->alasan_retur ?? '-' ?></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Detail Item Retur</h3>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr class="bg-info">
                                    <th width="5%">No</th>
                                    <th width="15%">Kode Barang</th>
                                    <th width="25%">Nama Barang</th>
                                    <th width="15%">Gudang</th>
                                    <th width="10%">Qty Retur</th>
                                    <th width="10%">Kondisi</th>
                                    <th width="10%">Harga Satuan</th>
                                    <th width="10%">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($detail)) : ?>
                                    <tr>
                                        <td colspan="8" class="text-center">Tidak ada item</td>
                                    </tr>
                                <?php else : ?>
                                    <?php $no = 1; $total = 0; foreach ($detail as $item) : ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td><?= $item->kode_barang ?></td>
                                            <td><?= $item->nama_barang ?> <?= $item->merk ? '- '.$item->merk : '' ?> <?= $item->tipe ? '- '.$item->tipe : '' ?></td>
                                            <td><?= $item->nama_gudang ?> (<?= $item->kode_gudang ?>)</td>
                                            <td class="text-right"><?= number_format($item->qty_retur, 2) ?> <?= $item->kode_satuan ?></td>
                                    <td>
                                        <?php
                                        switch ($item->kondisi_barang) {
                                            case 'baik':
                                                echo '<span class="badge badge-success">Baik</span>';
                                                break;
                                            case 'rusak':
                                                echo '<span class="badge badge-danger">Rusak</span>';
                                                break;
                                            case 'cacat':
                                                echo '<span class="badge badge-warning">Cacat</span>';
                                                break;
                                            default:
                                                echo ucfirst($item->kondisi_barang);
                                        }
                                        ?>
                                    </td>
                                            <td class="text-right">Rp <?= number_format($item->harga_satuan, 0) ?></td>
                                            <td class="text-right">Rp <?= number_format($item->total_harga, 0) ?></td>
                                        </tr>
                                        <?php $total += $item->total_harga; ?>
                                    <?php endforeach; ?>
                                    <tr class="bg-light">
                                        <th colspan="7" class="text-right">Total</th>
                                        <th class="text-right">Rp <?= number_format($total, 0) ?></th>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
     </div>
 
     <?php if (!empty($retur->keterangan)) : ?>
         <div class="row mt-3">
             <div class="col-md-12">
                 <div class="card">
                     <div class="card-header bg-light">
                         <h3 class="card-title">Keterangan</h3>
                     </div>
                     <div class="card-body">
                         <?= nl2br($retur->keterangan) ?>
                     </div>
                 </div>
             </div>
         </div>
     <?php endif; ?>
 </div>
 <div class="modal-footer">
    <?php if ($retur->status != 'dibatalkan') : ?>
         <button type="button" class="btn btn-info" onclick="printRetur(<?= $retur->id ?>)">Print</button>
     <?php endif; ?>
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
</div>