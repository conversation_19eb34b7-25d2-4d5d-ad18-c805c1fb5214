<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="akunTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="saldo-tab" data-toggle="tab" href="#saldo" role="tab">Saldo & Pengaturan</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="detail-tab" data-toggle="tab" href="#detail" role="tab">Detail & Keterangan</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="akunTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="kode_akun" class="col-sm-3 col-form-label">Kode Akun <span class="text-danger">*</span></label>
                        <div class="col-sm-7 kosong">
                            <input type="text" class="form-control" name="kode_akun" id="kode_akun" placeholder="Kode Akun" maxlength="20">
                            <small class="form-text text-muted">Format: 1-0000, 1-1000, dst.</small>
                            <span class="help-block"></span>
                        </div>
                        <div class="col-sm-2">
                            <button type="button" id="btn-generate-kode" class="btn btn-outline-secondary" onclick="generateKode()" title="Generate Kode Otomatis">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="nama_akun" class="col-sm-3 col-form-label">Nama Akun <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="nama_akun" id="nama_akun" placeholder="Nama Akun" maxlength="100">
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="id_kategori" class="col-sm-3 col-form-label">Kategori <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_kategori" id="id_kategori" style="width: 100%;" onchange="onKategoriChange()">
                                <option value="">-- Pilih Kategori --</option>
                                <?php if(isset($kategori_list)): ?>
                                    <?php foreach($kategori_list as $kategori): ?>
                                        <option value="<?= $kategori->id ?>"><?= $kategori->kode ?> - <?= $kategori->nama ?> (<?= $kategori->tipe ?>)</option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="level" class="col-sm-3 col-form-label">Level <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="level" id="level" style="width: 100%;" onchange="onLevelChange()">
                                <option value="">-- Pilih Level --</option>
                                <option value="0">Level 0 (Header)</option>
                                <option value="1">Level 1 (Sub Header)</option>
                                <option value="2">Level 2 (Sub-Sub Header)</option>
                                <option value="3">Level 3 (Detail)</option>
                            </select>
                            <small class="form-text text-muted">Level 0: Header utama, Level 1-3: Sub header dan detail</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row" id="parent_group" style="display: none;">
                        <label for="id_parent" class="col-sm-3 col-form-label">Parent Akun <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_parent" id="id_parent" style="width: 100%;" disabled>
                                <option value="">-- Pilih Parent Akun --</option>
                            </select>
                            <small class="form-text text-muted">Pilih parent akun sesuai level</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="saldo_normal" class="col-sm-3 col-form-label">Saldo Normal <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="saldo_normal" id="saldo_normal" style="width: 100%;">
                                <option value="">-- Pilih Saldo Normal --</option>
                                <option value="Debit">Debit</option>
                                <option value="Kredit">Kredit</option>
                            </select>
                            <small class="form-text text-muted">Aset & Beban: Debit, Liabilitas, Ekuitas & Pendapatan: Kredit</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Saldo & Pengaturan -->
            <div class="tab-pane fade" id="saldo" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="dapat_diinput" class="col-sm-3 col-form-label">Dapat Diinput</label>
                        <div class="col-sm-9 kosong">
                            <div class="custom-control custom-checkbox">
                                <input class="custom-control-input" type="checkbox" id="dapat_diinput" name="dapat_diinput" value="1" checked>
                                <label for="dapat_diinput" class="custom-control-label">Akun dapat diinput dalam transaksi</label>
                            </div>
                            <small class="form-text text-muted">Jika tidak dicentang, akun hanya sebagai header/grouping</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="saldo_awal" class="col-sm-3 col-form-label">Saldo Awal</label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="saldo_awal" id="saldo_awal" placeholder="Saldo Awal" step="1" value="0">
                            <small class="form-text text-muted">Saldo awal akun (opsional)</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tanggal_saldo_awal" class="col-sm-3 col-form-label">Tanggal Saldo Awal</label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="tanggal_saldo_awal" id="tanggal_saldo_awal" value="<?= date('Y-m-d') ?>">
                            <small class="form-text text-muted">Tanggal saldo awal (opsional)</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="aktif" class="col-sm-3 col-form-label">Status</label>
                        <div class="col-sm-9 kosong">
                            <div class="custom-control custom-checkbox">
                                <input class="custom-control-input" type="checkbox" id="aktif" name="aktif" value="1" checked>
                                <label for="aktif" class="custom-control-label">Aktif</label>
                            </div>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Detail & Keterangan -->
            <div class="tab-pane fade" id="detail" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="deskripsi" class="col-sm-3 col-form-label">Deskripsi</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="deskripsi" id="deskripsi" placeholder="Deskripsi Akun" rows="4"></textarea>
                            <small class="form-text text-muted">Informasi tambahan mengenai akun ini</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <hr>
                    <h6 class="text-muted">Informasi Tambahan</h6>

                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> <strong>Catatan:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Kode akun harus unik dan sesuai format</li>
                            <li>Level 0 adalah header utama, tidak perlu parent</li>
                            <li>Level 1-3 harus memiliki parent dari level di atasnya</li>
                            <li>Akun yang sudah digunakan dalam transaksi tidak dapat dihapus</li>
                        </ul>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });
});

function onKategoriChange() {
    var id_kategori = $('#id_kategori').val();
    var level = $('#level').val();
    
    if (id_kategori && level && level > 0) {
        loadParentAkun();
    }
    
    // Set saldo normal berdasarkan kategori
    if (id_kategori) {
        var kategoriText = $('#id_kategori option:selected').text();
        if (kategoriText.includes('Aset') || kategoriText.includes('Beban')) {
            $('#saldo_normal').val('Debit').trigger('change');
        } else if (kategoriText.includes('Liabilitas') || kategoriText.includes('Ekuitas') || kategoriText.includes('Pendapatan')) {
            $('#saldo_normal').val('Kredit').trigger('change');
        }
    }
}

function onLevelChange() {
    var level = $('#level').val();
    
    if (level > 0) {
        $('#parent_group').show();
        loadParentAkun();
    } else {
        $('#parent_group').hide();
        $('#id_parent').val('').trigger('change');
        $('#id_parent').prop('disabled', true);
    }
    
    // Set dapat_diinput berdasarkan level
    if (level < 3) {
        $('#dapat_diinput').prop('checked', false);
    } else {
        $('#dapat_diinput').prop('checked', true);
    }
}

function loadParentAkun() {
    var id_kategori = $('#id_kategori').val();
    var level = $('#level').val();
    
    if (id_kategori && level && level > 0) {
        $.ajax({
            url: "<?php echo site_url('coa/get_parent_akun') ?>",
            type: "POST",
            data: {
                id_kategori: id_kategori,
                level: level
            },
            dataType: "JSON",
            success: function(data) {
                var options = '<option value="">-- Pilih Parent Akun --</option>';
                $.each(data, function(index, value) {
                    options += '<option value="' + value.id + '">' + value.kode_akun + ' - ' + value.nama_akun + '</option>';
                });
                $('#id_parent').html(options);
                $('#id_parent').prop('disabled', false);
            },
            error: function() {
                $('#id_parent').html('<option value="">-- Pilih Parent Akun --</option>');
                $('#id_parent').prop('disabled', true);
            }
        });
    } else {
        $('#id_parent').html('<option value="">-- Pilih Parent Akun --</option>');
        $('#id_parent').prop('disabled', true);
    }
}

function generateKode() {
    var id_kategori = $('#id_kategori').val();
    var level = $('#level').val();
    var id_parent = $('#id_parent').val();
    
    if (!id_kategori) {
        Swal.fire({
            title: 'Perhatian!',
            text: 'Pilih kategori terlebih dahulu',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return;
    }
    
    if (level === null || level === '') {
        Swal.fire({
            title: 'Perhatian!',
            text: 'Pilih level terlebih dahulu',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return;
    }
    
    if (level > 0 && !id_parent) {
        Swal.fire({
            title: 'Perhatian!',
            text: 'Pilih parent akun terlebih dahulu',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return;
    }
    
    $.ajax({
        url: "<?php echo site_url('coa/generate_kode_akun') ?>",
        type: "POST",
        data: {
            id_kategori: id_kategori,
            level: level,
            id_parent: id_parent
        },
        dataType: "JSON",
        success: function(data) {
            if (data.kode) {
                $('#kode_akun').val(data.kode);
            }
        },
        error: function() {
            Swal.fire({
                title: 'Error!',
                text: 'Gagal generate kode akun.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}
</script>