<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Cetak Transfer Stok',
    'document_number' => $transfer->nomor_transfer,
    'document_title' => 'SURAT TRANSFER STOK'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Transfer',
        'items' => [
            ['label' => 'Nomor Transfer', 'value' => '<strong>' . $transfer->nomor_transfer . '</strong>'],
            ['label' => 'Tanggal', 'value' => format_date_indonesia($transfer->tanggal_transfer)],
            ['label' => 'Status', 'value' => get_status_badge($transfer->status)],
            ['label' => 'Keterangan', 'value' => !empty($transfer->keterangan) ? $transfer->keterangan : '-']
        ]
    ],
    [
        'title' => '<span class="icon-warehouse"></span>Informasi Gudang',
        'items' => [
            ['label' => 'Gudang Asal', 'value' => $transfer->nama_gudang_asal . ' (' . $transfer->kode_gudang_asal . ')'],
            ['label' => 'Gudang Tujuan', 'value' => $transfer->nama_gudang_tujuan . ' (' . $transfer->kode_gudang_tujuan . ')'],
            ['label' => 'Dibuat Oleh', 'value' => $transfer->dibuat_oleh ?? '-'],
            ['label' => 'Tanggal Dibuat', 'value' => $transfer->dibuat_pada ? format_date_indonesia($transfer->dibuat_pada, true) : '-']
        ]
    ]
];

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '15%'],
    ['label' => 'Nama Barang', 'width' => '35%'],
    ['label' => 'Satuan', 'width' => '15%', 'align' => 'center'],
    ['label' => 'Qty', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Keterangan', 'width' => '20%']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_qty = 0;

foreach ($detail_list as $detail) {
    $total_qty += $detail->qty;
    $table_data[] = [
        $no++,
        $detail->kode_barang,
        $detail->nama_barang,
        $detail->nama_satuan,
        number_format($detail->qty, 0, ',', '.'),
        !empty($detail->keterangan) ? $detail->keterangan : '-'
    ];
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL ITEM:</strong>', 'colspan' => '4', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '']
    ]
];

// Prepare signatures
$signatures = [
    [
        'title' => 'Pengirim',
        'name' => $transfer->dibuat_oleh ?? '(............................)',
        'position' => 'Gudang ' . $transfer->nama_gudang_asal
    ],
    [
        'title' => 'Penerima', 
        'name' => $transfer->diterima_oleh ?? '(............................)',
        'position' => 'Gudang ' . $transfer->nama_gudang_tujuan
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Manager'
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Barang Transfer', $table_headers, $table_data, $table_options);
$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>