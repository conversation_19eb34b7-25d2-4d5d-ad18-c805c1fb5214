<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Settlement Pembelian (Pembelian vs Penerimaan)</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            Settlement Pembelian digunakan untuk memastikan bahwa semua barang yang dipesan telah diterima dengan benar.
                            Proses ini akan membandingkan data pembelian dengan penerimaan barang untuk mengidentifikasi perbedaan atau kesalahan.
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card card-outline card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Filter Data</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="form-filter" class="form-horizontal">
                                    <div class="form-group row">
                                        <label for="tanggal_awal" class="col-sm-2 col-form-label">Tanggal Pembelian</label>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?= $tanggal_awal ?>">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">s/d</span>
                                                </div>
                                                <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?= $tanggal_akhir ?>">
                                            </div>
                                        </div>
                                        <label for="status" class="col-sm-2 col-form-label">Status Penerimaan</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="status" name="status">
                                                <option value="">Semua Status</option>
                                                <option value="belum_diterima">Belum Diterima</option>
                                                <option value="diterima_sebagian">Diterima Sebagian</option>
                                                <option value="sesuai">Sesuai</option>
                                                <option value="diterima_lebih">Diterima Lebih</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="offset-sm-2 col-sm-10">
                                            <button type="button" id="btn-filter" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                                            <button type="button" id="btn-reset" class="btn btn-default"><i class="fas fa-undo"></i> Reset</button>
                                            <button type="button" id="btn-export" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="table-settlement" class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Nomor Pembelian</th>
                                        <th>Tanggal Pembelian</th>
                                        <th>Supplier</th>
                                        <th>Jenis</th>
                                        <th>Status</th>
                                        <th>Total Item</th>
                                        <th>Total Qty</th>
                                        <th>Total Diterima</th>
                                        <th>Selisih Qty</th>
                                        <th>Status Penerimaan</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#table-settlement').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?= site_url('settlement/pembelian_data') ?>",
            "type": "POST",
            "data": function(data) {
                data.tanggal_awal = $('#tanggal_awal').val();
                data.tanggal_akhir = $('#tanggal_akhir').val();
                data.status = $('#status').val();
            },
            "dataSrc": "data"
        },
        "columns": [
            { "data": null, "orderable": false, "render": function(data, type, row, meta) {
                return meta.row + 1;
            }},
            { "data": "nomor_pembelian" },
            { "data": "tanggal_pembelian" },
            { "data": "nama_supplier" },
            { "data": "jenis_pembelian", "render": function(data, type, row) {
                if (data == 'reguler') {
                    return 'Reguler';
                } else if (data == 'konsinyasi') {
                    return 'Konsinyasi';
                } else if (data == 'kontrak') {
                    return 'Kontrak';
                } else {
                    return data;
                }
            }},
            { "data": "status", "render": function(data, type, row) {
                if (data == 'draft') {
                    return '<span class="badge badge-secondary">Draft</span>';
                } else if (data == 'disetujui') {
                    return '<span class="badge badge-info">Disetujui</span>';
                } else if (data == 'dipesan') {
                    return '<span class="badge badge-primary">Dipesan</span>';
                } else if (data == 'diterima') {
                    return '<span class="badge badge-warning">Diterima</span>';
                } else if (data == 'selesai') {
                    return '<span class="badge badge-success">Selesai</span>';
                } else if (data == 'dibatalkan') {
                    return '<span class="badge badge-danger">Dibatalkan</span>';
                } else {
                    return data;
                }
            }},
            { "data": "total_item" },
            { "data": "total_qty" },
            { "data": "total_diterima" },
            { "data": "selisih_qty", "render": function(data, type, row) {
                if (data == 0) {
                    return '<span class="text-success">0</span>';
                } else if (data > 0) {
                    return '<span class="text-danger">' + data + '</span>';
                } else {
                    return '<span class="text-warning">' + data + '</span>';
                }
            }},
            { "data": "status_penerimaan", "render": function(data, type, row) {
                if (data == 'belum_diterima') {
                    return '<span class="badge badge-danger">Belum Diterima</span>';
                } else if (data == 'diterima_sebagian') {
                    return '<span class="badge badge-warning">Diterima Sebagian</span>';
                } else if (data == 'sesuai') {
                    return '<span class="badge badge-success">Sesuai</span>';
                } else if (data == 'diterima_lebih') {
                    return '<span class="badge badge-info">Diterima Lebih</span>';
                } else {
                    return data;
                }
            }},
            { "data": null, "orderable": false, "render": function(data, type, row) {
                var html = '<div class="btn-group">';
                html += '<a href="<?= site_url('settlement/pembelian_detail/') ?>' + row.id + '" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>';
                html += '</div>';
                return html;
            }}
        ],
        "order": [[2, 'desc']],
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    $('#btn-filter').click(function() {
        table.ajax.reload();
    });

    $('#btn-reset').click(function() {
        $('#form-filter')[0].reset();
        table.ajax.reload();
    });

    $('#btn-export').click(function() {
        var tanggal_awal = $('#tanggal_awal').val();
        var tanggal_akhir = $('#tanggal_akhir').val();
        var status = $('#status').val();
        
        window.location.href = '<?= site_url('settlement/export/pembelian') ?>?tanggal_awal=' + tanggal_awal + '&tanggal_akhir=' + tanggal_akhir + '&status=' + status;
    });
});
</script>