<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Pengiriman Barang
 * Mengatur pengiriman barang dan detailnya
 * Konsisten dengan modul pesanan
 */
class Pengiriman extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_pengiriman', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'pengiriman/pengiriman', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_pengiriman->get_datatables();
        $data = array();
        foreach ($list as $pengiriman) {
            $row = array();
            $row[] = $pengiriman->nomor_pengiriman;
            $row[] = date('d/m/Y', strtotime($pengiriman->tanggal_pengiriman));
            $row[] = $pengiriman->nomor_pesanan;
            $row[] = $pengiriman->nama_pelanggan . ' (' . $pengiriman->kode_pelanggan . ')';
            
            // Status badge
            switch ($pengiriman->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-warning">Draft</span>';
                    break;
                case 'prepared':
                    $status_badge = '<span class="badge badge-primary">Disiapkan</span>';
                    break;
                case 'shipped':
                    $status_badge = '<span class="badge badge-info">Dikirim</span>';
                    break;
                case 'in_transit':
                    $status_badge = '<span class="badge badge-secondary">Dalam Perjalanan</span>';
                    break;
                case 'delivered':
                    $status_badge = '<span class="badge badge-success">Diterima</span>';
                    break;
                case 'returned':
                    $status_badge = '<span class="badge badge-danger">Dikembalikan</span>';
                    break;
                case 'cancelled':
                    $status_badge = '<span class="badge badge-danger">Dibatalkan</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-secondary">' . ucfirst($pengiriman->status) . '</span>';
            }
            $row[] = $status_badge;
            
            $row[] = number_format($pengiriman->total_item ?? 0, 0) . ' item';
            $row[] = number_format($pengiriman->total_qty ?? 0, 0);
            $row[] = number_format($pengiriman->total_berat ?? 0, 2) . ' kg';
            
            // Estimasi tiba
            $row[] = $pengiriman->estimasi_tiba ? date('d/m/Y', strtotime($pengiriman->estimasi_tiba)) : '-';
            
            // Action buttons
            $actions = '';
            if ($pengiriman->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $pengiriman->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pengiriman->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success process" href="javascript:void(0)" title="Siapkan" onclick="updateStatus(' . $pengiriman->id . ', \'prepared\')"><i class="fas fa-box"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $pengiriman->id . ')"><i class="fas fa-trash"></i></a>';
            } else if ($pengiriman->status == 'prepared') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pengiriman->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success ship" href="javascript:void(0)" title="Kirim" onclick="updateStatus(' . $pengiriman->id . ', \'shipped\')"><i class="fas fa-truck"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printPengiriman(' . $pengiriman->id . ')"><i class="fas fa-print"></i></a>';
            } else if ($pengiriman->status == 'shipped' || $pengiriman->status == 'in_transit') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pengiriman->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success deliver" href="javascript:void(0)" title="Terima" onclick="updateStatus(' . $pengiriman->id . ', \'delivered\')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printPengiriman(' . $pengiriman->id . ')"><i class="fas fa-print"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pengiriman->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printPengiriman(' . $pengiriman->id . ')"><i class="fas fa-print"></i></a>';
            }

            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_pengiriman->count_all(),
            "recordsFiltered" => $this->Mod_pengiriman->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    // Method untuk form input modal
    public function form_input()
    {
        // Load dropdown data untuk form
        $data['pelanggan_list'] = $this->Mod_pengiriman->get_pelanggan_aktif();
        $data['pesanan_list'] = $this->Mod_pengiriman->get_pesanan_aktif();
        $data['nomor_pengiriman'] = $this->Mod_pengiriman->generate_nomor_pengiriman();
        $this->load->view('pengiriman/form_input', $data);
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_pesanan_info()
    {
        $id = $this->input->post('id');
        $pesanan = $this->Mod_pengiriman->get_pesanan_by_id($id);

        if ($pesanan) {
            echo json_encode(array('status' => true, 'data' => $pesanan));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data pesanan tidak ditemukan'));
        }
    }

    public function get_pesanan_items()
    {
        $id = $this->input->post('id');
        $items = $this->Mod_pengiriman->get_pesanan_detail($id);

        if ($items) {
            echo json_encode(array('status' => true, 'data' => $items));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Item pesanan tidak ditemukan'));
        }
    }

    public function form_detail_item()
    {
        $id_pengiriman = $this->input->post('id_pengiriman');
        $id_pesanan = $this->input->post('id_pesanan');
        
        // Get pengiriman data if id_pengiriman is provided
        if ($id_pengiriman) {
            $pengiriman = $this->Mod_pengiriman->get_by_id($id_pengiriman);
            if ($pengiriman) {
                $id_pesanan = $pengiriman->id_pesanan;
            }
        }
        
        // Get ordered items with remaining quantities if id_pesanan is provided
        $data['ordered_items'] = [];
        if ($id_pesanan) {
            $data['ordered_items'] = $this->Mod_pengiriman->get_ordered_items_for_shipping($id_pesanan, $id_pengiriman);
        }
        
        // We'll load warehouses dynamically via AJAX when a product is selected
        $data['id_pengiriman'] = $id_pengiriman;
        $data['id_pesanan'] = $id_pesanan;
        
        $this->load->view('pengiriman/form_detail_item', $data);
    }
    
    // New method to get warehouses with stock for a specific product
    public function get_warehouses_with_stock()
    {
        $id_barang = $this->input->post('id_barang');
        $id_pengiriman = $this->input->post('id_pengiriman');
        
        // Get warehouses with stock
        $warehouses = $this->Mod_pengiriman->get_warehouses_with_stock($id_barang);
        
        if ($warehouses && $id_pengiriman) {
            // For each warehouse, check if there are items already in this shipment
            foreach ($warehouses as $warehouse) {
                $this->db->select('COALESCE(SUM(qty_dikirim), 0) as qty_dalam_pengiriman');
                $this->db->from('pengiriman_detail');
                $this->db->where('id_pengiriman', $id_pengiriman);
                $this->db->where('id_barang', $id_barang);
                $this->db->where('id_gudang', $warehouse->id);
                $result = $this->db->get()->row();
                
                if ($result && $result->qty_dalam_pengiriman > 0) {
                    $warehouse->qty_dalam_pengiriman_ini = $result->qty_dalam_pengiriman;
                    // Adjust available stock
                    $warehouse->stok_tersedia = $warehouse->stok_tersedia - $result->qty_dalam_pengiriman;
                }
            }
            
            // Filter out warehouses with no available stock after adjustment
            $filtered_warehouses = array_filter($warehouses, function($warehouse) {
                return $warehouse->stok_tersedia > 0 || (isset($warehouse->qty_dalam_pengiriman_ini) && $warehouse->qty_dalam_pengiriman_ini > 0);
            });
            
            // Re-index array
            $filtered_warehouses = array_values($filtered_warehouses);
            
            if (!empty($filtered_warehouses)) {
                echo json_encode(array('status' => true, 'data' => $filtered_warehouses));
                return;
            }
        }
        
        if ($warehouses) {
            echo json_encode(array('status' => true, 'data' => $warehouses));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Tidak ada gudang dengan stok tersedia untuk barang ini'));
        }
    }
    
    // New method to get available stock and remaining quantity to ship
    public function get_stock_and_remaining()
    {
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $id_pesanan = $this->input->post('id_pesanan');
        $id_pengiriman = $this->input->post('id_pengiriman');
        $id_item = $this->input->post('id_item'); // For editing existing item
        
        // Get base stock available
        $stok_tersedia = $this->Mod_pengiriman->get_stok_barang($id_barang, $id_gudang);
        
        // Check if there are other items with the same product in the current shipment from the same warehouse
        $this->db->select('COALESCE(SUM(qty_dikirim), 0) as current_qty');
        $this->db->from('pengiriman_detail');
        $this->db->where('id_pengiriman', $id_pengiriman);
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        
        // If editing, exclude the current item
        if ($id_item) {
            $this->db->where('id !=', $id_item);
        }
        
        $other_items_result = $this->db->get()->row();
        $other_items_qty = $other_items_result ? $other_items_result->current_qty : 0;
        
        // Adjust available stock by subtracting what's already allocated in this shipment from this warehouse
        $stok_tersedia_adjusted = $stok_tersedia - $other_items_qty;
        
        // Get remaining quantity to be shipped (now fixed to not subtract current shipment qty)
        $sisa_pesanan_original = $this->Mod_pengiriman->get_remaining_qty_to_ship($id_pesanan, $id_barang, $id_pengiriman);
        
        // For display purposes, always show the original remaining quantity
        $sisa_pesanan_display = $sisa_pesanan_original;
        
        // For max_qty calculation, we need to consider if we're editing
        $sisa_pesanan_for_max = $sisa_pesanan_original;
        if ($id_item) {
            $current_item = $this->Mod_pengiriman->get_detail_item_by_id($id_item);
            if ($current_item && $current_item->id_barang == $id_barang) {
                // When editing, we can use the current item's quantity plus the remaining
                $sisa_pesanan_for_max += $current_item->qty_dikirim;
            }
        }
        
        // Max quantity is limited by both available stock and remaining order quantity
        $max_qty = min($stok_tersedia_adjusted, $sisa_pesanan_for_max);
        $max_qty = max(0, $max_qty); // Ensure it's not negative
        
        echo json_encode(array(
            'status' => true,
            'stok_tersedia' => $stok_tersedia_adjusted,
            'sisa_pesanan' => $sisa_pesanan_display, // Always show original remaining
            'max_qty' => $max_qty
        ));
    }

    public function edit($id)
    {
        $data = $this->Mod_pengiriman->get_by_id($id);
        echo json_encode($data);
    }

    public function detail_modal($id)
    {
        $data['pengiriman'] = $this->Mod_pengiriman->get_by_id($id);
        $data['pengiriman_detail'] = $this->Mod_pengiriman->get_detail_by_pengiriman_id($id);
        $data['barang_list'] = $this->Mod_pengiriman->get_barang_aktif();
        $data['gudang_list'] = $this->Mod_pengiriman->get_gudang_aktif();
        $this->load->view('pengiriman/detail_modal', $data);
    }
    
    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_pengiriman');
        if (empty($nomor)) {
            $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
        }

        $save = array(
            'nomor_pengiriman' => $nomor,
            'tanggal_pengiriman' => $this->input->post('tanggal_pengiriman'),
            'id_pesanan' => $this->input->post('id_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan_hidden') ? $this->input->post('id_pelanggan_hidden') : $this->input->post('id_pelanggan'),
            'alamat_pengiriman' => $this->input->post('alamat_pengiriman'),
            'status' => 'draft', // Always set to draft
            'estimasi_tiba' => $this->input->post('estimasi_tiba'),
            'keterangan' => $this->input->post('keterangan'),
            'total_item' => 0,
            'total_qty' => 0,
            'total_berat' => 0,
            'created_by' => $this->session->userdata('nama_user'),
        );

        $insert_id = $this->Mod_pengiriman->insert('pengiriman', $save);
        
        if ($insert_id) {
            echo json_encode(array(
                "status" => TRUE,
                "message" => "Data pengiriman berhasil disimpan"
            ));
        } else {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Gagal menyimpan data pengiriman"
            ));
        }
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'tanggal_pengiriman' => $this->input->post('tanggal_pengiriman'),
            'id_pesanan' => $this->input->post('id_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan_hidden') ? $this->input->post('id_pelanggan_hidden') : $this->input->post('id_pelanggan'),
            'alamat_pengiriman' => $this->input->post('alamat_pengiriman'),
            'status' => 'draft', // Always set to draft
            'estimasi_tiba' => $this->input->post('estimasi_tiba'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('nama_user'),
        );

        $update = $this->Mod_pengiriman->update($id, $save);
        
        if ($update) {
            echo json_encode(array(
                "status" => TRUE,
                "message" => "Data pengiriman berhasil diupdate"
            ));
        } else {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Gagal mengupdate data pengiriman"
            ));
        }
    }
    
    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;
        
        if($this->input->post('nomor_pengiriman') == '')
        {
            $data['inputerror'][] = 'nomor_pengiriman';
            $data['error_string'][] = 'Nomor pengiriman tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('tanggal_pengiriman') == '')
        {
            $data['inputerror'][] = 'tanggal_pengiriman';
            $data['error_string'][] = 'Tanggal pengiriman tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        // Customer validation removed as it's automatically filled from order selection
        
        if($this->input->post('id_pesanan') == '')
        {
            $data['inputerror'][] = 'id_pesanan';
            $data['error_string'][] = 'Pesanan harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('alamat_pengiriman') == '')
        {
            $data['inputerror'][] = 'alamat_pengiriman';
            $data['error_string'][] = 'Alamat pengiriman tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($data['status'] === FALSE)
        {
            echo json_encode($data);
            exit();
        }
    }

    public function delete()
    {
        $id = $this->input->post('id');

        // Cek apakah pengiriman masih draft
        $pengiriman = $this->Mod_pengiriman->get_by_id($id);
        if($pengiriman && $pengiriman->status != 'draft') {
            echo json_encode(array('status' => 'error', 'message' => 'Pengiriman yang sudah diproses tidak dapat dihapus!'));
            return;
        }

        // Hapus detail pengiriman terlebih dahulu
        $this->Mod_pengiriman->delete_detail_by_pengiriman($id);

        // Hapus pengiriman
        $delete_result = $this->Mod_pengiriman->delete($id, 'pengiriman');
        
        if($delete_result) {
            echo json_encode(array(
                "status" => 'success', 
                "message" => 'Data pengiriman berhasil dihapus.'
            ));
        } else {
            echo json_encode(array(
                "status" => 'error', 
                "message" => 'Gagal menghapus data pengiriman.'
            ));
        }
    }

    // Detail pengiriman functions
    public function add_detail()
    {
        $id_pengiriman = $this->input->post('id_pengiriman');
        $id_pesanan = $this->input->post('id_pesanan');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $qty_dikirim = $this->input->post('qty_dikirim');
        $berat_satuan = $this->input->post('berat_satuan');
        $keterangan = $this->input->post('keterangan');
        
        // Validate input
        if (empty($id_barang) || empty($id_gudang) || empty($qty_dikirim)) {
            echo json_encode(array('status' => 'error', 'message' => 'Semua field harus diisi!'));
            return;
        }
        
        // Get available stock
        $stok_tersedia = $this->Mod_pengiriman->get_stok_barang($id_barang, $id_gudang);
        
        // Check if this product is already in the current shipment
        $this->db->select('COALESCE(SUM(qty_dikirim), 0) as current_qty');
        $this->db->from('pengiriman_detail');
        $this->db->where('id_pengiriman', $id_pengiriman);
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $current_result = $this->db->get()->row();
        $current_qty_in_shipment = $current_result ? $current_result->current_qty : 0;
        
        // Adjust available stock by subtracting what's already in this shipment from this warehouse
        $stok_tersedia_adjusted = $stok_tersedia - $current_qty_in_shipment;
        
        // Get remaining quantity to be shipped (now fixed to not subtract current shipment qty)
        $sisa_pesanan = $this->Mod_pengiriman->get_remaining_qty_to_ship($id_pesanan, $id_barang, $id_pengiriman);
        
        // Validate quantity
        if ($qty_dikirim <= 0) {
            echo json_encode(array('status' => 'error', 'message' => 'Qty harus lebih dari 0!'));
            return;
        }
        
        if ($qty_dikirim > $stok_tersedia_adjusted) {
            echo json_encode(array('status' => 'error', 'message' => 'Qty melebihi stok tersedia di gudang! (Stok tersedia: ' . $stok_tersedia_adjusted . ')'));
            return;
        }
        
        if ($qty_dikirim > $sisa_pesanan) {
            echo json_encode(array('status' => 'error', 'message' => 'Qty melebihi sisa pesanan yang belum dikirim! (Sisa: ' . $sisa_pesanan . ')'));
            return;
        }
        
        // Additional validation: Check total quantity in current shipment doesn't exceed remaining order
        $this->db->select('COALESCE(SUM(qty_dikirim), 0) as total_in_shipment');
        $this->db->from('pengiriman_detail');
        $this->db->where('id_pengiriman', $id_pengiriman);
        $this->db->where('id_barang', $id_barang);
        $total_result = $this->db->get()->row();
        $total_in_shipment = $total_result ? $total_result->total_in_shipment : 0;
        
        if (($total_in_shipment + $qty_dikirim) > $sisa_pesanan) {
            echo json_encode(array('status' => 'error', 'message' => 'Total qty dalam pengiriman ini akan melebihi sisa pesanan! (Total akan menjadi: ' . ($total_in_shipment + $qty_dikirim) . ', Sisa pesanan: ' . $sisa_pesanan . ')'));
            return;
        }
        
        $data = array(
            'id_pengiriman' => $id_pengiriman,
            'id_barang' => $id_barang,
            'id_gudang' => $id_gudang,
            'qty_dikirim' => $qty_dikirim,
            'berat_satuan' => $berat_satuan,
            'total_berat' => $qty_dikirim * $berat_satuan,
            'keterangan' => $keterangan
        );
        
        $insert = $this->Mod_pengiriman->save_detail($data);
        if ($insert) {
            $this->Mod_pengiriman->update_total_pengiriman($id_pengiriman);
            echo json_encode(array('status' => 'success', 'message' => 'Item berhasil ditambahkan!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Item gagal ditambahkan!'));
        }
    }

    public function update_detail()
    {
        $id = $this->input->post('id_item');
        $id_pengiriman = $this->input->post('id_pengiriman');
        $id_pesanan = $this->input->post('id_pesanan');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $qty_dikirim = $this->input->post('qty_dikirim');
        $berat_satuan = $this->input->post('berat_satuan');
        $keterangan = $this->input->post('keterangan');
        
        // Validate input
        if (empty($id_barang) || empty($id_gudang) || empty($qty_dikirim)) {
            echo json_encode(array('status' => 'error', 'message' => 'Semua field harus diisi!'));
            return;
        }
        
        // Get current item data
        $current_item = $this->Mod_pengiriman->get_detail_item_by_id($id);
        if (!$current_item) {
            echo json_encode(array('status' => 'error', 'message' => 'Item tidak ditemukan!'));
            return;
        }
        
        // Get available stock
        $stok_tersedia = $this->Mod_pengiriman->get_stok_barang($id_barang, $id_gudang);
        
        // Check if there are other items with the same product in the current shipment
        $this->db->select('COALESCE(SUM(qty_dikirim), 0) as current_qty');
        $this->db->from('pengiriman_detail');
        $this->db->where('id_pengiriman', $id_pengiriman);
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $this->db->where('id !=', $id); // Exclude the current item
        $other_items_result = $this->db->get()->row();
        $other_items_qty = $other_items_result ? $other_items_result->current_qty : 0;
        
        // If updating the same item in the same warehouse, add current quantity to available stock
        if ($current_item->id_barang == $id_barang && $current_item->id_gudang == $id_gudang) {
            // Adjust available stock by adding current item's quantity and subtracting other items' quantities
            $stok_tersedia_adjusted = $stok_tersedia + $current_item->qty_dikirim - $other_items_qty;
        } else {
            // If changing product or warehouse, just subtract other items' quantities
            $stok_tersedia_adjusted = $stok_tersedia - $other_items_qty;
        }
        
        // Get remaining quantity to be shipped (now fixed to not subtract current shipment qty)
        $sisa_pesanan = $this->Mod_pengiriman->get_remaining_qty_to_ship($id_pesanan, $id_barang, $id_pengiriman);
        
        // If updating the same item, add current quantity back to remaining quantity
        if ($current_item->id_barang == $id_barang) {
            $sisa_pesanan += $current_item->qty_dikirim;
        }
        
        // Validate quantity
        if ($qty_dikirim <= 0) {
            echo json_encode(array('status' => 'error', 'message' => 'Qty harus lebih dari 0!'));
            return;
        }
        
        if ($qty_dikirim > $stok_tersedia_adjusted) {
            echo json_encode(array('status' => 'error', 'message' => 'Qty melebihi stok tersedia di gudang! (Stok tersedia: ' . $stok_tersedia_adjusted . ')'));
            return;
        }
        
        if ($qty_dikirim > $sisa_pesanan) {
            echo json_encode(array('status' => 'error', 'message' => 'Qty melebihi sisa pesanan yang belum dikirim! (Sisa: ' . $sisa_pesanan . ')'));
            return;
        }
        
        // Additional validation for update: Check total quantity in current shipment doesn't exceed remaining order
        // Get original remaining quantity (without adding current item back)
        $original_sisa_pesanan = $this->Mod_pengiriman->get_remaining_qty_to_ship($id_pesanan, $id_barang, $id_pengiriman);
        
        $this->db->select('COALESCE(SUM(qty_dikirim), 0) as total_in_shipment');
        $this->db->from('pengiriman_detail');
        $this->db->where('id_pengiriman', $id_pengiriman);
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id !=', $id); // Exclude current item being updated
        $total_result = $this->db->get()->row();
        $total_in_shipment = $total_result ? $total_result->total_in_shipment : 0;
        
        if (($total_in_shipment + $qty_dikirim) > $original_sisa_pesanan) {
            // Debug information
            $debug_info = "Debug Info: ";
            $debug_info .= "Total in shipment (other items): " . $total_in_shipment . ", ";
            $debug_info .= "Qty dikirim (new): " . $qty_dikirim . ", ";
            $debug_info .= "Total akan menjadi: " . ($total_in_shipment + $qty_dikirim) . ", ";
            $debug_info .= "Sisa pesanan asli: " . $original_sisa_pesanan . ", ";
            $debug_info .= "Current item qty: " . $current_item->qty_dikirim;
            
            echo json_encode(array('status' => 'error', 'message' => 'Total qty dalam pengiriman ini akan melebihi sisa pesanan! ' . $debug_info));
            return;
        }
        
        $data = array(
            'id_barang' => $id_barang,
            'id_gudang' => $id_gudang,
            'qty_dikirim' => $qty_dikirim,
            'berat_satuan' => $berat_satuan,
            'total_berat' => $qty_dikirim * $berat_satuan,
            'keterangan' => $keterangan
        );
        
        $update = $this->Mod_pengiriman->update_detail(array('id' => $id), $data);
        if ($update) {
            $this->Mod_pengiriman->update_total_pengiriman($id_pengiriman);
            echo json_encode(array('status' => 'success', 'message' => 'Item berhasil diupdate!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Item gagal diupdate!'));
        }
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $id_pengiriman = $this->input->post('id_pengiriman');
        
        $this->Mod_pengiriman->delete_detail($id);
        $this->Mod_pengiriman->update_total_pengiriman($id_pengiriman);
        
        echo json_encode(array('status' => 'success', 'message' => 'Item berhasil dihapus!'));
    }
    
    public function get_detail_item($id)
    {
        $detail = $this->Mod_pengiriman->get_detail_item_by_id($id);
        
        if ($detail) {
            echo json_encode(array('status' => 'success', 'data' => $detail));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Data item tidak ditemukan'));
        }
    }

    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $penerima = $this->input->post('penerima');
        
        $data = array(
            'status' => $status,
            'updated_by' => $this->session->userdata('nama_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        // Tambahkan data sesuai status
        if ($status == 'shipped') {
            $data['tanggal_dikirim'] = date('Y-m-d H:i:s');
        } else if ($status == 'delivered') {
            $data['tanggal_diterima'] = date('Y-m-d H:i:s');
            $data['penerima'] = $penerima;
        }
        
        $update = $this->Mod_pengiriman->update($id, $data);
        
        // Update status pesanan jika diperlukan
        if ($update) {
            $pengiriman = $this->Mod_pengiriman->get_by_id($id);
            if ($status == 'shipped') {
                $this->Mod_pengiriman->update_status_pesanan($pengiriman->id_pesanan, 'dikirim');
            } else if ($status == 'delivered') {
                $this->Mod_pengiriman->update_status_pesanan($pengiriman->id_pesanan, 'selesai');
            }
            
            echo json_encode(array('status' => 'success', 'message' => 'Status pengiriman berhasil diupdate'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal mengupdate status pengiriman'));
        }
    }
    
    public function print($id)
    {
        $data['pengiriman'] = $this->Mod_pengiriman->get_by_id($id);
        $data['pengiriman_detail'] = $this->Mod_pengiriman->get_detail_by_pengiriman_id($id);
        $this->load->view('pengiriman/print_pengiriman', $data);
    }
    
    public function get_berat_barang()
    {
        $id_barang = $this->input->post('id_barang');
        
        if (empty($id_barang)) {
            echo json_encode(array('status' => false, 'message' => 'ID Barang tidak valid'));
            return;
        }
        
        $berat = $this->Mod_pengiriman->get_berat_barang_by_id($id_barang);
        
        if ($berat !== null) {
            echo json_encode(array('status' => true, 'berat' => $berat));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Berat barang tidak ditemukan'));
        }
    }
}