<?php if (empty($items)) : ?>
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle"></i> Tidak ada item yang tersedia untuk diretur atau semua item sudah diretur.
    </div>
<?php else : ?>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr class="bg-light">
                    <th width="5%">No</th>
                    <th width="15%">Kode Barang</th>
                    <th width="20%">Nama Barang</th>
                    <th width="10%">Gudang</th>
                    <th width="10%">Qty Tersedia</th>
                    <th width="10%">Qty Retur</th>
                    <th width="10%">Kondisi</th>
                    <th width="10%">Harga Satuan</th>
                    <th width="10%">Keterangan</th>
                </tr>
            </thead>
            <tbody>
                <?php $no = 1; foreach ($items as $index => $item) : ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td>
                            <?= $item->kode_barang ?>
                            <input type="hidden" name="id_barang[]" value="<?= $item->id_barang ?>">
                        </td>
                        <td><?= $item->nama_barang ?> <?= $item->merk ? '- '.$item->merk : '' ?> <?= $item->tipe ? '- '.$item->tipe : '' ?></td>
                        <td>
                            <?php if ($sumber == 'pengiriman' && isset($item->id_gudang) && isset($item->nama_gudang)) : ?>
                                <?= $item->nama_gudang ?>
                                <input type="hidden" name="id_gudang[]" value="<?= $item->id_gudang ?>">
                            <?php else : ?>
                                <select class="form-control form-control-sm" name="id_gudang[]" required>
                                    <option value="">-- Pilih Gudang --</option>
                                    <?php foreach ($gudang_list as $gudang) : ?>
                                        <option value="<?= $gudang->id ?>"><?= $gudang->nama_gudang ?></option>
                                    <?php endforeach; ?>
                                </select>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $max_qty = isset($item->qty_faktur) ? $item->qty_faktur : (isset($item->qty_dikirim) ? $item->qty_dikirim : 0);
                            $qty_sudah_diretur = isset($item->qty_sudah_diretur) ? $item->qty_sudah_diretur : 0;
                            
                            // Qty tersedia adalah qty asli dikurangi qty yang sudah diretur (dari retur lain)
                            // Qty retur existing sudah dikurangi dari qty_sudah_diretur di controller
                            $qty_tersedia = $max_qty - $qty_sudah_diretur;
                            
                            echo number_format($qty_tersedia, 2);
                            ?>
                        </td>
                        <td>
                            <?php 
                            $default_qty = isset($item->qty_retur_existing) ? $item->qty_retur_existing : 0;
                            ?>
                            <input type="number" class="form-control form-control-sm qty-input" name="qty_retur[]" min="0.01" max="<?= $qty_tersedia ?>" step="1" value="<?= $default_qty ?>" required>
                        </td>
                        <td>
                            <select class="form-control form-control-sm" name="kondisi_barang[]" required>
                                <option value="baik" <?= (isset($item->kondisi_existing) && $item->kondisi_existing == 'baik') ? 'selected' : '' ?>>Baik</option>
                                <option value="rusak" <?= (isset($item->kondisi_existing) && $item->kondisi_existing == 'rusak') ? 'selected' : '' ?>>Rusak</option>
                                <option value="cacat" <?= (isset($item->kondisi_existing) && $item->kondisi_existing == 'cacat') ? 'selected' : '' ?>>Cacat</option>
                            </select>
                        </td>
                        <td>
                            <?php 
                            $default_harga = isset($item->harga_satuan_existing) ? $item->harga_satuan_existing : (isset($item->harga_satuan) ? $item->harga_satuan : 0);
                            ?>
                            <input type="number" class="form-control form-control-sm" name="harga_satuan[]" value="<?= $default_harga ?>" min="0" step="1" required>
                        </td>
                        <td>
                            <input type="text" class="form-control form-control-sm" name="keterangan_item[]" placeholder="Keterangan" value="<?= isset($item->keterangan_existing) ? $item->keterangan_existing : '' ?>">
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> Masukkan jumlah barang yang akan diretur. Jumlah tidak boleh melebihi qty yang tersedia.
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
    $(document).ready(function() {
        // Validasi input qty
        $('.qty-input').on('input', function() {
            var max = parseFloat($(this).attr('max'));
            var value = parseFloat($(this).val());
            
            if (value > max) {
                $(this).val(max);
                Swal.fire({
                    title: 'Peringatan!',
                    text: 'Qty retur tidak boleh melebihi qty tersedia',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
            }
        });
    });
</script>