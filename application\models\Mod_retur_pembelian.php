<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Retur Pembelian
 * Mengatur data retur pembelian dan detailnya
 * Terintegrasi dengan modul pembelian dan penerimaan pembelian
 */
class Mod_retur_pembelian extends CI_Model
{
    var $table = 'retur_pembelian';
    var $table_detail = 'retur_pembelian_detail';
    var $column_search = array(
        'rp.nomor_retur', 
        'rp.tanggal_retur', 
        's.nama', 
        'p.nomor_pembelian', 
        'pp.nomor_penerimaan',
        'rp.status', 
        'rp.keterangan'
    );
    var $column_order = array(
        'rp.id', 
        'rp.nomor_retur', 
        'rp.tanggal_retur', 
        's.nama', 
        'p.nomor_pembelian', 
        'pp.nomor_penerimaan',
        'rp.status',
        'rp.total_item',
        'rp.total_qty',
        'rp.total_nilai'
    );
    var $order = array('rp.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            rp.id,
            rp.nomor_retur,
            rp.tanggal_retur,
            rp.id_penerimaan,
            rp.id_pembelian,
            rp.id_supplier,
            rp.status,
            rp.total_item,
            rp.total_qty,
            rp.total_nilai,
            rp.alasan_retur,
            rp.keterangan,
            rp.created_by,
            rp.updated_by,
            rp.created_at,
            rp.updated_at,
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            p.nomor_pembelian,
            pp.nomor_penerimaan
        ');
        $this->db->from($this->table . ' rp');
        $this->db->join('supplier s', 'rp.id_supplier = s.id', 'left');
        $this->db->join('pembelian p', 'rp.id_pembelian = p.id', 'left');
        $this->db->join('penerimaan_pembelian pp', 'rp.id_penerimaan = pp.id', 'left');
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->select('
            rp.*,
            s.kode as kode_supplier,
            s.nama as nama_supplier,
            s.alamat as alamat_supplier,
            s.no_telepon,
            s.email,
            p.nomor_pembelian,
            p.tanggal_pembelian,
            p.jenis_pembelian,
            pp.nomor_penerimaan,
            pp.tanggal_penerimaan
        ');
        $this->db->from($this->table . ' rp');
        $this->db->join('supplier s', 'rp.id_supplier = s.id', 'left');
        $this->db->join('pembelian p', 'rp.id_pembelian = p.id', 'left');
        $this->db->join('penerimaan_pembelian pp', 'rp.id_penerimaan = pp.id', 'left');
        $this->db->where('rp.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    // Method konsisten dengan modul lain
    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
        return $this->db->affected_rows();
    }

    public function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }

    // Detail retur functions
    public function get_detail_by_retur_id($id_retur)
    {
        $this->db->select('
            rpd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.kode_satuan,
            g.nama_gudang,
            g.kode_gudang
        ');
        $this->db->from($this->table_detail . ' rpd');
        $this->db->join('barang b', 'rpd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'rpd.id_gudang = g.id', 'left');
        $this->db->where('rpd.id_retur', $id_retur);
        $this->db->order_by('rpd.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    public function update_detail($where, $data)
    {
        $this->db->update($this->table_detail, $data, $where);
        return $this->db->affected_rows();
    }

    public function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    public function delete_detail_by_retur($id_retur)
    {
        $this->db->where('id_retur', $id_retur);
        $this->db->delete($this->table_detail);
    }

    // Generate nomor retur - Format: RPB-YYYYMMDD-XXXX (Retur Pembelian)
    public function generate_nomor_retur()
    {
        $prefix = 'RPB';
        $date = date('Ymd');
        
        $this->db->select('nomor_retur');
        $this->db->from($this->table);
        $this->db->like('nomor_retur', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_retur', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_retur;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Update total retur
    public function update_total_retur($id_retur)
    {
        $this->db->select('COUNT(DISTINCT id_barang) as total_item, SUM(qty_retur) as total_qty, SUM(total_harga) as total_nilai');
        $this->db->from($this->table_detail);
        $this->db->where('id_retur', $id_retur);
        $query = $this->db->get();
        $result = $query->row();
        
        $data = array(
            'total_item' => $result->total_item ? $result->total_item : 0,
            'total_qty' => $result->total_qty ? $result->total_qty : 0,
            'total_nilai' => $result->total_nilai ? $result->total_nilai : 0,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id_retur);
        $this->db->update($this->table, $data);
    }

    // Method untuk mendapatkan data dropdown
    public function get_supplier_aktif()
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('supplier');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pembelian_aktif()
    {
        $this->db->select('p.id, p.nomor_pembelian, p.tanggal_pembelian, p.id_supplier, p.status, s.nama as nama_supplier');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        
        // Pembelian yang sudah diterima atau selesai
        $this->db->where_in('p.status', ['diterima', 'selesai']);
        
        // Pastikan pembelian memiliki item
        $this->db->where('EXISTS (SELECT 1 FROM pembelian_detail pd WHERE pd.id_pembelian = p.id)');
        
        $this->db->order_by('p.tanggal_pembelian', 'desc');
        $query = $this->db->get();
        
        return $query->result();
    }

    public function get_penerimaan_by_pembelian($id_pembelian)
    {
        $this->db->select('pp.id, pp.nomor_penerimaan, pp.tanggal_penerimaan, pp.status');
        $this->db->from('penerimaan_pembelian pp');
        $this->db->where('pp.id_pembelian', $id_pembelian);
        $this->db->where_in('pp.status', ['diterima', 'selesai']);
        $this->db->order_by('pp.tanggal_penerimaan', 'desc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_penerimaan_aktif()
    {
        $this->db->select('pp.id, pp.nomor_penerimaan, pp.tanggal_penerimaan, pp.id_pembelian, pp.id_supplier, pp.status, s.nama as nama_supplier, p.nomor_pembelian');
        $this->db->from('penerimaan_pembelian pp');
        $this->db->join('supplier s', 'pp.id_supplier = s.id', 'left');
        $this->db->join('pembelian p', 'pp.id_pembelian = p.id', 'left');
        
        // Penerimaan yang sudah diterima atau selesai
        $this->db->where_in('pp.status', ['diterima', 'selesai']);
        
        // Pastikan penerimaan memiliki item
        $this->db->where('EXISTS (SELECT 1 FROM penerimaan_pembelian_detail ppd WHERE ppd.id_penerimaan = pp.id)');
        
        $this->db->order_by('pp.tanggal_penerimaan', 'desc');
        $query = $this->db->get();
        
        return $query->result();
    }

    public function get_pembelian_by_id($id)
    {
        $this->db->select('p.*, s.nama as nama_supplier, s.kode as kode_supplier');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        $this->db->where('p.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_penerimaan_by_id($id)
    {
        $this->db->select('pp.*, s.nama as nama_supplier, s.kode as kode_supplier, p.nomor_pembelian');
        $this->db->from('penerimaan_pembelian pp');
        $this->db->join('supplier s', 'pp.id_supplier = s.id', 'left');
        $this->db->join('pembelian p', 'pp.id_pembelian = p.id', 'left');
        $this->db->where('pp.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_penerimaan_detail($id_penerimaan)
    {
        $this->db->select('
            ppd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.kode_satuan,
            g.nama_gudang,
            g.kode_gudang,
            pd.harga_satuan
        ');
        $this->db->from('penerimaan_pembelian_detail ppd');
        $this->db->join('barang b', 'ppd.id_barang = b.id', 'left');
        $this->db->join('pembelian_detail pd', 'ppd.id_pembelian_detail = pd.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'ppd.id_gudang = g.id', 'left');
        $this->db->where('ppd.id_penerimaan', $id_penerimaan);
        $query = $this->db->get();
        return $query->result();
    }

    public function get_gudang_aktif()
    {
        $this->db->select('id, kode_gudang, nama_gudang, alamat, keterangan');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Cek kuantitas barang yang sudah pernah diretur dan selesai diproses
     * Hanya menghitung retur dengan status 'diproses' atau 'selesai'
     * Tidak termasuk retur draft dari ID retur lain untuk menghindari konflik
     * 
     * @param int $id_penerimaan_detail ID detail penerimaan
     * @param int $id_barang ID barang
     * @param int|null $exclude_id_retur ID retur yang dikecualikan (untuk editing)
     * @return int Total qty yang sudah diretur dan selesai
     */
    public function get_qty_already_returned($id_penerimaan_detail, $id_barang, $exclude_id_retur = null)
    {
        $this->db->select('COALESCE(SUM(rpd.qty_retur), 0) as total_returned');
        $this->db->from('retur_pembelian_detail rpd');
        $this->db->join('retur_pembelian rp', 'rpd.id_retur = rp.id', 'left');
        $this->db->where('rpd.id_penerimaan_detail', $id_penerimaan_detail);
        $this->db->where('rpd.id_barang', $id_barang);
        // Hanya hitung retur yang sudah diproses atau selesai, tidak termasuk draft dan dibatalkan
        $this->db->where_in('rp.status', ['diproses', 'selesai']);
        
        // Kecualikan retur yang sedang dibuat/diedit untuk menghindari double counting
        if ($exclude_id_retur !== null) {
            $this->db->where('rp.id !=', $exclude_id_retur);
        }
        
        $query = $this->db->get();
        $result = $query->row();
        return $result ? $result->total_returned : 0;
    }
    
    // Fungsi untuk membuat barang keluar otomatis dari retur pembelian
    public function create_barang_keluar_from_retur($id_retur)
    {
        // Load model barang keluar
        $this->load->model('Mod_barang_keluar');
        
        // Get data retur
        $retur = $this->get_by_id($id_retur);
        if (!$retur) {
            return array('status' => false, 'message' => 'Data retur tidak ditemukan');
        }
        
        // Get detail retur
        $detail_items = $this->get_detail_by_retur_id($id_retur);
        if (empty($detail_items)) {
            return array('status' => false, 'message' => 'Detail retur tidak ditemukan');
        }
        
        // Buat header barang keluar
        $data_header = array(
            'nomor_pengeluaran' => $this->Mod_barang_keluar->generate_nomor(),
            'tanggal' => date('Y-m-d'),
            'jenis' => 'retur_pembelian',
            'ref_nomor' => $retur->nomor_retur,
            'id_pelanggan' => null, // Retur pembelian tidak terkait dengan pelanggan
            'status' => 'draft',
            'keterangan' => 'Barang keluar dari retur pembelian ' . $retur->nomor_retur,
            'created_by' => $retur->updated_by,
            'created_at' => date('Y-m-d H:i:s')
        );
        
        // Insert header barang keluar
        $id_barang_keluar = $this->Mod_barang_keluar->insert($data_header);
        
        if (!$id_barang_keluar) {
            return array('status' => false, 'message' => 'Gagal membuat barang keluar');
        }
        
        // Insert detail barang keluar
        $total_item = 0;
        $total_qty = 0;
        
        foreach ($detail_items as $item) {
            // Get data satuan
            $this->db->select('satuan_id');
            $this->db->from('barang');
            $this->db->where('id', $item->id_barang);
            $barang = $this->db->get()->row();
            
            $data_detail = array(
                'id_barang_keluar' => $id_barang_keluar,
                'id_barang' => $item->id_barang,
                'id_gudang' => $item->id_gudang,
                'id_satuan' => $barang ? $barang->satuan_id : null,
                'qty_keluar' => $item->qty_retur,
                'keterangan' => 'Retur pembelian: ' . $item->keterangan,
                'created_at' => date('Y-m-d H:i:s')
            );
            
            $this->Mod_barang_keluar->insert_detail($data_detail);
            $total_item++;
            $total_qty += $item->qty_retur;
        }
        
        // Update total di header
        $data_update = array(
            'total_item' => $total_item,
            'total_qty' => $total_qty
        );
        
        $this->Mod_barang_keluar->update($id_barang_keluar, $data_update);
        
        // Finalisasi barang keluar
        $result = $this->Mod_barang_keluar->finalize_barang_keluar($id_barang_keluar, $retur->updated_by);
        
        if (!$result['valid']) {
            return array('status' => false, 'message' => $result['message']);
        }
        
        // Update retur dengan referensi barang keluar
        $this->db->where('id', $id_retur);
        $this->db->update('retur_pembelian', array('ref_barang_keluar' => $id_barang_keluar));
        
        return array(
            'status' => true, 
            'message' => 'Barang keluar berhasil dibuat',
            'id_barang_keluar' => $id_barang_keluar
        );
    }
    
    // Fungsi untuk mengurangi stok barang
    public function update_stok_barang($id_retur)
    {
        // Load model penyesuaian stok
        $this->load->model('Mod_penyesuaian_stok');
        
        // Get detail retur
        $detail_items = $this->get_detail_by_retur_id($id_retur);
        if (empty($detail_items)) {
            return array('status' => false, 'message' => 'Detail retur tidak ditemukan');
        }
        
        $retur = $this->get_by_id($id_retur);
        
        foreach ($detail_items as $item) {
            // Get stok terakhir
            $stok_terakhir = $this->Mod_penyesuaian_stok->get_stok_terakhir($item->id_barang, $item->id_gudang);
            
            // Calculate new stock
            $stok_baru = $stok_terakhir - $item->qty_retur;
            
            // Record stock movement
            $data_movement = array(
                'tanggal' => date('Y-m-d H:i:s'),
                'id_barang' => $item->id_barang,
                'id_gudang' => $item->id_gudang,
                'qty_awal' => $stok_terakhir,
                'qty_perubahan' => -$item->qty_retur, // Negative for reduction
                'qty_akhir' => $stok_baru,
                'jenis_transaksi' => 'retur_pembelian',
                'id_referensi' => $id_retur,
                'keterangan' => 'Pengurangan stok dari retur pembelian ' . $retur->nomor_retur,
                'created_by' => $retur->updated_by,
                'created_at' => date('Y-m-d H:i:s')
            );
            
            $this->Mod_penyesuaian_stok->insert_stok_movement($data_movement);
        }
        
        return array('status' => true, 'message' => 'Stok berhasil diperbarui');
    }
    
    // Method untuk mendapatkan detail penerimaan berdasarkan ID
    public function get_penerimaan_detail_by_id($id_penerimaan_detail)
    {
        $this->db->select('
            ppd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.kode_satuan,
            g.nama_gudang,
            g.kode_gudang,
            pd.harga_satuan
        ');
        $this->db->from('penerimaan_pembelian_detail ppd');
        $this->db->join('barang b', 'ppd.id_barang = b.id', 'left');
        $this->db->join('pembelian_detail pd', 'ppd.id_pembelian_detail = pd.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'ppd.id_gudang = g.id', 'left');
        $this->db->where('ppd.id', $id_penerimaan_detail);
        $query = $this->db->get();
        return $query->row();
    }
    
    public function get_detail_by_retur_and_item($id_retur, $id_penerimaan_detail, $id_barang) {
        $this->db->select('*');
        $this->db->from('retur_pembelian_detail');
        $this->db->where('id_retur', $id_retur);
        $this->db->where('id_penerimaan_detail', $id_penerimaan_detail);
        $this->db->where('id_barang', $id_barang);
        $query = $this->db->get();
        return $query->row();
    }
}