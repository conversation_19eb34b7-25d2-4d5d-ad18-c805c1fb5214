<!-- Header Info -->
<div class="row mb-3">
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Nomor Opname</th>
                <td><?= $opname->nomor_opname ?></td>
            </tr>
            <tr>
                <th>Tanggal</th>
                <td><?= date('d/m/Y', strtotime($opname->tanggal_opname)) ?></td>
            </tr>
            <tr>
                <th>Gudang</th>
                <td><?= $opname->nama_gudang ?> (<?= $opname->kode_gudang ?>)</td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Status</th>
                <td>
                    <?php if ($opname->status == 'final'): ?>
                        <span class="badge badge-success">FINAL</span>
                    <?php else: ?>
                        <span class="badge badge-warning">DRAFT</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <th>Total Item</th>
                <td><?= number_format($opname->total_item, 0) ?> item</td>
            </tr>
            <tr>
                <th>Selisih</th>
                <td>
                    <span class="text-success">+<?= number_format($opname->total_selisih_positif, 0) ?></span> /
                    <span class="text-danger">-<?= number_format($opname->total_selisih_negatif, 0) ?></span>
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- Action Buttons -->
<?php if ($opname->status == 'draft'): ?>
<div class="row mb-3">
    <div class="col-12">
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addDetailItem()">
            <i class="fas fa-plus"></i> Tambah Item
        </button>
        <button type="button" class="btn btn-sm btn-outline-info" onclick="autoGenerateDetail()">
            <i class="fas fa-magic"></i> Auto Generate dari Stok Sistem
        </button>
        <button type="button" class="btn btn-sm btn-outline-success" onclick="finalizeFromDetail()">
            <i class="fas fa-check"></i> Finalisasi Opname
        </button>
    </div>
</div>
<?php endif; ?>

<!-- Detail Table -->
<div class="table-responsive">
    <table id="tbl_detail_opname" class="table table-bordered table-striped table-sm">
        <thead class="bg-light">
            <tr>
                <th width="5%">No</th>
                <th width="15%">Kode Barang</th>
                <th width="25%">Nama Barang</th>
                <th width="12%">Qty Sistem</th>
                <th width="12%">Qty Fisik</th>
                <th width="12%">Selisih</th>
                <th width="15%">Keterangan</th>
                <?php if ($opname->status == 'draft'): ?>
                <th width="10%">Aksi</th>
                <?php endif; ?>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($detail_list)): ?>
                <?php $no = 1; foreach ($detail_list as $detail): ?>
                <tr>
                    <td><?= $no++ ?></td>
                    <td><?= $detail->kode_barang ?></td>
                    <td><?= $detail->nama_barang ?></td>
                    <td class="text-right"><?= number_format($detail->qty_sistem, 0) ?></td>
                    <td class="text-right"><?= number_format($detail->qty_fisik, 0) ?></td>
                    <td class="text-right">
                        <?php if ($detail->selisih >= 0): ?>
                            <span class="text-success">+<?= number_format($detail->selisih, 0) ?></span>
                        <?php else: ?>
                            <span class="text-danger"><?= number_format($detail->selisih, 0) ?></span>
                        <?php endif; ?>
                    </td>
                    <td><?= $detail->keterangan ?: '-' ?></td>
                    <?php if ($opname->status == 'draft'): ?>
                    <td>
                        <button type="button" class="btn btn-xs btn-outline-info" onclick="editDetailItem(<?= $detail->id ?>)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(<?= $detail->id ?>)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                    <?php endif; ?>
                </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="<?= $opname->status == 'draft' ? '8' : '7' ?>" class="text-center">
                        Belum ada detail item. 
                        <?php if ($opname->status == 'draft'): ?>
                            <a href="javascript:void(0)" onclick="autoGenerateDetail()">Klik di sini untuk auto generate dari stok sistem</a>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Modal Form Detail -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Item Opname</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form_detail">
                    <input type="hidden" name="id" id="detail_id">
                    <input type="hidden" name="id_opname" value="<?= $opname->id ?>">
                    <input type="hidden" name="id_gudang" value="<?= $opname->id_gudang ?>">
                    
                    <div class="form-group" id="barang_group">
                        <label for="id_barang">Barang <span class="text-danger">*</span></label>
                        <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                            <option value="">-- Pilih Barang --</option>
                            <?php foreach ($barang_available as $barang): ?>
                                <option value="<?= $barang->id ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                            <?php endforeach; ?>
                        </select>
                        <span class="help-block"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="qty_sistem">Qty Sistem</label>
                        <input type="number" class="form-control" name="qty_sistem" id="qty_sistem" readonly>
                        <small class="form-text text-muted">Akan terisi otomatis setelah memilih barang.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="qty_fisik">Qty Fisik <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="qty_fisik" id="qty_fisik" required min="0" step="1" oninput="updateSelisihDetail()">
                        <span class="help-block"></span>
                    </div>
                    
                    <div class="form-group">
                        <label>Selisih</label>
                        <div class="form-control-plaintext">
                            <strong id="selisih_display">0.00</strong>
                            <small class="text-muted ml-2">(Qty Fisik - Qty Sistem)</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="keterangan_detail">Keterangan</label>
                        <textarea class="form-control" name="keterangan" id="keterangan_detail" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
                        <span class="help-block"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="saveDetail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-secondary" onclick="$('#modal_form_detail').modal('hide');">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
var save_method_detail;
var current_opname_id = <?= $opname->id ?>;
var current_gudang_id = <?= $opname->id_gudang ?>;

$(document).ready(function() {
    // Initialize Select2 untuk modal detail
    $('#modal_form_detail').on('shown.bs.modal', function() {
        $('.select2').select2({
            dropdownParent: $('#modal_form_detail'),
            placeholder: "-- Pilih --",
            allowClear: true
        });
    });
    
    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var id_barang = $(this).val();
        if (id_barang) {
            loadQtySistem(id_barang);
        } else {
            $('#qty_sistem').val('');
            updateSelisihDetail(); // Call updateSelisihDetail when barang is cleared
        }
    });
});

function addDetailItem() {
    save_method_detail = 'add';
    $('#form_detail')[0].reset();
    $('#detail_id').val('');
    $('#barang_group').show();
    $('#qty_sistem').val('');
    $('#selisih_display').text('0.00');
    $('.modal-title').text('Tambah Detail Item');
    $('#modal_form_detail').modal('show');
}

function editDetailItem(id) {
    save_method_detail = 'update';
    $('#barang_group').hide(); // Hide barang selection saat edit
    $('.modal-title').text('Edit Detail Item');
    
    $.ajax({
        url: "<?php echo site_url('StokOpname/edit_detail/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('#detail_id').val(data.id);
            $('#id_barang').val(data.id_barang).trigger('change');
            $('#qty_sistem').val(data.qty_sistem);
            $('#qty_fisik').val(data.qty_fisik);
            $('#keterangan_detail').val(data.keterangan);
            updateSelisihDetail(); // Ensure selisih is updated when editing
            $('#modal_form_detail').modal('show');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat memuat data detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function saveDetail() {
    $('#btnSaveDetail').text('saving...').prop('disabled', true);

    var url;
    if (save_method_detail == 'add') {
        url = "<?php echo site_url('StokOpname/insert_detail') ?>";
    } else {
        url = "<?php echo site_url('StokOpname/update_detail') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form_detail').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form_detail').modal('hide');

                // Reload hanya tabel detail, bukan seluruh konten modal
                reloadDetailTable();

                // Trigger event to update main DataTable
                $(document).trigger('detailSaved');

                Swal.fire({
                    title: 'Berhasil!',
                    text: 'Detail item berhasil disimpan.',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                // Error handling remains the same
                for (var i = 0; i < data.inputerror.length; i++) {
                    $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                    $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                }

                var errorMessages = data.error_string.join('<br>');
                Swal.fire({
                    title: 'Gagal Menyimpan!',
                    html: 'Terjadi kesalahan:<br>' + errorMessages,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menyimpan detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        },
        complete: function() {
            $('#btnSaveDetail').text('Save').prop('disabled', false);
        }
    });
}

function deleteDetailItem(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Detail item akan dihapus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?php echo site_url('StokOpname/delete_detail') ?>",
                type: "POST",
                dataType: "JSON",
                data: { id: id },
                success: function(data) {
                    if (data.status) {
                        // Reload hanya tabel detail, bukan seluruh konten modal
                        reloadDetailTable();

                        // Trigger event to update main DataTable
                        $(document).trigger('detailSaved');

                        Swal.fire({
                            title: 'Terhapus!',
                            text: 'Detail item berhasil dihapus.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Gagal menghapus detail item.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menghapus detail.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function autoGenerateDetail() {
    Swal.fire({
        title: 'Auto Generate Detail?',
        text: "Sistem akan membuat detail berdasarkan stok yang ada di gudang. Detail yang sudah ada akan dihapus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#17a2b8',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, generate!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?php echo site_url('StokOpname/auto_generate_detail') ?>",
                type: "POST",
                dataType: "JSON",
                data: {
                    id_opname: current_opname_id,
                    id_gudang: current_gudang_id
                },
                success: function(data) {
                    if (data.status) {
                        // Reload hanya tabel detail, bukan seluruh konten modal
                        reloadDetailTable();

                        // Trigger event to update main DataTable
                        $(document).trigger('detailSaved');

                        Swal.fire({
                            title: 'Berhasil!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Gagal generate detail.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat generate detail.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function finalizeFromDetail() {
    Swal.fire({
        title: 'Finalisasi Stok Opname?',
        text: "Setelah difinalisasi, data tidak dapat diubah lagi dan akan otomatis update stok sistem!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, finalisasi!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?php echo site_url('StokOpname/finalize') ?>",
                type: "POST",
                dataType: "JSON",
                data: { id: current_opname_id },
                success: function(data) {
                    if (data.status) {
                        // Reload seluruh konten detail untuk update status
                        reloadDetailContent();

                        // Trigger event to update main DataTable
                        $(document).trigger('detailSaved');

                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Stok opname berhasil difinalisasi.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Gagal finalisasi stok opname.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat finalisasi.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function loadQtySistem(id_barang) {
    $.ajax({
        url: "<?php echo site_url('StokOpname/get_stok_sistem') ?>",
        type: "POST",
        data: {
            id_barang: id_barang,
            id_gudang: current_gudang_id
        },
        dataType: "JSON",
        success: function(data) {
            $('#qty_sistem').val(data.qty_sistem);
            updateSelisihDetail();
        },
        error: function(jqXHR, textStatus, errorThrown) {
            $('#qty_sistem').val('0');
            updateSelisihDetail();
        }
    });
}

function updateSelisihDetail() {
    var qty_fisik = parseFloat($('#qty_fisik').val()) || 0;
    var qty_sistem = parseFloat($('#qty_sistem').val()) || 0;
    var selisih = qty_fisik - qty_sistem;
    $('#selisih_display').text(Math.round(selisih));
}

// Function to reload detail content in parent modal
function reloadDetailContent() {
    $.ajax({
        url: "<?php echo site_url('StokOpname/detail/') ?>" + current_opname_id,
        type: "GET",
        dataType: "html",
        success: function(data) {
            // Update the modal content
            $('#modal-detail-body').html(data);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error reloading detail content:', textStatus);
            // Fallback: reload the page if AJAX fails
            location.reload();
        }
    });
}

// Fungsi untuk reload hanya tabel detail tanpa reload seluruh modal
function reloadDetailTable() {
    $.ajax({
        url: "<?php echo site_url('StokOpname/get_detail_table/') ?>" + current_opname_id,
        type: "GET",
        dataType: "html",
        success: function(data) {
            // Update hanya bagian tabel detail
            $('.table-responsive').html(data);
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error reloading detail table:', textStatus);
            // Fallback: reload seluruh konten detail
            reloadDetailContent();
        }
    });
}
</script>





