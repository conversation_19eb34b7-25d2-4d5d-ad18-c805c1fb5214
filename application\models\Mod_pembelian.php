<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Pembelian (Purchase)
 * Mengatur data pembelian dari supplier dan detailnya
 */
class Mod_pembelian extends CI_Model
{
    var $table = 'v_pembelian_summary';
    var $table_detail = 'pembelian_detail';
    var $table_header = 'pembelian';
    var $column_search = array(
        'nomor_pembelian', 
        'tanggal_pembelian', 
        'nama_supplier', 
        'jenis_pembelian',
        'status', 
        'keterangan'
    );
    var $column_order = array(
        'id', 
        'nomor_pembelian', 
        'tanggal_pembelian', 
        'nama_supplier', 
        'jenis_pembelian',
        'status',
        'status_pembayaran',
        'total_item',
        'total_qty',
        'total_akhir'
    );
    var $order = array('id' => 'desc');
    var $filter = array();

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        
        // Apply filters
        $this->_apply_filters();
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    private function _apply_filters()
    {
        if (!empty($this->filter['status'])) {
            $this->db->where('status', $this->filter['status']);
        }
        
        if (!empty($this->filter['jenis'])) {
            $this->db->where('jenis_pembelian', $this->filter['jenis']);
        }
        
        if (!empty($this->filter['tanggal_dari'])) {
            $this->db->where('tanggal_pembelian >=', $this->filter['tanggal_dari']);
        }
        
        if (!empty($this->filter['tanggal_sampai'])) {
            $this->db->where('tanggal_pembelian <=', $this->filter['tanggal_sampai']);
        }
    }

    public function set_filter($filter)
    {
        $this->filter = $filter;
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function save($data)
    {
        $this->db->insert($this->table_header, $data);
        return $this->db->insert_id();
    }

    public function delete_by_id($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_header);
    }

    // Method konsisten dengan modul lain
    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_header, $data);
        return $this->db->affected_rows();
    }

    public function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }

    // Detail pembelian functions
    public function get_detail_by_pembelian_id($id_pembelian)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            s.kode_satuan,
            g.nama_gudang,
            g.kode_gudang
        ');
        $this->db->from($this->table_detail . ' pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'pd.id_gudang = g.id', 'left');
        $this->db->where('pd.id_pembelian', $id_pembelian);
        $this->db->order_by('pd.id', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        $insert_id = $this->db->insert_id();
        
        // Update total pembelian
        if ($insert_id) {
            $this->update_total_pembelian($data['id_pembelian']);
        }
        
        return $insert_id;
    }

    public function update_detail($where, $data)
    {
        $this->db->update($this->table_detail, $data, $where);
        $affected = $this->db->affected_rows();
        
        // Update total pembelian jika ada perubahan
        if ($affected > 0 && isset($where['id'])) {
            $detail = $this->get_detail_by_id($where['id']);
            if ($detail) {
                $this->update_total_pembelian($detail->id_pembelian);
            }
        }
        
        return $affected;
    }

    public function delete_detail($id)
    {
        // Ambil id_pembelian sebelum dihapus
        $detail = $this->get_detail_by_id($id);
        $id_pembelian = $detail ? $detail->id_pembelian : null;
        
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
        
        // Update total pembelian setelah hapus
        if ($id_pembelian) {
            $this->update_total_pembelian($id_pembelian);
        }
    }

    public function delete_detail_by_pembelian($id_pembelian)
    {
        $this->db->where('id_pembelian', $id_pembelian);
        $this->db->delete($this->table_detail);
    }

    // Generate nomor pembelian - Format: PB-YYYYMMDD-XXXX
    public function generate_nomor_pembelian()
    {
        $prefix = 'PB';
        $date = date('Ymd');
        
        $this->db->select('nomor_pembelian');
        $this->db->from($this->table_header);
        $this->db->like('nomor_pembelian', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_pembelian', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_pembelian;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Generate nomor pembayaran - Format: BPB-YYYYMMDD-XXXX (Bayar Pembelian)
    public function generate_nomor_pembayaran()
    {
        $prefix = 'BPB';
        $date = date('Ymd');
        
        $this->db->select('nomor_pembayaran');
        $this->db->from('pembelian_pembayaran');
        $this->db->like('nomor_pembayaran', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_pembayaran', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_pembayaran;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Update total pembelian
    public function update_total_pembelian($id_pembelian)
    {
        // Panggil stored procedure untuk update total
        $this->db->query("CALL sp_update_pembelian_totals(?)", array($id_pembelian));
    }

    // Method untuk mendapatkan data dropdown
    public function get_supplier_aktif()
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('supplier');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_barang_aktif()
    {
        $this->db->select('b.id, b.kode_barang, b.nama_barang, b.merk, b.tipe, b.harga_beli, s.nama_satuan');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('b.aktif', 1);
        $this->db->order_by('b.nama_barang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_gudang_aktif()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_detail_by_id($id)
    {
        $this->db->select('*');
        $this->db->from($this->table_detail);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_supplier_by_id($id)
    {
        $this->db->select('id, kode, nama, alamat, no_telepon, email, nama_pic');
        $this->db->from('supplier');
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    // Tracking functions
    public function get_tracking_by_pembelian_id($id_pembelian)
    {
        $this->db->select('*');
        $this->db->from('pembelian_tracking');
        $this->db->where('id_pembelian', $id_pembelian);
        $this->db->order_by('tanggal_status', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    // Pembayaran functions
    public function get_pembayaran_by_pembelian_id($id_pembelian)
    {
        $this->db->select('*');
        $this->db->from('pembelian_pembayaran');
        $this->db->where('id_pembelian', $id_pembelian);
        $this->db->order_by('tanggal_pembayaran', 'desc');
        $query = $this->db->get();
        return $query->result();
    }

    public function save_pembayaran($data)
    {
        $this->db->insert('pembelian_pembayaran', $data);
        return $this->db->insert_id();
    }

    public function update_pembayaran($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update('pembelian_pembayaran', $data);
        return $this->db->affected_rows();
    }

    public function delete_pembayaran($id)
    {
        $this->db->where('id', $id);
        $this->db->delete('pembelian_pembayaran');
        return $this->db->affected_rows();
    }

    // Export functions
    public function get_all_for_export()
    {
        $this->db->from($this->table);
        $this->_apply_filters();
        $this->db->order_by('tanggal_pembelian', 'desc');
        $query = $this->db->get();
        return $query->result();
    }

    // Dashboard functions
    public function get_summary_stats()
    {
        $stats = array();
        
        // Total pembelian bulan ini
        $this->db->select('COUNT(*) as total, SUM(total_akhir) as nilai');
        $this->db->from($this->table_header);
        $this->db->where('MONTH(tanggal_pembelian)', date('m'));
        $this->db->where('YEAR(tanggal_pembelian)', date('Y'));
        $bulan_ini = $this->db->get()->row();
        $stats['bulan_ini'] = $bulan_ini;
        
        // Total pembelian pending
        $this->db->select('COUNT(*) as total, SUM(total_akhir) as nilai');
        $this->db->from($this->table_header);
        $this->db->where_in('status', ['draft', 'disetujui', 'dipesan']);
        $pending = $this->db->get()->row();
        $stats['pending'] = $pending;
        
        // Total pembelian selesai
        $this->db->select('COUNT(*) as total, SUM(total_akhir) as nilai');
        $this->db->from($this->table_header);
        $this->db->where('status', 'selesai');
        $selesai = $this->db->get()->row();
        $stats['selesai'] = $selesai;
        
        // Pembelian per bulan (6 bulan terakhir)
        $this->db->select('MONTH(tanggal_pembelian) as bulan, YEAR(tanggal_pembelian) as tahun, COUNT(*) as total, SUM(total_akhir) as nilai');
        $this->db->from($this->table_header);
        $this->db->where('tanggal_pembelian >=', date('Y-m-d', strtotime('-6 months')));
        $this->db->group_by('YEAR(tanggal_pembelian), MONTH(tanggal_pembelian)');
        $this->db->order_by('tahun, bulan');
        $per_bulan = $this->db->get()->result();
        $stats['per_bulan'] = $per_bulan;
        
        return $stats;
    }

    // Integration functions
    public function create_barang_masuk_from_pembelian($id_pembelian)
    {
        // Get pembelian data
        $pembelian = $this->get_by_id($id_pembelian);
        if (!$pembelian || $pembelian->status != 'diterima') {
            return false;
        }
        
        // Check if barang masuk already exists
        $this->db->where('jenis', 'pembelian');
        $this->db->where('ref_nomor', $pembelian->nomor_pembelian);
        $existing = $this->db->get('barang_masuk')->row();
        if ($existing) {
            return $existing->id;
        }
        
        // Generate nomor barang masuk
        $nomor_bm = $this->generate_nomor_barang_masuk();
        
        // Create barang masuk header
        $bm_data = array(
            'nomor_penerimaan' => $nomor_bm,
            'tanggal' => date('Y-m-d'),
            'id_supplier' => $pembelian->id_supplier,
            'jenis' => 'pembelian',
            'ref_nomor' => $pembelian->nomor_pembelian,
            'keterangan' => 'Penerimaan barang dari pembelian ' . $pembelian->nomor_pembelian,
            'status' => 'final',
            'created_by' => $this->session->userdata('id_user'),
            'finalized_by' => $this->session->userdata('id_user'),
            'finalized_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->insert('barang_masuk', $bm_data);
        $bm_id = $this->db->insert_id();
        
        // Create barang masuk detail
        $detail_pembelian = $this->get_detail_by_pembelian_id($id_pembelian);
        foreach ($detail_pembelian as $detail) {
            $bmd_data = array(
                'id_barang_masuk' => $bm_id,
                'id_barang' => $detail->id_barang,
                'id_gudang' => $detail->id_gudang,
                'qty_diterima' => $detail->qty,
                'id_satuan' => $detail->satuan_id,
                'harga_satuan' => $detail->harga_satuan,
                'keterangan' => $detail->keterangan
            );
            $this->db->insert('barang_masuk_detail', $bmd_data);
        }
        
        return $bm_id;
    }

    // Generate nomor barang masuk - Format: BM-YYYYMMDD-XXXX
    private function generate_nomor_barang_masuk()
    {
        $prefix = 'BM';
        $date = date('Ymd');
        
        $this->db->select('nomor_penerimaan');
        $this->db->from('barang_masuk');
        $this->db->like('nomor_penerimaan', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_penerimaan', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_penerimaan;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }
}