<form action="#" id="form_detail_item">
    <input type="hidden" name="id" id="detail_item_id">
    <input type="hidden" name="id_faktur_penjualan" value="<?= $id_faktur_penjualan ?>">
    
    <div class="form-group">
        <label>Barang</label>
        <select name="id_barang" id="id_barang" class="form-control select2" style="width: 100%;">
            <option value="">-- Pi<PERSON>h <PERSON> --</option>
            <?php foreach ($barang_list as $barang): ?>
            <option value="<?= $barang->id ?>" 
                    data-harga="<?= $barang->harga_jual ?>"
                    data-pajak="<?= $barang->pajak_persen ?? 11 ?>">
                <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?>
                <?php if ($barang->pajak_persen): ?>
                    <small>(PPN <?= number_format($barang->pajak_persen, 1) ?>%)</small>
                <?php endif; ?>
            </option>
            <?php endforeach; ?>
        </select>
        <span class="help-block text-danger"></span>
    </div>
    
    <div class="form-group">
        <label>Qty</label>
        <input type="number" class="form-control" id="qty" name="qty" min="1" step="1" onchange="updateTotalHarga()">
        <span class="help-block text-danger"></span>
    </div>
    
    <div class="form-group">
        <label>Harga Satuan</label>
        <input type="number" class="form-control" id="harga_satuan" name="harga_satuan" min="0" step="1" onchange="updateTotalHarga()">
        <span class="help-block text-danger"></span>
    </div>
    
    <div class="form-group">
        <label>Diskon (%)</label>
        <input type="number" class="form-control" id="diskon_persen" name="diskon_persen" min="0" max="100" step="1" value="0" onchange="updateTotalHarga()">
        <span class="help-block text-danger"></span>
    </div>
    
    <div class="form-group">
        <label>Pajak (%)</label>
        <input type="number" class="form-control" id="pajak_persen" name="pajak_persen" min="0" max="100" step="0.1" value="11" readonly>
        <small class="text-muted">PPN otomatis dari master barang</small>
        <span class="help-block text-danger"></span>
    </div>
    
    <div class="form-group">
        <label>Subtotal</label>
        <div class="form-control-plaintext">
            <strong id="subtotal_display">0</strong>
        </div>
    </div>
    
    <div class="form-group">
        <label>Total</label>
        <div class="form-control-plaintext">
            <strong id="total_display">0</strong>
            <small class="text-muted ml-2">(Setelah diskon dan pajak)</small>
        </div>
    </div>
    
    <div class="form-group">
        <label>Keterangan</label>
        <textarea class="form-control" id="keterangan" name="keterangan" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
        <span class="help-block text-danger"></span>
    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize select2
    $('.select2').select2();
    
    // Handle barang selection
    $('#id_barang').change(function() {
        var selectedOption = $(this).find(':selected');
        var harga = selectedOption.data('harga') || 0;
        var pajak = selectedOption.data('pajak') || 11;
        
        $('#harga_satuan').val(harga);
        $('#pajak_persen').val(pajak);
        updateTotalHarga();
    });
});

function updateTotalHarga() {
    var qty = parseFloat($('#qty').val()) || 0;
    var harga = parseFloat($('#harga_satuan').val()) || 0;
    var diskon_persen = parseFloat($('#diskon_persen').val()) || 0;
    var pajak_persen = parseFloat($('#pajak_persen').val()) || 0;
    
    var subtotal = qty * harga;
    var diskon_nilai = (diskon_persen / 100) * subtotal;
    var nilai_setelah_diskon = subtotal - diskon_nilai;
    var pajak_nilai = (pajak_persen / 100) * nilai_setelah_diskon;
    var total = nilai_setelah_diskon + pajak_nilai;
    
    $('#subtotal_display').text('Rp ' + formatNumber(subtotal));
    $('#total_display').text('Rp ' + formatNumber(total));
}

// Using common formatNumber function from common_functions.js
</script>