<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="barangKeluarTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="pelanggan-tab" data-toggle="tab" href="#pelanggan" role="tab">Pelanggan & Referensi</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="keterangan-tab" data-toggle="tab" href="#keterangan" role="tab">Keterangan</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="barangKeluarTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="nomor_pengeluaran" class="col-sm-3 col-form-label">Nomor Pengeluaran</label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <input type="text" class="form-control" name="nomor_pengeluaran" id="nomor_pengeluaran" placeholder="Nomor akan di-generate otomatis" autocomplete="off">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="btn-generate-nomor" onclick="generateNomor()" title="Generate Nomor">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Format: BK-2025-01-001, BK-2025-01-002, dst. Kosongkan untuk generate otomatis.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tanggal" class="col-sm-3 col-form-label">Tanggal <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="tanggal" id="tanggal" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="jenis" class="col-sm-3 col-form-label">Jenis <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="jenis" id="jenis" required style="width: 100%;">
                                <option value="">-- Pilih Jenis --</option>
                                <option value="penjualan">Penjualan</option>
                                <option value="retur_pembelian">Retur Pembelian</option>
                                <option value="transfer_keluar">Transfer Keluar</option>
                                <option value="penyesuaian">Penyesuaian</option>
                                <option value="produksi">Produksi</option>
                                <option value="rusak">Rusak</option>
                                <option value="hilang">Hilang</option>
                                <option value="sample">Sample</option>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Pelanggan & Referensi -->
            <div class="tab-pane fade" id="pelanggan" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="id_pelanggan" class="col-sm-3 col-form-label">Pelanggan</label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_pelanggan" id="id_pelanggan" style="width: 100%;">
                                <option value="">-- Pilih Pelanggan --</option>
                                <?php foreach ($pelanggan_list as $pelanggan): ?>
                                    <option value="<?= $pelanggan->id ?>"><?= $pelanggan->kode ?> - <?= $pelanggan->nama ?></option>
                                <?php endforeach; ?>
                            </select>
                            <small class="form-text text-muted">Opsional. Pilih pelanggan jika barang keluar untuk penjualan.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="ref_nomor" class="col-sm-3 col-form-label">Nomor Referensi</label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="ref_nomor" id="ref_nomor" placeholder="Nomor SO, Transfer, dll" autocomplete="off">
                            <small class="form-text text-muted">Nomor dokumen referensi seperti SO, Transfer Order, dll.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Keterangan -->
            <div class="tab-pane fade" id="keterangan" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan tambahan (opsional)" rows="4"></textarea>
                            <small class="form-text text-muted">Informasi tambahan mengenai pengeluaran barang ini.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });
    
    // Event handler untuk perubahan jenis
    $('#jenis').on('change', function() {
        var jenis = $(this).val();
        
        // Show/hide pelanggan field based on jenis
        if (jenis === 'penjualan') {
            $('#id_pelanggan').closest('.form-group').show();
        } else {
            $('#id_pelanggan').closest('.form-group').hide();
            $('#id_pelanggan').val('').trigger('change');
        }
        
        // Update placeholder untuk ref_nomor
        var placeholder = 'Nomor referensi';
        switch(jenis) {
            case 'penjualan':
                placeholder = 'Nomor SO (Sales Order)';
                break;
            case 'transfer_keluar':
                placeholder = 'Nomor Transfer Order';
                break;
            case 'retur_pembelian':
                placeholder = 'Nomor Invoice Pembelian';
                break;
            case 'produksi':
                placeholder = 'Nomor Work Order';
                break;
            case 'rusak':
                placeholder = 'Nomor Laporan Kerusakan';
                break;
            case 'hilang':
                placeholder = 'Nomor Laporan Kehilangan';
                break;
            case 'sample':
                placeholder = 'Nomor Permintaan Sample';
                break;
            default:
                placeholder = 'Nomor referensi dokumen';
                break;
        }
        $('#ref_nomor').attr('placeholder', placeholder);
    });
    
    // Trigger change event untuk set initial state
    $('#jenis').trigger('change');
});
</script>
