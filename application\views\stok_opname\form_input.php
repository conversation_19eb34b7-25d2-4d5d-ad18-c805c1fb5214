<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <div class="form-group row">
            <label for="nomor_opname" class="col-sm-3 col-form-label">Nomor Opname</label>
            <div class="col-sm-9 kosong">
                <div class="input-group">
                    <input type="text" class="form-control" name="nomor_opname" id="nomor_opname" placeholder="Nomor akan di-generate otomatis" autocomplete="off">
                    <div class="input-group-append">
                        <button type="button" class="btn btn-outline-secondary" id="btn-generate-nomor" onclick="generateNomor()" title="Generate Nomor">
                            <i class="fas fa-sync"></i>
                        </button>
                    </div>
                </div>
                <small class="form-text text-muted">Format: SO-2025-001, SO-2025-002, dst. Kosongkan untuk generate otomatis.</small>
                <span class="help-block"></span>
            </div>
        </div>

        <div class="form-group row">
            <label for="tanggal_opname" class="col-sm-3 col-form-label">Tanggal Opname <span class="text-danger">*</span></label>
            <div class="col-sm-9 kosong">
                <input type="date" class="form-control" name="tanggal_opname" id="tanggal_opname" required>
                <span class="help-block"></span>
            </div>
        </div>

        <div class="form-group row">
            <label for="id_gudang" class="col-sm-3 col-form-label">Gudang <span class="text-danger">*</span></label>
            <div class="col-sm-9 kosong">
                <select class="form-control select2" name="id_gudang" id="id_gudang" required style="width: 100%;">
                    <option value="">-- Pilih Gudang --</option>
                    <?php foreach ($gudang_list as $gudang): ?>
                        <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                    <?php endforeach; ?>
                </select>
                <span class="help-block"></span>
            </div>
        </div>

        <div class="form-group row">
            <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
            <div class="col-sm-9 kosong">
                <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan opname (opsional)" rows="3"></textarea>
                <small class="form-text text-muted">Informasi tambahan mengenai stok opname ini.</small>
                <span class="help-block"></span>
            </div>
        </div>

        <!-- Auto Generate Section (hanya untuk add) -->
        <div id="auto_generate_section">
            <hr>
            <h6 class="text-muted">Opsi Auto Generate</h6>
            
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">Auto Generate Detail</label>
                <div class="col-sm-9">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="auto_generate" id="auto_generate" value="1">
                        <label class="form-check-label" for="auto_generate">
                            Generate detail otomatis dari stok sistem
                        </label>
                    </div>
                    <small class="form-text text-muted">
                        Jika dicentang, sistem akan otomatis membuat detail opname berdasarkan stok yang ada di gudang terpilih.
                        Qty fisik akan diisi sama dengan qty sistem sebagai default.
                    </small>
                </div>
            </div>
        </div>

    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });
});
</script>
