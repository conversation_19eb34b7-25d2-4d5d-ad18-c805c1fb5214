<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail PPN Keluaran (Penjualan)</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement/pajak') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            <p>Detail PPN Keluaran (Penjualan) untuk periode <?= date('F Y', mktime(0, 0, 0, $bulan, 1, $tahun)) ?></p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="table-pajak" class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Nomor Faktur</th>
                                        <th>Tanggal Faktur</th>
                                        <th>Pelanggan</th>
                                        <th>DPP (Subtotal)</th>
                                        <th>Diskon</th>
                                        <th>PPN</th>
                                        <th>Total Faktur</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = 1;
                                    $total_dpp = 0;
                                    $total_diskon = 0;
                                    $total_ppn = 0;
                                    $total_faktur = 0;
                                    
                                    if (!empty($transaksi)):
                                        foreach ($transaksi as $row): 
                                            $total_dpp += $row->subtotal;
                                            $total_diskon += $row->diskon;
                                            $total_ppn += $row->pajak;
                                            $total_faktur += $row->total_faktur;
                                    ?>
                                    <tr>
                                        <td><?= $no++ ?></td>
                                        <td><?= $row->nomor_faktur ?></td>
                                        <td><?= date('d-m-Y', strtotime($row->tanggal_faktur)) ?></td>
                                        <td><?= $row->nama_pelanggan ?></td>
                                        <td class="text-right"><?= 'Rp ' . number_format($row->subtotal, 2, ',', '.') ?></td>
                                        <td class="text-right"><?= 'Rp ' . number_format($row->diskon, 2, ',', '.') ?></td>
                                        <td class="text-right"><?= 'Rp ' . number_format($row->pajak, 2, ',', '.') ?></td>
                                        <td class="text-right"><?= 'Rp ' . number_format($row->total_faktur, 2, ',', '.') ?></td>
                                        <td>
                                            <a href="<?= site_url('FakturPenjualan/detail/' . $row->id) ?>" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>
                                        </td>
                                    </tr>
                                    <?php 
                                        endforeach;
                                    else:
                                    ?>
                                    <tr>
                                        <td colspan="9" class="text-center">Tidak ada data PPN Keluaran</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="4" class="text-right">Total</th>
                                        <th class="text-right"><?= 'Rp ' . number_format($total_dpp, 2, ',', '.') ?></th>
                                        <th class="text-right"><?= 'Rp ' . number_format($total_diskon, 2, ',', '.') ?></th>
                                        <th class="text-right"><?= 'Rp ' . number_format($total_ppn, 2, ',', '.') ?></th>
                                        <th class="text-right"><?= 'Rp ' . number_format($total_faktur, 2, ',', '.') ?></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Ringkasan PPN Keluaran</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Jumlah Transaksi</th>
                                        <td class="text-right"><?= count($transaksi) ?></td>
                                    </tr>
                                    <tr>
                                        <th>Total DPP (Subtotal)</th>
                                        <td class="text-right"><?= 'Rp ' . number_format($total_dpp, 2, ',', '.') ?></td>
                                    </tr>
                                    <tr>
                                        <th>Total Diskon</th>
                                        <td class="text-right"><?= 'Rp ' . number_format($total_diskon, 2, ',', '.') ?></td>
                                    </tr>
                                    <tr>
                                        <th>Total PPN</th>
                                        <td class="text-right"><?= 'Rp ' . number_format($total_ppn, 2, ',', '.') ?></td>
                                    </tr>
                                    <tr>
                                        <th>Total Faktur</th>
                                        <td class="text-right"><?= 'Rp ' . number_format($total_faktur, 2, ',', '.') ?></td>
                                    </tr>
                                    <tr>
                                        <th>Rata-rata PPN per Transaksi</th>
                                        <td class="text-right">
                                            <?= count($transaksi) > 0 ? 'Rp ' . number_format($total_ppn / count($transaksi), 2, ',', '.') : 'Rp 0,00' ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Grafik PPN Keluaran</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="ppnChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Catatan Penting</h3>
                            </div>
                            <div class="card-body">
                                <ul>
                                    <li>PPN Keluaran adalah Pajak Pertambahan Nilai yang dipungut perusahaan saat melakukan penjualan barang/jasa kena pajak.</li>
                                    <li>PPN Keluaran harus disetorkan ke kas negara jika nilainya lebih besar dari PPN Masukan.</li>
                                    <li>Pastikan semua faktur pajak keluaran telah dibuat dan diterbitkan dengan benar.</li>
                                    <li>Faktur pajak keluaran harus memenuhi persyaratan formal dan material sesuai ketentuan perpajakan.</li>
                                    <li>Pelaporan PPN Keluaran dilakukan melalui SPT Masa PPN paling lambat akhir bulan berikutnya setelah masa pajak berakhir.</li>
                                </ul>
                                <div class="mt-3">
                                    <a href="<?= site_url('settlement/export/pajak_keluaran?tahun=' . $tahun . '&bulan=' . $bulan) ?>" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#table-pajak').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });
    
    // Chart
    var ctx = document.getElementById('ppnChart').getContext('2d');
    var ppnChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['DPP (Subtotal)', 'Diskon', 'PPN'],
            datasets: [{
                data: [<?= $total_dpp ?>, <?= $total_diskon ?>, <?= $total_ppn ?>],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(75, 192, 192, 0.7)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(75, 192, 192, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            tooltips: {
                callbacks: {
                    label: function(tooltipItem, data) {
                        var dataset = data.datasets[tooltipItem.datasetIndex];
                        var value = dataset.data[tooltipItem.index];
                        return data.labels[tooltipItem.index] + ': Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                    }
                }
            }
        }
    });
});
</script>