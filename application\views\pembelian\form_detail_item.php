<form action="#" id="form_detail_item" class="form-horizontal">
    <input type="hidden" name="id" value="">
    <input type="hidden" name="id_pembelian" value="">
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="id_barang" class="control-label">Barang <span class="text-red">*</span></label>
                <select class="form-control select2" id="id_barang" name="id_barang" style="width: 100%;" required>
                    <option value="">-- Pilih Barang --</option>
                    <?php foreach ($barang_list as $barang): ?>
                        <option value="<?= $barang->id ?>" data-harga="<?= $barang->harga_beli ?>">
                            <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?>
                            <?php if ($barang->merk): ?>(<?= $barang->merk ?>)<?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="id_gudang" class="control-label">Gudang <span class="text-red">*</span></label>
                <select class="form-control select2" id="id_gudang" name="id_gudang" style="width: 100%;" required>
                    <option value="">-- Pilih Gudang --</option>
                    <?php foreach ($gudang_list as $gudang): ?>
                        <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                    <?php endforeach; ?>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label for="qty" class="control-label">Quantity <span class="text-red">*</span></label>
                <input type="number" class="form-control" id="qty" name="qty" 
       placeholder="Minimal 1" min="1" step="1" required>
<span class="text-danger" id="error_qty"></span>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="harga_satuan" class="control-label">Harga Satuan <span class="text-red">*</span></label>
                <input type="number" class="form-control" id="harga_satuan" name="harga_satuan" 
       placeholder="0" min="0" step="1" required readonly>
<span class="text-danger" id="error_harga_satuan"></span>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="subtotal_display" class="control-label">Subtotal</label>
                <input type="text" class="form-control" id="subtotal_display" 
                       placeholder="Rp 0" readonly>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label for="diskon_persen" class="control-label">Diskon (%)</label>
                <input type="number" class="form-control" id="diskon_persen" name="diskon_persen" 
       placeholder="0" min="0" max="100" step="0.1" maxlength="3" >
<span class="text-danger" id="error_diskon_persen"></span>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="diskon_nominal" class="control-label">Diskon (Rp)</label>
                <input type="number" class="form-control" id="diskon_nominal" name="diskon_nominal" 
       placeholder="0" min="0" step="1">
<span class="text-danger" id="error_diskon_nominal"></span>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="ppn_persen" class="control-label">PPN (%)</label>
                <input type="number" class="form-control" id="ppn_persen" name="ppn_persen" 
                       placeholder="11" value="11" min="0" max="100" step="1">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="total_setelah_diskon_display" class="control-label">Total Setelah Diskon</label>
                <input type="text" class="form-control" id="total_setelah_diskon_display" 
                       placeholder="Rp 0" readonly>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="total_akhir_display" class="control-label">Total Akhir (+ PPN)</label>
                <input type="text" class="form-control" id="total_akhir_display" 
                       placeholder="Rp 0" readonly style="font-weight: bold;">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label for="keterangan" class="control-label">Keterangan</label>
                <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                          placeholder="Keterangan tambahan untuk item ini"></textarea>
                <span class="help-block"></span>
            </div>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    // Validasi komprehensif input
    function validateInputs() {
        let valid = true;
        // Diskon persen
        let diskonPersenInput = $('#diskon_persen');
        let diskonPersen = parseFloat(diskonPersenInput.val()) || 0;
        if (diskonPersen < 0) {
            diskonPersenInput.val(0);
            $('#error_diskon_persen').text('Diskon persen minimal 0');
            valid = false;
        } else if (diskonPersen > 100) {
            diskonPersenInput.val(100);
            $('#error_diskon_persen').text('Diskon persen maksimal 100');
            valid = false;
        } else {
            $('#error_diskon_persen').text('');
        }
        // Diskon nominal
        let subtotal = parseFloat($('#qty').val() || 0) * parseFloat($('#harga_satuan').val() || 0);
        let diskonNominal = parseFloat($('#diskon_nominal').val()) || 0;
        if (diskonNominal < 0) {
            $('#diskon_nominal').val(0);
            $('#error_diskon_nominal').text('Diskon nominal minimal 0');
            valid = false;
        } else if (diskonNominal > subtotal) {
            $('#diskon_nominal').val(subtotal);
            $('#error_diskon_nominal').text('Diskon nominal maksimal sebesar subtotal');
            valid = false;
        } else {
            $('#error_diskon_nominal').text('');
        }
        // Quantity
        let qtyInput = $('#qty');
        let qty = parseInt(qtyInput.val());
        
        // Jika input kosong atau NaN, biarkan kosong untuk sementara
        if (isNaN(qty)) {
            $('#error_qty').text('');
        } else if (qty < 1) {
            $('#error_qty').text('Quantity minimal 1');
            valid = false;
        } else {
            $('#error_qty').text('');
        }
        // Harga satuan
        let hargaSatuan = parseFloat($('#harga_satuan').val()) || 0;
        if (hargaSatuan < 0) {
            $('#harga_satuan').val(0);
            $('#error_harga_satuan').text('Harga satuan minimal 0');
            valid = false;
        } else {
            $('#error_harga_satuan').text('');
        }
        // Subtotal dan total tidak boleh negatif
        if (subtotal < 0) {
            $('#subtotal_display').val('Rp 0');
            valid = false;
        }
        return valid;
    }

    $('.select2').select2({
        dropdownParent: $('#modal_detail_item')
    });

    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var hargaBeli = selectedOption.data('harga');
        if (hargaBeli) {
            $('#harga_satuan').val(hargaBeli);
            calculateTotal();
        }
    });

    let isCalculating = false;
    let activeDiscountField = 'diskon_persen'; // Default ke persen sebagai prioritas

    // Event handler untuk quantity - mencegah nilai negatif
    $('#qty').on('input', function() {
        var value = parseInt($(this).val());
        if (!isNaN(value) && value < 0) {
            $(this).val('');
        }
    });
    
    // Event handler untuk quantity - validasi saat blur (kehilangan fokus)
    $('#qty').on('blur', function() {
        var value = parseInt($(this).val());
        if (!isNaN(value) && value < 1) {
            $('#error_qty').text('Quantity minimal 1');
        } else if (!isNaN(value)) {
            $('#error_qty').text('');
        }
    });

    // Event handler tunggal untuk perhitungan otomatis
    $('#qty, #harga_satuan, #diskon_persen, #diskon_nominal, #ppn_persen').on('input', function(e) {
        // Tentukan field diskon mana yang terakhir diubah oleh pengguna
        if (e.target.id === 'diskon_persen' || e.target.id === 'diskon_nominal') {
            activeDiscountField = e.target.id;
        }
        calculateTotal();
    });

    // Fungsi untuk menghitung total
    function calculateTotal() {
        // Mencegah pemanggilan rekursif/berulang yang menyebabkan loop
        if (isCalculating) return;
        isCalculating = true;

        // Validasi dan koreksi nilai input
        validateInputs();

        // Baca ulang semua nilai setelah validasi
        var qty = parseFloat($('#qty').val());
        var hargaSatuan = parseFloat($('#harga_satuan').val()) || 0;
        var diskonPersen = parseFloat($('#diskon_persen').val()) || 0;
        var diskonNominal = parseFloat($('#diskon_nominal').val()) || 0;
        var ppnPersen = parseFloat($('#ppn_persen').val()) || 0;
        
        // Jika qty kosong atau NaN, set ke 0 untuk perhitungan
        if (isNaN(qty)) {
            qty = 0;
        }

        var subtotal = qty * hargaSatuan;
        $('#subtotal_display').val(formatRupiah(subtotal));

        var totalDiskon = 0;
        // Logika sinkronisasi diskon
        if (activeDiscountField === 'diskon_persen') {
            totalDiskon = (subtotal * diskonPersen) / 100;
            $('#diskon_nominal').val(totalDiskon);
        } else { // activeDiscountField === 'diskon_nominal'
            if ($('#diskon_nominal').val() === '' || diskonNominal === 0) {
                $('#diskon_persen').val('');
                totalDiskon = 0;
            } else {
                totalDiskon = diskonNominal;
                if (subtotal > 0) {
                    var persenFromNominal = (diskonNominal / subtotal) * 100;
                    $('#diskon_persen').val(persenFromNominal.toFixed(6));
                } else {
                    $('#diskon_persen').val('');
                }
            }
        }

        var totalSetelahDiskon = subtotal - totalDiskon;
        $('#total_setelah_diskon_display').val(formatRupiah(totalSetelahDiskon));

        var ppnNominal = (totalSetelahDiskon * ppnPersen) / 100;
        var totalAkhir = totalSetelahDiskon + ppnNominal;
        $('#total_akhir_display').val(formatRupiah(totalAkhir));

        // Selesai kalkulasi, izinkan pemanggilan berikutnya
        isCalculating = false;
    }

    // Fungsi untuk format rupiah
    function formatRupiah(angka) {
        var number_string = angka.toString().replace(/[^,\d]/g, '');
        var split = number_string.split(',');
        var sisa = split[0].length % 3;
        var rupiah = split[0].substr(0, sisa);
        var ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            var separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }

    // Prevent form submission on enter
    $('#form_detail_item').on('keypress', function(e) {
        if (e.which == 13) {
            e.preventDefault();
            return false;
        }
    });
});
</script>