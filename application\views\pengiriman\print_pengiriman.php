<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Pengiriman',
    'document_number' => $pengiriman->nomor_pengiriman,
    'document_title' => 'SURAT JALAN PENGIRIMAN'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Pengiriman',
        'items' => [
            ['label' => 'Nomor Pengiriman', 'value' => '<strong>' . $pengiriman->nomor_pengiriman . '</strong>'],
            ['label' => 'Tanggal Pengiriman', 'value' => format_date_indonesia($pengiriman->tanggal_pengiriman)],
            ['label' => 'Status', 'value' => get_status_badge($pengiriman->status)],
            ['label' => 'Jenis Pengiriman', 'value' => ucfirst($pengiriman->jenis_pengiriman ?? 'reguler')]
        ]
    ],
    [
        'title' => '<span class="icon-user"></span>Informasi Penerima',
        'items' => [
            ['label' => 'Nama Penerima', 'value' => '<strong>' . $pengiriman->nama_pelanggan . '</strong>'],
            ['label' => 'Alamat Pengiriman', 'value' => $pengiriman->alamat_pengiriman],
            ['label' => 'Telepon', 'value' => $pengiriman->no_telepon ?: '-'],
            ['label' => 'Email', 'value' => $pengiriman->email ?? '-']
        ]
    ]
];

// Add shipping details if available
if (!empty($pengiriman->ekspedisi) || !empty($pengiriman->no_resi)) {
    $shipping_items = [];
    if (!empty($pengiriman->ekspedisi)) {
        $shipping_items[] = ['label' => 'Ekspedisi', 'value' => $pengiriman->ekspedisi];
    }
    if (!empty($pengiriman->no_resi)) {
        $shipping_items[] = ['label' => 'No. Resi', 'value' => $pengiriman->no_resi];
    }
    if (!empty($pengiriman->estimasi_tiba)) {
        $shipping_items[] = ['label' => 'Estimasi Tiba', 'value' => format_date_indonesia($pengiriman->estimasi_tiba)];
    }
    if (!empty($pengiriman->biaya_kirim)) {
        $shipping_items[] = ['label' => 'Biaya Kirim', 'value' => format_currency($pengiriman->biaya_kirim)];
    }
    
    if (!empty($shipping_items)) {
        $info_sections[] = [
            'title' => '<span class="icon-truck"></span>Detail Pengiriman',
            'items' => $shipping_items
        ];
    }
}

// Add reference documents if available
if (!empty($pengiriman->nomor_pesanan) || !empty($pengiriman->nomor_faktur)) {
    $ref_items = [];
    if (!empty($pengiriman->nomor_pesanan)) {
        $ref_items[] = ['label' => 'No. Pesanan', 'value' => $pengiriman->nomor_pesanan];
    }
    if (!empty($pengiriman->nomor_faktur)) {
        $ref_items[] = ['label' => 'No. Faktur', 'value' => $pengiriman->nomor_faktur];
    }
    
    $info_sections[] = [
        'title' => '<span class="icon-document"></span>Dokumen Referensi',
        'items' => $ref_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '15%'],
    ['label' => 'Nama Barang', 'width' => '35%'],
    ['label' => 'Satuan', 'width' => '10%', 'align' => 'center'],
    ['label' => 'Qty Kirim', 'width' => '10%', 'align' => 'right'],
    ['label' => 'Kondisi', 'width' => '10%', 'align' => 'center'],
    ['label' => 'Keterangan', 'width' => '15%']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_qty = 0;

if (!empty($pengiriman_detail)) {
    foreach ($pengiriman_detail as $detail) {
        $nama_barang = $detail->nama_barang;
        if ($detail->merk) {
            $nama_barang .= ' - ' . $detail->merk;
        }
        if ($detail->tipe) {
            $nama_barang .= ' (' . $detail->tipe . ')';
        }
        
        // Color coding untuk kondisi
        $kondisi = ucfirst($detail->kondisi_barang ?? 'baik');
        if (($detail->kondisi_barang ?? 'baik') == 'rusak') {
            $kondisi = '<span class="color-danger">' . $kondisi . '</span>';
        } elseif (($detail->kondisi_barang ?? 'baik') == 'baik') {
            $kondisi = '<span class="color-success">' . $kondisi . '</span>';
        }
        
        $table_data[] = [
            $no++,
            $detail->kode_barang,
            $nama_barang,
            $detail->nama_satuan,
            number_format($detail->qty_dikirim, 0, ',', '.'),
            $kondisi,
            !empty($detail->keterangan) ? $detail->keterangan : '-'
        ];
        
        $total_qty += $detail->qty_dikirim;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL ITEM:</strong>', 'colspan' => '4', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '', 'colspan' => '2']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Barang Dikirim', $table_headers, $table_data, $table_options);

// Add notes if available
if (!empty($pengiriman->keterangan)) {
    $content .= create_notes_section('Keterangan Pengiriman', $pengiriman->keterangan);
}

// Add shipping instructions
$content .= create_notes_section('Petunjuk Pengiriman', 
    '• Barang harus dikirim dalam kondisi baik dan sesuai dengan daftar' . "\n" .
    '• Penerima wajib memeriksa kondisi barang sebelum menandatangani' . "\n" .
    '• Laporkan segera jika ada kerusakan atau kekurangan barang' . "\n" .
    '• Simpan dokumen ini sebagai bukti pengiriman yang sah' . "\n" .
    '• Hubungi customer service untuk informasi lebih lanjut');

// Signatures
$signatures = [
    [
        'title' => 'Pengirim',
        'name' => $pengiriman->pengirim ?? '(............................)',
        'position' => 'Gudang Pengiriman'
    ],
    [
        'title' => 'Kurir',
        'name' => $pengiriman->kurir ?? '(............................)',
        'position' => $pengiriman->ekspedisi ?? 'Ekspedisi'
    ],
    [
        'title' => 'Penerima',
        'name' => '(............................)',
        'position' => 'Tgl: ___/___/______'
    ]
];

$content .= create_signature_section($signatures);

// Add tracking info if available
if (!empty($pengiriman->no_resi)) {
    $content .= '<div class="notes-section mt-20">';
    $content .= '<div class="text-center">';
    $content .= '<div class="font-bold mb-10">INFORMASI TRACKING</div>';
    $content .= '<div class="text-large font-bold color-primary">No. Resi: ' . $pengiriman->no_resi . '</div>';
    if (!empty($pengiriman->ekspedisi)) {
        $content .= '<div class="color-muted">Ekspedisi: ' . $pengiriman->ekspedisi . '</div>';
    }
    $content .= '</div>';
    $content .= '</div>';
}

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>