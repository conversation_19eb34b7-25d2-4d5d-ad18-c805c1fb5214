<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller <PERSON><PERSON>
 * Mengatur penerimaan barang masuk dan detailnya
 */
class BarangMasuk extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_barang_masuk', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'barang_masuk/barang_masuk', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_barang_masuk->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $barang_masuk) {
            $no++;
            $row = array();
            $row[] = $barang_masuk->nomor_penerimaan;
            $row[] = date('d/m/Y', strtotime($barang_masuk->tanggal));
            $row[] = $barang_masuk->nama_supplier ? 
                     $barang_masuk->nama_supplier . '<br><small class="text-muted">' . $barang_masuk->kode_supplier . '</small>' : 
                     '<span class="text-muted">-</span>';
            
            // Jenis dengan badge
            $jenis_class = 'badge-primary';
            switch($barang_masuk->jenis) {
                case 'pembelian': $jenis_class = 'badge-primary'; break;
                case 'retur_penjualan': $jenis_class = 'badge-info'; break;
                case 'bonus': $jenis_class = 'badge-success'; break;
                case 'transfer_masuk': $jenis_class = 'badge-warning'; break;
                default: $jenis_class = 'badge-secondary'; break;
            }
            $row[] = '<span class="badge ' . $jenis_class . '">' . strtoupper(str_replace('_', ' ', $barang_masuk->jenis)) . '</span>';
            
            $row[] = $barang_masuk->ref_nomor ?: '-';
            
            // Status dengan badge
            $status_class = $barang_masuk->status == 'final' ? 'badge-success' : 'badge-warning';
            $row[] = '<span class="badge ' . $status_class . '">' . strtoupper($barang_masuk->status) . '</span>';
            
            $row[] = number_format($barang_masuk->total_item, 0);
            $row[] = number_format($barang_masuk->total_qty, 0);

            // Action buttons
            $actions = '';
            if ($barang_masuk->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $barang_masuk->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $barang_masuk->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success finalize" href="javascript:void(0)" title="Finalisasi" onclick="finalize(' . $barang_masuk->id . ')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $barang_masuk->id . ')"><i class="fas fa-trash"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $barang_masuk->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="print_barang_masuk(' . $barang_masuk->id . ')"><i class="fas fa-print"></i></a>';
            }
            
            $row[] = $actions;
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_barang_masuk->count_all(),
            "recordsFiltered" => $this->Mod_barang_masuk->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_penerimaan');
        if (empty($nomor)) {
            $nomor = $this->Mod_barang_masuk->generate_nomor();
        }

        $save = array(
            'nomor_penerimaan' => $nomor,
            'tanggal' => $this->input->post('tanggal'),
            'id_supplier' => $this->input->post('id_supplier') ?: null,
            'jenis' => $this->input->post('jenis'),
            'ref_nomor' => $this->input->post('ref_nomor'),
            'keterangan' => $this->input->post('keterangan'),
            'created_by' => $this->session->userdata('id_user'),
        );
        
        $id_barang_masuk = $this->Mod_barang_masuk->insert($save);
        
        echo json_encode(array(
            "status" => TRUE,
            "id_barang_masuk" => $id_barang_masuk
        ));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');

        $save = array(
            'tanggal' => $this->input->post('tanggal'),
            'id_supplier' => $this->input->post('id_supplier') ?: null,
            'jenis' => $this->input->post('jenis'),
            'ref_nomor' => $this->input->post('ref_nomor'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_barang_masuk->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_barang_masuk->get($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['supplier_list'] = $this->Mod_barang_masuk->get_supplier_dropdown();
        $this->load->view('barang_masuk/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_barang_masuk->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_barang_masuk->generate_nomor();
        echo json_encode(array('nomor' => $nomor));
    }

    // Finalisasi barang masuk
    public function finalize()
    {
        $id = $this->input->post('id');
        $user_final = $this->session->userdata('id_user');
        
        $result = $this->Mod_barang_masuk->finalize_barang_masuk($id, $user_final);
        
        if ($result) {
            echo json_encode(array("status" => TRUE, "message" => "Barang masuk berhasil difinalisasi"));
        } else {
            echo json_encode(array("status" => FALSE, "message" => "Gagal finalisasi barang masuk"));
        }
    }

    // ===== DETAIL BARANG MASUK METHODS =====

    public function detail($id)
    {
        $data['barang_masuk'] = $this->Mod_barang_masuk->get($id);
        $data['detail_list'] = $this->Mod_barang_masuk->get_detail($id);
        $data['barang_list'] = $this->Mod_barang_masuk->get_barang_dropdown();
        $data['gudang_list'] = $this->Mod_barang_masuk->get_gudang_dropdown();
        $data['satuan_list'] = $this->Mod_barang_masuk->get_satuan_dropdown();
        $this->load->view('barang_masuk/detail_barang_masuk', $data);
    }

    public function insert_detail()
    {
        $this->_validate_detail();

        $save = array(
            'id_barang_masuk' => $this->input->post('id_barang_masuk'),
            'id_barang' => $this->input->post('id_barang'),
            'id_gudang' => $this->input->post('id_gudang'),
            'qty_diterima' => $this->input->post('qty_diterima'),
            'id_satuan' => $this->input->post('id_satuan'),
            'keterangan' => $this->input->post('keterangan'),
        );
        
        $this->Mod_barang_masuk->insert_detail($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $this->_validate_detail();

        $id = $this->input->post('id');

        $save = array(
            'id_barang' => $this->input->post('id_barang'),
            'id_gudang' => $this->input->post('id_gudang'),
            'qty_diterima' => $this->input->post('qty_diterima'),
            'id_satuan' => $this->input->post('id_satuan'),
            'keterangan' => $this->input->post('keterangan'),
        );

        $this->Mod_barang_masuk->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_barang_masuk->get_detail_by_id($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $this->Mod_barang_masuk->delete_detail($id);
        echo json_encode(array("status" => TRUE));
    }

    public function get_barang_detail()
    {
        $id_barang = $this->input->post('id_barang');
        $data = $this->Mod_barang_masuk->get_barang_detail($id_barang);
        echo json_encode($data);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $nomor = $this->input->post('nomor_penerimaan');
        $id = $this->input->post('id');

        // Validasi nomor penerimaan
        if (empty($nomor)) {
            // Nomor boleh kosong, akan di-generate otomatis
        } else {
            if (!preg_match('/^BM-\d{8}-\d{4}$/', $nomor)) {
                $data['inputerror'][] = 'nomor_penerimaan';
                $data['error_string'][] = 'Format nomor harus BM-YYYYMMDD-XXXX, contoh: BM-20250628-0001';
                $data['status'] = FALSE;
            } else if ($this->Mod_barang_masuk->check_nomor_exists($nomor, $id)) {
                $data['inputerror'][] = 'nomor_penerimaan';
                $data['error_string'][] = 'Nomor penerimaan sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi tanggal
        if (empty($this->input->post('tanggal'))) {
            $data['inputerror'][] = 'tanggal';
            $data['error_string'][] = 'Tanggal wajib diisi';
            $data['status'] = FALSE;
        }

        // Validasi jenis
        if (empty($this->input->post('jenis'))) {
            $data['inputerror'][] = 'jenis';
            $data['error_string'][] = 'Jenis barang masuk wajib dipilih';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    private function _validate_detail()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        // Validasi barang
        if (empty($this->input->post('id_barang'))) {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi gudang
        if (empty($this->input->post('id_gudang'))) {
            $data['inputerror'][] = 'id_gudang';
            $data['error_string'][] = 'Gudang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi qty diterima
        $qty_diterima = $this->input->post('qty_diterima');
        if (empty($qty_diterima) && $qty_diterima !== '0') {
            $data['inputerror'][] = 'qty_diterima';
            $data['error_string'][] = 'Qty diterima wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty_diterima) || $qty_diterima <= 0) {
            $data['inputerror'][] = 'qty_diterima';
            $data['error_string'][] = 'Qty diterima harus berupa angka positif';
            $data['status'] = FALSE;
        }



        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Get detail items untuk refresh tabel
    public function get_detail_items($id_barang_masuk)
    {
        $data = $this->Mod_barang_masuk->get_detail($id_barang_masuk);
        echo json_encode($data);
    }

    // Print Barang Masuk
    public function cetak_barang_masuk($id)
    {
        $data['barang_masuk'] = $this->Mod_barang_masuk->get($id);
        $data['detail_list'] = $this->Mod_barang_masuk->get_detail($id);
        $this->load->view('barang_masuk/cetak_barang_masuk', $data);
    }
}
