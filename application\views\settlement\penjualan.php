<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Settlement Penjualan (Faktur vs Pengiriman)</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi</h5>
                            Settlement Penjualan digunakan untuk memastikan bahwa semua pengiriman barang telah difakturkan dengan benar.
                            Proses ini akan membandingkan data pengiriman dengan faktur penjualan untuk mengidentifikasi perbedaan atau kesalahan.
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card card-outline card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Filter Data</h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="form-filter" class="form-horizontal">
                                    <div class="form-group row">
                                        <label for="tanggal_awal" class="col-sm-2 col-form-label">Tanggal Pengiriman</label>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="date" class="form-control" id="tanggal_awal" name="tanggal_awal" value="<?= $tanggal_awal ?>">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">s/d</span>
                                                </div>
                                                <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?= $tanggal_akhir ?>">
                                            </div>
                                        </div>
                                        <label for="status" class="col-sm-2 col-form-label">Status Faktur</label>
                                        <div class="col-sm-4">
                                            <select class="form-control" id="status" name="status">
                                                <option value="">Semua Status</option>
                                                <option value="belum_difakturkan">Belum Difakturkan</option>
                                                <option value="sudah_difakturkan">Sudah Difakturkan</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="offset-sm-2 col-sm-10">
                                            <button type="button" id="btn-filter" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                                            <button type="button" id="btn-reset" class="btn btn-default"><i class="fas fa-undo"></i> Reset</button>
                                            <button type="button" id="btn-export" class="btn btn-success"><i class="fas fa-file-excel"></i> Export Excel</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="table-settlement" class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Nomor Pengiriman</th>
                                        <th>Tanggal Pengiriman</th>
                                        <th>Pelanggan</th>
                                        <th>Total Item</th>
                                        <th>Total Qty</th>
                                        <th>Status Faktur</th>
                                        <th>Nomor Faktur</th>
                                        <th>Tanggal Faktur</th>
                                        <th>Selisih Item</th>
                                        <th>Selisih Qty</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var table = $('#table-settlement').DataTable({
        "processing": true,
        "serverSide": false,
        "ajax": {
            "url": "<?= site_url('settlement/penjualan_data') ?>",
            "type": "POST",
            "data": function(data) {
                data.tanggal_awal = $('#tanggal_awal').val();
                data.tanggal_akhir = $('#tanggal_akhir').val();
                data.status = $('#status').val();
            },
            "dataSrc": "data"
        },
        "columns": [
            { "data": null, "orderable": false, "render": function(data, type, row, meta) {
                return meta.row + 1;
            }},
            { "data": "nomor_pengiriman" },
            { "data": "tanggal_pengiriman" },
            { "data": "nama_pelanggan" },
            { "data": "total_item" },
            { "data": "total_qty" },
            { "data": null, "render": function(data, type, row) {
                if (row.status_faktur == 'belum_difakturkan') {
                    return '<span class="badge badge-danger">Belum Difakturkan</span>';
                } else {
                    return '<span class="badge badge-success">Sudah Difakturkan</span>';
                }
            }},
            { "data": "nomor_faktur", "render": function(data, type, row) {
                return data ? data : '-';
            }},
            { "data": "tanggal_faktur", "render": function(data, type, row) {
                return data ? data : '-';
            }},
            { "data": "selisih_item", "render": function(data, type, row) {
                if (row.status_faktur == 'belum_difakturkan') {
                    return '-';
                } else if (data == 0) {
                    return '<span class="text-success">0</span>';
                } else {
                    return '<span class="text-danger">' + data + '</span>';
                }
            }},
            { "data": "selisih_qty", "render": function(data, type, row) {
                if (row.status_faktur == 'belum_difakturkan') {
                    return '-';
                } else if (data == 0) {
                    return '<span class="text-success">0</span>';
                } else {
                    return '<span class="text-danger">' + data + '</span>';
                }
            }},
            { "data": null, "orderable": false, "render": function(data, type, row) {
                var html = '<div class="btn-group">';
                html += '<a href="<?= site_url('settlement/penjualan_detail/') ?>' + row.id + '" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>';
                if (row.status_faktur == 'belum_difakturkan') {
                    html += '<a href="<?= site_url('settlement/penjualan_create_faktur/') ?>' + row.id + '" class="btn btn-success btn-sm"><i class="fas fa-file-invoice"></i> Buat Faktur</a>';
                }
                html += '</div>';
                return html;
            }}
        ],
        "order": [[2, 'desc']],
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    $('#btn-filter').click(function() {
        table.ajax.reload();
    });

    $('#btn-reset').click(function() {
        $('#form-filter')[0].reset();
        table.ajax.reload();
    });

    $('#btn-export').click(function() {
        var tanggal_awal = $('#tanggal_awal').val();
        var tanggal_akhir = $('#tanggal_akhir').val();
        var status = $('#status').val();
        
        window.location.href = '<?= site_url('settlement/export/penjualan') ?>?tanggal_awal=' + tanggal_awal + '&tanggal_akhir=' + tanggal_akhir + '&status=' + status;
    });
});
</script>