<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Stok Opname
 * Mengatur data stok opname dan detailnya
 */
class Mod_stok_opname extends CI_Model
{
    var $table = 'stok_opname';
    var $table_detail = 'stok_opname_detail';
    var $column_search = array(
        'so.nomor_opname', 
        'so.tanggal_opname', 
        'g.nama_gudang', 
        'so.status', 
        'so.keterangan'
    );
    var $column_order = array(
        'so.id', 
        'so.nomor_opname', 
        'so.tanggal_opname', 
        'g.nama_gudang', 
        'so.status', 
        'so.total_item',
        'so.total_selisih_positif',
        'so.total_selisih_negatif'
    );
    var $order = array('so.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            so.id,
            so.nomor_opname,
            so.tanggal_opname,
            so.id_gudang,
            so.status,
            so.total_item,
            so.total_selisih_positif,
            so.total_selisih_negatif,
            so.keterangan,
            so.user_input,
            so.user_final,
            so.tanggal_final,
            g.kode_gudang,
            g.nama_gudang
        ');
        $this->db->from('stok_opname so');
        $this->db->join('gudang g', 'so.id_gudang = g.id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from('stok_opname so');
        $this->db->join('gudang g', 'so.id_gudang = g.id', 'left');
        return $this->db->count_all_results();
    }

    // Insert header opname
    function insert($data)
    {
        $insert = $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // Update header opname
    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    // Get header opname
    function get($id)
    {
        $this->db->select('
            so.*,
            g.kode_gudang,
            g.nama_gudang
        ');
        $this->db->from('stok_opname so');
        $this->db->join('gudang g', 'so.id_gudang = g.id', 'left');
        $this->db->where('so.id', $id);
        return $this->db->get()->row();
    }

    // Delete header opname (cascade delete detail)
    function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Generate nomor opname otomatis - Format: SOP-YYYYMMDD-XXXX (Stok Opname)
    function generate_nomor()
    {
        $prefix = 'SOP';
        $date = date('Ymd');
        
        $this->db->select('nomor_opname');
        $this->db->from($this->table);
        $this->db->like('nomor_opname', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_opname', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $last_nomor = $query->row()->nomor_opname;
            $last_sequence = (int)substr($last_nomor, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }

        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    // Cek apakah nomor sudah ada
    function check_nomor_exists($nomor, $id = null)
    {
        $this->db->where('nomor_opname', $nomor);
        if ($id) {
            $this->db->where('id !=', $id);
        }
        $query = $this->db->get($this->table);
        return $query->num_rows() > 0;
    }

    // Get dropdown gudang
    function get_gudang_dropdown()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'ASC');
        $query = $this->db->get();
        return $query->result();
    }

    // ===== DETAIL OPNAME METHODS =====

    // Get detail opname
    function get_detail($id_opname)
    {
        $this->db->select('
            sod.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe
        ');
        $this->db->from('stok_opname_detail sod');
        $this->db->join('barang b', 'sod.id_barang = b.id', 'left');
        $this->db->where('sod.id_opname', $id_opname);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Insert detail opname
    function insert_detail($data)
    {
        return $this->db->insert($this->table_detail, $data);
    }

    // Update detail opname
    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    // Delete detail opname
    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    // Get single detail
    function get_detail_by_id($id)
    {
        $this->db->select('
            sod.*,
            b.kode_barang,
            b.nama_barang
        ');
        $this->db->from('stok_opname_detail sod');
        $this->db->join('barang b', 'sod.id_barang = b.id', 'left');
        $this->db->where('sod.id', $id);
        return $this->db->get()->row();
    }

    // Load stok sistem untuk opname
    function load_stok_sistem($id_gudang)
    {
        $this->db->select('
            sb.id_barang,
            sb.qty_terakhir as qty_sistem,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe
        ');
        $this->db->from('stok_barang sb');
        $this->db->join('barang b', 'sb.id_barang = b.id', 'left');
        $this->db->where('sb.id_gudang', $id_gudang);
        $this->db->where('b.aktif', 1);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Auto generate detail dari stok sistem
    function auto_generate_detail($id_opname, $id_gudang)
    {
        // Hapus detail yang sudah ada
        $this->db->where('id_opname', $id_opname);
        $this->db->delete($this->table_detail);

        // Get stok sistem
        $stok_data = $this->load_stok_sistem($id_gudang);

        if (!empty($stok_data)) {
            $batch_data = array();
            foreach ($stok_data as $stok) {
                $batch_data[] = array(
                    'id_opname' => $id_opname,
                    'id_barang' => $stok->id_barang,
                    'qty_sistem' => $stok->qty_sistem,
                    'qty_fisik' => $stok->qty_sistem, // Default sama dengan sistem
                    'keterangan' => ''
                );
            }
            
            if (!empty($batch_data)) {
                $this->db->insert_batch($this->table_detail, $batch_data);
                return count($batch_data);
            }
        }
        
        return 0;
    }

    // Finalisasi opname
    function finalize_opname($id, $user_final)
    {
        $data = array(
            'status' => 'final',
            'user_final' => $user_final,
            'tanggal_final' => date('Y-m-d H:i:s')
        );
        
        $this->db->where('id', $id);
        $this->db->where('status', 'draft'); // Hanya bisa finalisasi jika masih draft
        $this->db->update($this->table, $data);
        
        return $this->db->affected_rows() > 0;
    }

    // Get barang untuk dropdown (yang belum ada di detail)
    function get_barang_available($id_opname)
    {
        $this->db->select('b.id, b.kode_barang, b.nama_barang');
        $this->db->from('barang b');
        $this->db->where('b.aktif', 1);
        $this->db->where('b.id NOT IN (
            SELECT id_barang FROM stok_opname_detail 
            WHERE id_opname = ' . (int)$id_opname . '
        )');
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Get stok terakhir barang di gudang untuk opname
    function get_stok_sistem_barang($id_barang, $id_gudang)
    {
        $this->db->select('qty_terakhir');
        $this->db->from('stok_barang');
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $result = $this->db->get()->row();
        
        return $result ? $result->qty_terakhir : 0;
    }
}
