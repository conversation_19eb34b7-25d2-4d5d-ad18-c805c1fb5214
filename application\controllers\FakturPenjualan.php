<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Faktur Penjualan
 * Mengatur faktur penjualan dan detailnya
 * Konsisten dengan modul pesanan dan pengiriman
 */
class FakturPenjualan extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_faktur_penjualan', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'faktur_penjualan/faktur_penjualan', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_faktur_penjualan->get_datatables();
        $data = array();
        foreach ($list as $faktur) {
            $row = array();
            $row[] = $faktur->nomor_faktur;
            $row[] = date('d/m/Y', strtotime($faktur->tanggal_faktur));
            $row[] = $faktur->nomor_pengiriman;
            $row[] = $faktur->nama_pelanggan . ' (' . $faktur->kode_pelanggan . ')';
            
            // Status badge
            switch ($faktur->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-warning">Draft</span>';
                    break;
                case 'final':
                    $status_badge = '<span class="badge badge-success">Final</span>';
                    break;
                case 'batal':
                    $status_badge = '<span class="badge badge-danger">Batal</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-secondary">' . ucfirst($faktur->status) . '</span>';
            }
            $row[] = $status_badge;
            
            // Status pembayaran badge
            switch ($faktur->status_pembayaran) {
                case 'belum_bayar':
                    $pembayaran_badge = '<span class="badge badge-danger">Belum Bayar</span>';
                    break;
                case 'sebagian':
                    $pembayaran_badge = '<span class="badge badge-warning">Sebagian</span>';
                    break;
                case 'lunas':
                    $pembayaran_badge = '<span class="badge badge-success">Lunas</span>';
                    break;
                default:
                    $pembayaran_badge = '<span class="badge badge-secondary">' . ucfirst($faktur->status_pembayaran) . '</span>';
            }
            $row[] = $pembayaran_badge;
            
            $row[] = number_format($faktur->total_item ?? 0, 0) . ' item';
            $row[] = number_format($faktur->total_qty ?? 0, 0);
            $row[] = 'Rp ' . number_format($faktur->total_faktur ?? 0, 0, ',', '.');
            
            // Action buttons
            $actions = '';
            if ($faktur->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $faktur->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $faktur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success finalize" href="javascript:void(0)" title="Finalisasi" onclick="updateStatus(' . $faktur->id . ', \'final\')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $faktur->id . ')"><i class="fas fa-trash"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $faktur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printFaktur(' . $faktur->id . ')"><i class="fas fa-print"></i></a>';
                
                if ($faktur->status == 'final' && $faktur->status_pembayaran != 'lunas') {
                    $actions .= ' <a class="btn btn-xs btn-outline-success payment" href="javascript:void(0)" title="Pembayaran" onclick="addPayment(' . $faktur->id . ')"><i class="fas fa-money-bill"></i></a>';
                }
            }

            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_faktur_penjualan->count_all(),
            "recordsFiltered" => $this->Mod_faktur_penjualan->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    // Method untuk form input modal
    public function form_input()
    {
        // Load dropdown data untuk form
        $data['pengiriman_list'] = $this->Mod_faktur_penjualan->get_pengiriman_diterima();
        $data['nomor_faktur'] = $this->Mod_faktur_penjualan->generate_nomor_faktur();
        $this->load->view('faktur_penjualan/form_input', $data);
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_faktur_penjualan->generate_nomor_faktur();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_pengiriman_info()
    {
        $id = $this->input->post('id');
        $pengiriman = $this->Mod_faktur_penjualan->get_pengiriman_by_id($id);

        if ($pengiriman) {
            echo json_encode(array('status' => true, 'data' => $pengiriman));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data pengiriman tidak ditemukan'));
        }
    }

    public function get_pengiriman_items()
    {
        $id = $this->input->post('id');
        $items = $this->Mod_faktur_penjualan->get_pengiriman_detail($id);

        if ($items) {
            echo json_encode(array('status' => true, 'data' => $items));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Item pengiriman tidak ditemukan'));
        }
    }

    public function form_detail_item()
    {
        $data['barang_list'] = $this->Mod_faktur_penjualan->get_barang_aktif();
        $data['id_faktur_penjualan'] = $this->input->get('id_faktur') ?: '';
        $this->load->view('faktur_penjualan/form_detail_item', $data);
    }

    public function edit($id)
    {
        $data = $this->Mod_faktur_penjualan->get_by_id($id);
        echo json_encode($data);
    }

    public function detail_modal($id)
    {
        try {
            // Validasi ID
            if (empty($id) || !is_numeric($id)) {
                show_error('ID Faktur tidak valid', 400);
                return;
            }

            // Ambil data faktur
            $data['faktur'] = $this->Mod_faktur_penjualan->get_by_id($id);
            if (!$data['faktur']) {
                show_error('Faktur tidak ditemukan', 404);
                return;
            }

            // Ambil data detail dan pembayaran
            $data['faktur_detail'] = $this->Mod_faktur_penjualan->get_detail_by_faktur_id($id);
            $data['pembayaran_list'] = $this->Mod_faktur_penjualan->get_pembayaran_by_faktur_id($id);
            $data['barang_list'] = $this->Mod_faktur_penjualan->get_barang_aktif();
            
            // Hitung total pembayaran
            $data['total_pembayaran'] = 0;
            if ($data['pembayaran_list']) {
                foreach ($data['pembayaran_list'] as $pembayaran) {
                    $data['total_pembayaran'] += $pembayaran->jumlah_pembayaran;
                }
            }

            $this->load->view('faktur_penjualan/detail_modal', $data);
            
        } catch (Exception $e) {
            log_message('error', 'Error in detail_modal: ' . $e->getMessage());
            show_error('Terjadi kesalahan saat memuat detail faktur', 500);
        }
    }
    
    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_faktur');
        if (empty($nomor)) {
            $nomor = $this->Mod_faktur_penjualan->generate_nomor_faktur();
        }

        $save = array(
            'nomor_faktur' => $nomor,
            'tanggal_faktur' => $this->input->post('tanggal_faktur'),
            'id_pengiriman' => $this->input->post('id_pengiriman'),
            'id_pelanggan' => $this->input->post('id_pelanggan_hidden') ? $this->input->post('id_pelanggan_hidden') : $this->input->post('id_pelanggan'),
            'id_pesanan' => $this->input->post('id_pesanan_hidden') ? $this->input->post('id_pesanan_hidden') : $this->input->post('id_pesanan'),
            'status' => 'draft', // Always set to draft
            'jatuh_tempo' => $this->input->post('jatuh_tempo'),
            'keterangan' => $this->input->post('keterangan'),
            'total_item' => 0,
            'total_qty' => 0,
            'subtotal' => 0,
            'diskon' => 0,
            'pajak' => 0,
            'total_faktur' => 0,
            'created_by' => $this->session->userdata('nama_user'),
        );

        $insert_id = $this->Mod_faktur_penjualan->insert('faktur_penjualan', $save);
        
        if ($insert_id) {
            // Jika ada item pengiriman, tambahkan ke detail faktur
            $id_pengiriman = $this->input->post('id_pengiriman');
            if ($id_pengiriman) {
                $items = $this->Mod_faktur_penjualan->get_pengiriman_detail($id_pengiriman);
                if ($items) {
                    foreach ($items as $item) {
                        $subtotal = $item->qty_dikirim * $item->harga_jual;
                        $pajak_persen = $item->pajak_persen ? $item->pajak_persen : 11.00; // Default PPN 11%
                        $pajak_nilai = round($subtotal * $pajak_persen / 100, 2);
                        $total = $subtotal + $pajak_nilai;
                        
                        $detail = array(
                            'id_faktur_penjualan' => $insert_id,
                            'id_barang' => $item->id_barang,
                            'qty' => $item->qty_dikirim,
                            'harga_satuan' => $item->harga_jual,
                            'diskon_persen' => 0,
                            'diskon_nilai' => 0,
                            'subtotal' => $subtotal,
                            'pajak_persen' => $pajak_persen,
                            'pajak_nilai' => $pajak_nilai,
                            'total' => $total,
                            'keterangan' => $item->keterangan
                        );
                        $this->Mod_faktur_penjualan->save_detail($detail);
                    }
                    
                    // Update total faktur
                    $this->Mod_faktur_penjualan->update_total_faktur($insert_id);
                }
            }
            
            echo json_encode(array(
                "status" => TRUE,
                "message" => "Data faktur penjualan berhasil disimpan"
            ));
        } else {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Gagal menyimpan data faktur penjualan"
            ));
        }
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'tanggal_faktur' => $this->input->post('tanggal_faktur'),
            'jatuh_tempo' => $this->input->post('jatuh_tempo'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('nama_user'),
        );

        $update = $this->Mod_faktur_penjualan->update($id, $save);
        
        if ($update) {
            echo json_encode(array(
                "status" => TRUE,
                "message" => "Data faktur penjualan berhasil diupdate"
            ));
        } else {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Gagal mengupdate data faktur penjualan"
            ));
        }
    }
    
    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;
        
        if($this->input->post('nomor_faktur') == '')
        {
            $data['inputerror'][] = 'nomor_faktur';
            $data['error_string'][] = 'Nomor faktur tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('tanggal_faktur') == '')
        {
            $data['inputerror'][] = 'tanggal_faktur';
            $data['error_string'][] = 'Tanggal faktur tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('id_pengiriman') == '' && $this->input->post('id') == '')
        {
            $data['inputerror'][] = 'id_pengiriman';
            $data['error_string'][] = 'Pengiriman harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($data['status'] === FALSE)
        {
            echo json_encode($data);
            exit();
        }
    }

    public function delete()
    {
        $id = $this->input->post('id');

        // Cek apakah faktur masih draft
        $faktur = $this->Mod_faktur_penjualan->get_by_id($id);
        if($faktur && $faktur->status != 'draft') {
            echo json_encode(array('status' => FALSE, 'message' => 'Faktur yang sudah final tidak dapat dihapus!'));
            return;
        }

        // Hapus detail faktur terlebih dahulu
        $this->Mod_faktur_penjualan->delete_detail_by_faktur($id);

        // Hapus faktur
        $delete_result = $this->Mod_faktur_penjualan->delete($id, 'faktur_penjualan');
        
        if($delete_result) {
            echo json_encode(array(
                "status" => 'success', 
                "message" => 'Data faktur penjualan berhasil dihapus.'
            ));
        } else {
            echo json_encode(array(
                "status" => 'error', 
                "message" => 'Gagal menghapus data faktur penjualan.'
            ));
        }
    }

    // Detail faktur functions
    public function add_detail()
    {
        $id_faktur = $this->input->post('id_faktur_penjualan');
        $id_barang = $this->input->post('id_barang');
        $qty = $this->input->post('qty');
        $harga_satuan = $this->input->post('harga_satuan');
        $diskon_persen = $this->input->post('diskon_persen') ?: 0;
        $keterangan = $this->input->post('keterangan');
        
        // Ambil data barang untuk mendapatkan PPN
        $this->db->select('b.*, jp.tarif_persen as pajak_persen');
        $this->db->from('barang b');
        $this->db->join('jenis_pajak jp', 'b.jenis_pajak_id = jp.id', 'left');
        $this->db->where('b.id', $id_barang);
        $barang = $this->db->get()->row();
        
        $pajak_persen = $barang->pajak_persen ? $barang->pajak_persen : 11.00; // Default PPN 11%
        
        $subtotal = $qty * $harga_satuan;
        $diskon_nilai = ($diskon_persen / 100) * $subtotal;
        $nilai_setelah_diskon = $subtotal - $diskon_nilai;
        $pajak_nilai = ($pajak_persen / 100) * $nilai_setelah_diskon;
        $total = $nilai_setelah_diskon + $pajak_nilai;
        
        $data = array(
            'id_faktur_penjualan' => $id_faktur,
            'id_barang' => $id_barang,
            'qty' => $qty,
            'harga_satuan' => $harga_satuan,
            'diskon_persen' => $diskon_persen,
            'diskon_nilai' => $diskon_nilai,
            'subtotal' => $subtotal,
            'pajak_persen' => $pajak_persen,
            'pajak_nilai' => $pajak_nilai,
            'total' => $total,
            'keterangan' => $keterangan
        );
        
        $insert = $this->Mod_faktur_penjualan->save_detail($data);
        if ($insert) {
            // Update total faktur
            $this->Mod_faktur_penjualan->update_total_faktur($id_faktur);
            echo json_encode(array('status' => 'success', 'message' => 'Item berhasil ditambahkan!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menyimpan item!'));
        }
    }

    public function delete_payment()
    {
        $id = $this->input->post('id');
        
        $delete = $this->Mod_faktur_penjualan->delete_pembayaran($id);
        if ($delete) {
            echo json_encode(array('status' => 'success', 'message' => 'Pembayaran berhasil dihapus!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Pembayaran gagal dihapus!'));
        }
    }

    public function print($id)
    {
        $data['faktur'] = $this->Mod_faktur_penjualan->get_by_id($id);
        $data['faktur_detail'] = $this->Mod_faktur_penjualan->get_detail_by_faktur_id($id);
        $data['pembayaran_list'] = $this->Mod_faktur_penjualan->get_pembayaran_by_faktur_id($id);
        
        $this->load->view('faktur_penjualan/print', $data);
    }
    
    public function add_payment()
    {
        // Validasi input
        $this->form_validation->set_rules('id_faktur_penjualan', 'Faktur', 'required');
        $this->form_validation->set_rules('tanggal_pembayaran', 'Tanggal Pembayaran', 'required');
        $this->form_validation->set_rules('jumlah_pembayaran', 'Jumlah Pembayaran', 'required|numeric|greater_than[0]');
        $this->form_validation->set_rules('metode_pembayaran', 'Metode Pembayaran', 'required');
        
        if ($this->form_validation->run() == FALSE) {
            echo json_encode(array(
                'status' => 'error',
                'message' => strip_tags(validation_errors())
            ));
            return;
        }
        
        $data = array(
            'id_faktur_penjualan' => $this->input->post('id_faktur_penjualan'),
            'tanggal_pembayaran' => $this->input->post('tanggal_pembayaran'),
            'jumlah_pembayaran' => $this->input->post('jumlah_pembayaran'),
            'metode_pembayaran' => $this->input->post('metode_pembayaran'),
            'keterangan' => $this->input->post('keterangan'),
            'created_by' => $this->session->userdata('nama_user')
        );
        
        $insert = $this->Mod_faktur_penjualan->insert_pembayaran($data);

        if ($insert) {
            // Create auto journal entry for payment
            $this->_create_payment_journal($data);

            echo json_encode(array(
                'status' => 'success',
                'message' => 'Pembayaran berhasil disimpan!'
            ));
        } else {
            echo json_encode(array(
                'status' => 'error',
                'message' => 'Gagal menyimpan pembayaran!'
            ));
        }
    }
    
    public function get_faktur_payment_info()
    {
        $id = $this->input->post('id');
        $faktur = $this->Mod_faktur_penjualan->get_by_id($id);
        $total_paid = $this->Mod_faktur_penjualan->get_total_paid($id);
        
        echo json_encode(array(
            'total_faktur' => $faktur->total_faktur,
            'total_paid' => $total_paid
        ));
    }
    
    public function get_detail_item($id)
    {
        $detail = $this->db->get_where('faktur_penjualan_detail', ['id' => $id])->row();
        if ($detail) {
            echo json_encode($detail);
        } else {
            echo json_encode(['status' => false, 'message' => 'Item tidak ditemukan']);
        }
    }
    
    public function delete_detail()
    {
        $id = $this->input->post('id');
        $id_faktur = $this->input->post('id_faktur_penjualan');
        
        // Delete the detail item
        $delete = $this->Mod_faktur_penjualan->delete_detail($id);
        
        if ($delete) {
            // Update faktur totals
            $this->Mod_faktur_penjualan->update_total_faktur($id_faktur);
            
            echo json_encode([
                'status' => 'success',
                'message' => 'Item berhasil dihapus'
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Gagal menghapus item'
            ]);
        }
    }
    
    public function update_detail()
    {
        $id = $this->input->post('id');
        $id_faktur = $this->input->post('id_faktur_penjualan');
        $qty = $this->input->post('qty');
        $harga_satuan = $this->input->post('harga_satuan');
        $diskon_persen = $this->input->post('diskon_persen') ?: 0;
        $keterangan = $this->input->post('keterangan');
        
        // Ambil data detail untuk mendapatkan id_barang
        $detail_existing = $this->db->get_where('faktur_penjualan_detail', ['id' => $id])->row();
        
        // Ambil data barang untuk mendapatkan PPN
        $this->db->select('b.*, jp.tarif_persen as pajak_persen');
        $this->db->from('barang b');
        $this->db->join('jenis_pajak jp', 'b.jenis_pajak_id = jp.id', 'left');
        $this->db->where('b.id', $detail_existing->id_barang);
        $barang = $this->db->get()->row();
        
        $pajak_persen = $barang->pajak_persen ? $barang->pajak_persen : 11.00; // Default PPN 11%
        
        $subtotal = $qty * $harga_satuan;
        $diskon_nilai = ($diskon_persen / 100) * $subtotal;
        $nilai_setelah_diskon = $subtotal - $diskon_nilai;
        $pajak_nilai = ($pajak_persen / 100) * $nilai_setelah_diskon;
        $total = $nilai_setelah_diskon + $pajak_nilai;
        
        $data = array(
            'qty' => $qty,
            'harga_satuan' => $harga_satuan,
            'diskon_persen' => $diskon_persen,
            'diskon_nilai' => $diskon_nilai,
            'subtotal' => $subtotal,
            'pajak_persen' => $pajak_persen,
            'pajak_nilai' => $pajak_nilai,
            'total' => $total,
            'keterangan' => $keterangan
        );
        
        $update = $this->Mod_faktur_penjualan->update_detail(['id' => $id], $data);
        
        if ($update) {
            // Update faktur totals
            $this->Mod_faktur_penjualan->update_total_faktur($id_faktur);
            
            echo json_encode([
                'status' => 'success',
                'message' => 'Item berhasil diupdate'
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Gagal mengupdate item'
            ]);
        }
    }
    
    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');

        // Validate status value
        if (!in_array($status, ['draft', 'final', 'batal'])) {
            echo json_encode(array('status' => 'error', 'message' => 'Status tidak valid!'));
            return;
        }

        // Get current faktur
        $faktur = $this->Mod_faktur_penjualan->get_by_id($id);
        if (!$faktur) {
            echo json_encode(array('status' => 'error', 'message' => 'Faktur tidak ditemukan!'));
            return;
        }

        // Check if status can be changed
        if ($faktur->status == 'final' && $status == 'draft') {
            echo json_encode(array('status' => 'error', 'message' => 'Faktur yang sudah final tidak dapat diubah menjadi draft!'));
            return;
        }

        // Update status
        $data = array(
            'status' => $status,
            'updated_by' => $this->session->userdata('nama_user')
        );

        $update = $this->Mod_faktur_penjualan->update($id, $data);

        if ($update) {
            // Auto create journal entry when status changes to 'final'
            if ($status == 'final' && $faktur->status != 'final') {
                $this->_create_sales_journal($id);
            }

            echo json_encode(array(
                'status' => 'success',
                'message' => 'Status faktur berhasil diubah menjadi ' . ucfirst($status)
            ));
        } else {
            echo json_encode(array(
                'status' => 'error',
                'message' => 'Gagal mengubah status faktur!'
            ));
        }
    }

    /**
     * Create automatic journal entries for sales transaction
     * @param int $faktur_id Faktur ID
     */
    private function _create_sales_journal($faktur_id)
    {
        try {
            // Load COA integration library
            $this->load->library('Coa_integration');

            // Get faktur data
            $faktur = $this->Mod_faktur_penjualan->get_by_id($faktur_id);
            if (!$faktur) {
                return false;
            }

            // Get faktur detail for COGS calculation
            $faktur_detail = $this->Mod_faktur_penjualan->get_detail_by_faktur_id($faktur_id);

            // Calculate COGS
            $total_cogs = $this->coa_integration->calculate_cogs($faktur_detail);

            // Prepare sales data for journal
            $sales_data = array(
                'tanggal_faktur' => $faktur->tanggal_faktur,
                'nomor_faktur' => $faktur->nomor_faktur,
                'total_faktur' => $faktur->total_faktur,
                'total_cogs' => $total_cogs,
                'payment_method' => 'kredit' // Default to credit, can be enhanced later
            );

            // Process sales transaction
            $result = $this->coa_integration->process_sales_transaction($sales_data);

            if ($result['success']) {
                log_message('info', 'Auto journal created for sales: ' . $faktur->nomor_faktur .
                           ' - Sales Journal ID: ' . $result['sales_journal_id'] .
                           ' - COGS Journal ID: ' . $result['cogs_journal_id']);
            } else {
                log_message('error', 'Failed to create auto journal for sales: ' . $faktur->nomor_faktur .
                           ' - Error: ' . $result['message']);
            }

        } catch (Exception $e) {
            log_message('error', 'Exception in _create_sales_journal: ' . $e->getMessage());
        }
    }

    /**
     * Create automatic journal entries for payment transaction
     * @param array $payment_data Payment data
     */
    private function _create_payment_journal($payment_data)
    {
        try {
            // Load COA integration library
            $this->load->library('Coa_integration');

            // Get faktur data for reference
            $faktur = $this->Mod_faktur_penjualan->get_by_id($payment_data['id_faktur_penjualan']);
            if (!$faktur) {
                return false;
            }

            // Prepare payment data for journal
            $journal_payment_data = array(
                'tanggal_pembayaran' => $payment_data['tanggal_pembayaran'],
                'referensi' => $faktur->nomor_faktur . ' - ' . $payment_data['metode_pembayaran'],
                'jumlah_pembayaran' => $payment_data['jumlah_pembayaran'],
                'type' => 'receivable' // This is payment from customer (piutang)
            );

            // Process payment transaction
            $result = $this->coa_integration->process_payment_transaction($journal_payment_data);

            if ($result['success']) {
                log_message('info', 'Auto payment journal created for: ' . $faktur->nomor_faktur .
                           ' - Journal ID: ' . $result['journal_id'] .
                           ' - Amount: ' . $payment_data['jumlah_pembayaran']);
            } else {
                log_message('error', 'Failed to create auto payment journal for: ' . $faktur->nomor_faktur .
                           ' - Error: ' . $result['message']);
            }

        } catch (Exception $e) {
            log_message('error', 'Exception in _create_payment_journal: ' . $e->getMessage());
        }
    }
}




