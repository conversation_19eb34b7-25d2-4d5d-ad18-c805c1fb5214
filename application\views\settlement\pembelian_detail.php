<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Settlement Pembelian</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement/pembelian') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Pembelian</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Nomor Pembelian</th>
                                        <td><?= $pembelian->nomor_pembelian ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tanggal Pembelian</th>
                                        <td><?= $pembelian->tanggal_pembelian ?></td>
                                    </tr>
                                    <tr>
                                        <th><PERSON><PERSON>belian</th>
                                        <td>
                                            <?php if ($pembelian->jenis_pembelian == 'reguler'): ?>
                                                Reguler
                                            <?php elseif ($pembelian->jenis_pembelian == 'konsinyasi'): ?>
                                                Konsinyasi
                                            <?php elseif ($pembelian->jenis_pembelian == 'kontrak'): ?>
                                                Kontrak
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Status Pembelian</th>
                                        <td>
                                            <?php if ($pembelian->status == 'draft'): ?>
                                                <span class="badge badge-secondary">Draft</span>
                                            <?php elseif ($pembelian->status == 'disetujui'): ?>
                                                <span class="badge badge-info">Disetujui</span>
                                            <?php elseif ($pembelian->status == 'dipesan'): ?>
                                                <span class="badge badge-primary">Dipesan</span>
                                            <?php elseif ($pembelian->status == 'diterima'): ?>
                                                <span class="badge badge-warning">Diterima</span>
                                            <?php elseif ($pembelian->status == 'selesai'): ?>
                                                <span class="badge badge-success">Selesai</span>
                                            <?php elseif ($pembelian->status == 'dibatalkan'): ?>
                                                <span class="badge badge-danger">Dibatalkan</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Status Pembayaran</th>
                                        <td>
                                            <?php if ($pembelian->status_pembayaran == 'belum_bayar'): ?>
                                                <span class="badge badge-danger">Belum Bayar</span>
                                            <?php elseif ($pembelian->status_pembayaran == 'sebagian'): ?>
                                                <span class="badge badge-warning">Bayar Sebagian</span>
                                            <?php elseif ($pembelian->status_pembayaran == 'lunas'): ?>
                                                <span class="badge badge-success">Lunas</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-info">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Supplier</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Kode Supplier</th>
                                        <td><?= $pembelian->kode_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nama Supplier</th>
                                        <td><?= $pembelian->nama_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Alamat</th>
                                        <td><?= $pembelian->alamat_supplier ?></td>
                                    </tr>
                                    <tr>
                                        <th>Telepon</th>
                                        <td><?= $pembelian->telepon_supplier ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email</th>
                                        <td><?= $pembelian->email_supplier ?? '-' ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Detail Barang Pembelian</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Kode Barang</th>
                                                <th>Nama Barang</th>
                                                <th>Gudang</th>
                                                <th>Qty Pesan</th>
                                                <th>Qty Diterima</th>
                                                <th>Selisih</th>
                                                <th>Satuan</th>
                                                <th>Harga Satuan</th>
                                                <th>Diskon</th>
                                                <th>Subtotal</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            $total_qty_pesan = 0;
                                            $total_qty_terima = 0;
                                            $total_selisih = 0;
                                            $total_subtotal = 0;
                                            foreach ($detail as $row): 
                                                $subtotal = $row->qty * $row->harga_satuan - $row->diskon_nilai;
                                                $total_qty_pesan += $row->qty;
                                                $total_qty_terima += $row->qty_diterima;
                                                $total_selisih += $row->selisih_qty;
                                                $total_subtotal += $subtotal;
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->kode_barang ?></td>
                                                <td><?= $row->nama_barang ?> <?= $row->merk ? '- '.$row->merk : '' ?> <?= $row->tipe ? '- '.$row->tipe : '' ?></td>
                                                <td><?= $row->nama_gudang ?></td>
                                                <td class="text-right"><?= number_format($row->qty, 2) ?></td>
                                                <td class="text-right"><?= number_format($row->qty_diterima, 2) ?></td>
                                                <td class="text-right <?= $row->selisih_qty != 0 ? 'text-danger' : 'text-success' ?>"><?= number_format($row->selisih_qty, 2) ?></td>
                                                <td><?= $row->nama_satuan ?></td>
                                                <td class="text-right"><?= number_format($row->harga_satuan, 2) ?></td>
                                                <td class="text-right"><?= number_format($row->diskon_nilai, 2) ?></td>
                                                <td class="text-right"><?= number_format($subtotal, 2) ?></td>
                                                <td>
                                                    <?php if ($row->qty_diterima == 0): ?>
                                                        <span class="badge badge-danger">Belum Diterima</span>
                                                    <?php elseif ($row->qty_diterima < $row->qty): ?>
                                                        <span class="badge badge-warning">Diterima Sebagian</span>
                                                    <?php elseif ($row->qty_diterima == $row->qty): ?>
                                                        <span class="badge badge-success">Sesuai</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-info">Diterima Lebih</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="4" class="text-right">Total</th>
                                                <th class="text-right"><?= number_format($total_qty_pesan, 2) ?></th>
                                                <th class="text-right"><?= number_format($total_qty_terima, 2) ?></th>
                                                <th class="text-right <?= $total_selisih != 0 ? 'text-danger' : 'text-success' ?>"><?= number_format($total_selisih, 2) ?></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                                <th class="text-right"><?= number_format($total_subtotal, 2) ?></th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Riwayat Penerimaan</h3>
                            </div>
                            <div class="card-body">
                                <?php if (empty($penerimaan)): ?>
                                <div class="alert alert-warning">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Belum ada penerimaan untuk pembelian ini.</p>
                                </div>
                                <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Nomor Penerimaan</th>
                                                <th>Tanggal Penerimaan</th>
                                                <th>Total Item</th>
                                                <th>Total Qty</th>
                                                <th>Keterangan</th>
                                                <th width="10%">Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            foreach ($penerimaan as $row): 
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->nomor_penerimaan ?></td>
                                                <td><?= $row->tanggal_penerimaan ?></td>
                                                <td class="text-right"><?= $row->total_item ?></td>
                                                <td class="text-right"><?= number_format($row->total_qty, 2) ?></td>
                                                <td><?= $row->keterangan ?? '-' ?></td>
                                                <td>
                                                    <a href="<?= site_url('PenerimaanPembelian/detail/' . $row->id) ?>" class="btn btn-info btn-sm"><i class="fas fa-eye"></i> Detail</a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>

                                <?php if ($total_selisih > 0): ?>
                                <div class="alert alert-warning mt-3">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Masih ada barang yang belum diterima. Silakan lakukan penerimaan barang untuk menyelesaikan pembelian ini.</p>
                                    <a href="<?= site_url('PenerimaanPembelian/create/' . $pembelian->id) ?>" class="btn btn-primary"><i class="fas fa-plus"></i> Buat Penerimaan</a>
                                </div>
                                <?php elseif ($total_selisih < 0): ?>
                                <div class="alert alert-info mt-3">
                                    <h5><i class="icon fas fa-info"></i> Informasi</h5>
                                    <p>Jumlah barang yang diterima lebih banyak dari yang dipesan. Silakan periksa kembali data penerimaan.</p>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-success mt-3">
                                    <h5><i class="icon fas fa-check"></i> Informasi</h5>
                                    <p>Semua barang telah diterima sesuai dengan pembelian.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>