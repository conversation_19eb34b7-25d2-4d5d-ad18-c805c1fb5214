-- Additional COA accounts for better integration
-- Run this script to add specific accounts for electronics store

-- Insert additional accounts for better integration
INSERT INTO `coa_akun` (`kode_akun`, `nama_akun`, `id_kategori`, `level`, `id_parent`, `saldo_normal`, `dapat_diinput`, `saldo_awal`, `tanggal_saldo_awal`, `deskripsi`, `aktif`) VALUES
-- Detailed Inventory Accounts
('1-1501', 'Persediaan Handphone', 1, 3, 12, 'Debit', 1, 0.00, NULL, 'Persediaan barang handphone', 1),
('1-1502', 'Persediaan Laptop', 1, 3, 12, 'Debit', 1, 0.00, NULL, 'Persediaan barang laptop', 1),
('1-1503', 'Persedia<PERSON> Aksesoris', 1, 3, 12, 'Debit', 1, 0.00, NULL, 'Persediaan aksesoris elektronik', 1),
('1-1504', '<PERSON><PERSON><PERSON><PERSON>', 1, 3, 12, 'Debit', 1, 0.00, NULL, 'Persediaan spare part dan komponen', 1),

-- Detailed Receivable Accounts
('1-1201', 'Piutang Usaha - Retail', 1, 3, 9, 'Debit', 1, 0.00, NULL, 'Piutang dari penjualan retail', 1),
('1-1202', 'Piutang Usaha - Grosir', 1, 3, 9, 'Debit', 1, 0.00, NULL, 'Piutang dari penjualan grosir', 1),
('1-1203', 'Piutang Usaha - Online', 1, 3, 9, 'Debit', 1, 0.00, NULL, 'Piutang dari penjualan online', 1),

-- Detailed Payable Accounts
('2-1101', 'Hutang Usaha - Supplier Utama', 2, 3, 32, 'Kredit', 1, 0.00, NULL, 'Hutang kepada supplier utama', 1),
('2-1102', 'Hutang Usaha - Supplier Lokal', 2, 3, 32, 'Kredit', 1, 0.00, NULL, 'Hutang kepada supplier lokal', 1),
('2-1103', 'Hutang Usaha - Importir', 2, 3, 32, 'Kredit', 1, 0.00, NULL, 'Hutang kepada importir', 1),

-- Additional Revenue Accounts
('4-1400', 'Pendapatan Service', 4, 2, 54, 'Kredit', 1, 0.00, NULL, 'Pendapatan dari jasa service dan reparasi', 1),
('4-1500', 'Pendapatan Instalasi', 4, 2, 54, 'Kredit', 1, 0.00, NULL, 'Pendapatan dari jasa instalasi', 1),
('4-1600', 'Pendapatan Konsultasi', 4, 2, 54, 'Kredit', 1, 0.00, NULL, 'Pendapatan dari jasa konsultasi IT', 1),

-- Additional Expense Accounts
('5-2950', 'Beban Pengiriman', 5, 2, 70, 'Debit', 1, 0.00, NULL, 'Beban pengiriman dan ekspedisi', 1),
('5-2960', 'Beban Garansi', 5, 2, 70, 'Debit', 1, 0.00, NULL, 'Beban klaim garansi', 1),
('5-2970', 'Beban Retur', 5, 2, 70, 'Debit', 1, 0.00, NULL, 'Beban penanganan retur barang', 1),
('5-2980', 'Beban E-commerce', 5, 2, 70, 'Debit', 1, 0.00, NULL, 'Beban platform e-commerce dan marketplace', 1),
('5-2990', 'Beban Payment Gateway', 5, 2, 70, 'Debit', 1, 0.00, NULL, 'Beban payment gateway dan transfer', 1),

-- Cash and Bank Accounts (more detailed)
('1-1106', 'Bank BRI', 1, 3, 3, 'Debit', 1, 0.00, NULL, 'Rekening Bank BRI', 1),
('1-1107', 'Bank CIMB Niaga', 1, 3, 3, 'Debit', 1, 0.00, NULL, 'Rekening Bank CIMB Niaga', 1),
('1-1108', 'E-Wallet OVO', 1, 3, 3, 'Debit', 1, 0.00, NULL, 'Saldo e-wallet OVO', 1),
('1-1109', 'E-Wallet GoPay', 1, 3, 3, 'Debit', 1, 0.00, NULL, 'Saldo e-wallet GoPay', 1),
('1-1110', 'E-Wallet DANA', 1, 3, 3, 'Debit', 1, 0.00, NULL, 'Saldo e-wallet DANA', 1),

-- Additional Asset Accounts
('1-1900', 'Uang Muka Karyawan', 1, 2, 2, 'Debit', 1, 0.00, NULL, 'Uang muka dan piutang karyawan', 1),
('1-1950', 'Deposit Supplier', 1, 2, 2, 'Debit', 1, 0.00, NULL, 'Deposit yang dibayarkan ke supplier', 1),

-- Additional Liability Accounts
('2-1700', 'Hutang Gaji', 2, 2, 31, 'Kredit', 1, 0.00, NULL, 'Hutang gaji karyawan', 1),
('2-1800', 'Hutang Komisi', 2, 2, 31, 'Kredit', 1, 0.00, NULL, 'Hutang komisi sales', 1),
('2-1900', 'Uang Muka Pelanggan', 2, 2, 31, 'Kredit', 1, 0.00, NULL, 'Uang muka dari pelanggan', 1),

-- Cost of Goods Sold (more detailed)
('5-1600', 'HPP - Handphone', 5, 2, 64, 'Debit', 1, 0.00, NULL, 'Harga Pokok Penjualan Handphone', 1),
('5-1700', 'HPP - Laptop', 5, 2, 64, 'Debit', 1, 0.00, NULL, 'Harga Pokok Penjualan Laptop', 1),
('5-1800', 'HPP - Aksesoris', 5, 2, 64, 'Debit', 1, 0.00, NULL, 'Harga Pokok Penjualan Aksesoris', 1),

-- Adjustment Accounts
('4-2500', 'Pendapatan Penyesuaian Persediaan', 4, 2, 58, 'Kredit', 1, 0.00, NULL, 'Pendapatan dari penyesuaian persediaan positif', 1),
('5-4600', 'Beban Penyesuaian Persediaan', 5, 2, 89, 'Debit', 1, 0.00, NULL, 'Beban dari penyesuaian persediaan negatif', 1),
('5-4700', 'Beban Selisih Stok', 5, 2, 89, 'Debit', 1, 0.00, NULL, 'Beban selisih stok opname', 1);

-- Update parent accounts to not allow direct input
UPDATE `coa_akun` SET `dapat_diinput` = 0 WHERE `kode_akun` IN ('1-1500', '1-1200', '2-1100');

-- Add indexes for better performance
ALTER TABLE `coa_transaksi` ADD INDEX `idx_coa_transaksi_referensi` (`referensi`);
ALTER TABLE `coa_transaksi` ADD INDEX `idx_coa_transaksi_tanggal_status` (`tanggal_transaksi`, `status`);
ALTER TABLE `coa_transaksi_detail` ADD INDEX `idx_coa_transaksi_detail_akun` (`id_akun`);
