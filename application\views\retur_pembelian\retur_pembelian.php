<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-undo text-blue"></i> Data Retur Pembelian</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <?= $this->session->flashdata('pesan') ?>
                        <table id="tbl_retur" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor Retur</th>
                                    <th>Tanggal</th>
                                    <th>Nomor Pembelian</th>
                                    <th><PERSON><PERSON></th>
                                    <th>Supplier</th>
                                    <th>Status</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Total Nilai</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal Form Header -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Retur Pembelian</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form akan dimuat di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Simpan</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Batal</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="modal_detail" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div class="modal fade" id="modal-hapus" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Hapus</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus retur pembelian ini?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="btn-hapus">Hapus</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Status -->
<div class="modal fade" id="modal-status" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="status-title">Konfirmasi Perubahan Status</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="status-message">Apakah Anda yakin ingin mengubah status retur pembelian ini?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success" id="btn-status">Konfirmasi</button>
            </div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;
    var id_retur;
    var status_to_update;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_retur").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Retur Pembelian Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('ReturPembelian/ajax_list') ?>",
                "type": "POST"
            },

            //Set column definition initialisation properties.
            "columnDefs": [{
                "targets": [-1], //last column
                "orderable": false, //set not orderable
            }],
        });
    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Tambah Retur Pembelian'); // set a title to modal header

        // Load form add
        $.ajax({
            url: "<?php echo site_url('ReturPembelian/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show'); // show bootstrap modal after loading form
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Edit Retur Pembelian'); // set a title to modal header

        // Load form edit
        $.ajax({
            url: "<?php echo site_url('ReturPembelian/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);

                // Load data untuk edit
                $.ajax({
                    url: "<?php echo site_url('ReturPembelian/edit') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_retur"]').val(data.nomor_retur);
                        $('[name="tanggal_retur"]').val(data.tanggal_retur);
                        
                        // Set nilai penerimaan yang akan dipilih setelah dropdown diperbarui
                        if (typeof window.setPenerimaanForEdit === 'function') {
                            window.setPenerimaanForEdit(data.id_penerimaan);
                        }
                        
                        // Trigger pembelian change yang akan memperbarui dropdown penerimaan
                        $('[name="id_pembelian"]').val(data.id_pembelian).trigger('change');
                        
                        // Set supplier
                        $('#id_supplier_display').val(data.id_supplier).trigger('change');
                        $('#id_supplier').val(data.id_supplier);
                        $('#id_supplier_hidden').val(data.id_supplier);
                        $('[name="alasan_retur"]').val(data.alasan_retur);
                        $('[name="keterangan"]').val(data.keterangan);

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat mengambil data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function detail(id) {
        $('#modal_detail').modal('show');
        $('#modal_detail .modal-content').html('<div class="modal-body text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

        $.ajax({
            url: "<?php echo site_url('ReturPembelian/detail_modal') ?>/" + id,
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal_detail .modal-content').html(data);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#modal_detail .modal-content').html('<div class="modal-body text-center text-danger"><i class="fa fa-exclamation-triangle"></i> Error loading detail</div>');
            }
        });
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('ReturPembelian/insert') ?>";
        } else {
            url = "<?php echo site_url('ReturPembelian/update') ?>";
        }

        // ajax adding data to database
        var formData = new FormData($('#form')[0]);
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload();
                    
                    Swal.fire({
                        title: 'Data retur pembelian berhasil disimpan!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                    
                    // Show SweetAlert notification with detailed errors
                    var errorMessages = data.error_string.join('<br>');
                    Swal.fire({
                        title: 'Gagal Menyimpan!',
                        html: 'Terjadi kesalahan:<br>' + errorMessages,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    
                    // Reinisialisasi Select2 setelah validasi gagal
                    $('.select2').select2();
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            }
        });
    }

    function hapus(id) {
        id_retur = id;
        $('#modal-hapus').modal('show');
    }

    $('#btn-hapus').click(function() {
        $.ajax({
            url: '<?= site_url('ReturPembelian/delete') ?>',
            type: 'POST',
            data: {
                id: id_retur
            },
            dataType: 'json',
            success: function(response) {
                $('#modal-hapus').modal('hide');
                if (response.status == 'success') {
                    Swal.fire({
                        title: 'Terhapus!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    table.ajax.reload();
                } else {
                    Swal.fire({
                        title: 'Gagal!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#modal-hapus').modal('hide');
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menghapus data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    });

    function updateStatus(id, status) {
        id_retur = id;
        status_to_update = status;
        
        var statusText = '';
        var buttonClass = 'btn-success';
        
        if (status == 'diproses') {
            statusText = 'Proses';
            $('#status-title').text('Konfirmasi Proses Retur');
            $('#status-message').text('Apakah Anda yakin ingin memproses retur pembelian ini?');
        } else if (status == 'selesai') {
            statusText = 'Selesai';
            $('#status-title').text('Konfirmasi Selesai Retur');
            $('#status-message').text('Apakah Anda yakin ingin menyelesaikan retur pembelian ini?');
        } else if (status == 'dibatalkan') {
            statusText = 'Batal';
            buttonClass = 'btn-danger';
            $('#status-title').text('Konfirmasi Pembatalan Retur');
            $('#status-message').text('Apakah Anda yakin ingin membatalkan retur pembelian ini?');
        }
        
        $('#btn-status').text('Ya, ' + statusText);
        $('#btn-status').removeClass('btn-success btn-danger').addClass(buttonClass);
        
        $('#modal-status').modal('show');
    }

    $('#btn-status').click(function() {
        $.ajax({
            url: '<?= site_url('ReturPembelian/update_status') ?>',
            type: 'POST',
            data: {
                id: id_retur,
                status: status_to_update
            },
            dataType: 'json',
            success: function(response) {
                $('#modal-status').modal('hide');
                if (response.status) {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    table.ajax.reload();
                } else {
                    Swal.fire({
                        title: 'Gagal!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $('#modal-status').modal('hide');
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengubah status.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    });

    function printRetur(id) {
        window.open('<?= site_url('ReturPembelian/print/') ?>' + id, '_blank');
    }
</script>