<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Tambah Penyesuaian Stok</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('penyesuaian') ?>">Penyesuaian Stok</a></li>
                        <li class="breadcrumb-item active">Tambah</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Form Penyesuaian Stok Baru</h3>
                            <div class="card-tools">
                                <a href="<?= base_url('penyesuaian') ?>" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> Kembali
                                </a>
                            </div>
                        </div>

                        <form id="form-penyesuaian" method="post" action="<?= base_url('penyesuaian/insert') ?>">
                            <div class="card-body">

                                <!-- Tab Navigation -->
                                <ul class="nav nav-tabs" id="penyesuaianStokTab" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="detail-tab" data-toggle="tab" href="#detail" role="tab">Detail Penyesuaian</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="status-tab" data-toggle="tab" href="#status" role="tab">Keterangan & Status</a>
                                    </li>
                                </ul>

                                <!-- Tab Content -->
                                <div class="tab-content" id="penyesuaianStokTabContent">

                                    <!-- Tab Data Dasar -->
                                    <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                        <div class="pt-3">

                                            <div class="form-group row">
                                                <label for="kode_penyesuaian" class="col-sm-3 col-form-label">Kode Penyesuaian</label>
                                                <div class="col-sm-9">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="kode_penyesuaian" id="kode_penyesuaian" placeholder="Kode akan di-generate otomatis" autocomplete="off">
                                                        <div class="input-group-append">
                                                            <button type="button" class="btn btn-outline-secondary" id="btn-generate-kode" onclick="generateKode()" title="Generate Kode">
                                                                <i class="fas fa-sync"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <small class="form-text text-muted">Format: PST-YYYYMMDD-XXXX, contoh: PST-20250627-0001. Kosongkan untuk generate otomatis.</small>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="tanggal" class="col-sm-3 col-form-label">Tanggal <span class="text-danger">*</span></label>
                                                <div class="col-sm-9">
                                                    <input type="date" class="form-control" name="tanggal" id="tanggal" value="<?= date('Y-m-d') ?>" required>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="id_barang" class="col-sm-3 col-form-label">Barang <span class="text-danger">*</span></label>
                                                <div class="col-sm-9">
                                                    <div class="input-group">
                                                        <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                                                            <option value="">-- Pilih Barang --</option>
                                                            <?php foreach ($barang_list as $barang): ?>
                                                                <option value="<?= $barang->id ?>" <?= (isset($selected_barang) && $selected_barang->id == $barang->id) ? 'selected' : '' ?>>
                                                                    <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?>
                                                                </option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <div class="input-group-append">
                                                            <button type="button" class="btn btn-outline-info" id="btn-info-stok" onclick="showStokInfo()" title="Info Stok" disabled>
                                                                <i class="fas fa-info-circle"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="id_gudang" class="col-sm-3 col-form-label">Gudang <span class="text-danger">*</span></label>
                                                <div class="col-sm-9">
                                                    <select class="form-control select2" name="id_gudang" id="id_gudang" required style="width: 100%;">
                                                        <option value="">-- Pilih Gudang --</option>
                                                        <?php foreach ($gudang_list as $gudang): ?>
                                                            <option value="<?= $gudang->id ?>" <?= (isset($selected_gudang) && $selected_gudang->id == $gudang->id) ? 'selected' : '' ?>>
                                                                <?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                    <!-- Tab Detail Penyesuaian -->
                                    <div class="tab-pane fade" id="detail" role="tabpanel">
                                        <div class="pt-3">

                                            <div class="form-group row">
                                                <label for="qty_awal" class="col-sm-3 col-form-label">Qty Awal <span class="text-danger">*</span></label>
                                                <div class="col-sm-9">
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" name="qty_awal" id="qty_awal" placeholder="Jumlah stok saat ini" required min="0" step="1" onchange="updateSelisih()" value="<?= isset($stok_terakhir) ? $stok_terakhir : '0' ?>" readonly>
                                                        <div class="input-group-append">
                                                            <button type="button" class="btn btn-outline-secondary" id="btn-load-stok" onclick="loadStokTerakhir()" title="Load Stok Terakhir">
                                                                <i class="fas fa-sync"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <small class="form-text text-muted">Stok saat ini di sistem. Klik tombol refresh untuk memperbarui.</small>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="qty_baru" class="col-sm-3 col-form-label">Qty Baru <span class="text-danger">*</span></label>
                                                <div class="col-sm-9">
                                                    <input type="number" class="form-control" name="qty_baru" id="qty_baru" placeholder="Jumlah stok setelah penyesuaian" required min="0" step="1" onchange="updateSelisih()">
                                                    <small class="form-text text-muted">Jumlah stok yang seharusnya (hasil perhitungan fisik).</small>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="qty_selisih" class="col-sm-3 col-form-label">Selisih</label>
                                                <div class="col-sm-9">
                                                    <input type="number" class="form-control" name="qty_selisih" id="qty_selisih" readonly>
                                                    <small class="form-text text-muted">Selisih = Qty Baru - Qty Awal. Positif = Penambahan, Negatif = Pengurangan.</small>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="jenis_penyesuaian" class="col-sm-3 col-form-label">Jenis Penyesuaian</label>
                                                <div class="col-sm-9">
                                                    <input type="text" class="form-control" name="jenis_penyesuaian" id="jenis_penyesuaian" readonly>
                                                    <small class="form-text text-muted">Otomatis terisi berdasarkan selisih qty.</small>
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                    <!-- Tab Keterangan & Status -->
                                    <div class="tab-pane fade" id="status" role="tabpanel">
                                        <div class="pt-3">

                                            <div class="form-group row">
                                                <label for="alasan" class="col-sm-3 col-form-label">Alasan <span class="text-danger">*</span></label>
                                                <div class="col-sm-9">
                                                    <select class="form-control" name="alasan" id="alasan" required>
                                                        <option value="">-- Pilih Alasan --</option>
                                                        <option value="Stok Opname">Stok Opname</option>
                                                        <option value="Barang Rusak">Barang Rusak</option>
                                                        <option value="Barang Hilang">Barang Hilang</option>
                                                        <option value="Barang Kadaluarsa">Barang Kadaluarsa</option>
                                                        <option value="Kesalahan Input">Kesalahan Input</option>
                                                        <option value="Retur Tidak Tercatat">Retur Tidak Tercatat</option>
                                                        <option value="Lainnya">Lainnya</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                                                <div class="col-sm-9">
                                                    <textarea class="form-control" name="keterangan" id="keterangan" rows="3" placeholder="Keterangan tambahan (opsional)"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label for="aktif" class="col-sm-3 col-form-label">Status</label>
                                                <div class="col-sm-9">
                                                    <div class="custom-control custom-switch">
                                                        <input type="checkbox" class="custom-control-input" name="aktif" id="aktif" value="1" checked>
                                                        <label class="custom-control-label" for="aktif">Aktif</label>
                                                    </div>
                                                    <small class="form-text text-muted">Centang untuk mengaktifkan penyesuaian stok ini.</small>
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                </div>

                            </div>

                            <div class="card-footer">
                                <div class="row">
                                    <div class="col-sm-12 text-right">
                                        <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                            <i class="fas fa-times"></i> Batal
                                        </button>
                                        <button type="submit" class="btn btn-primary" id="btn-save">
                                            <i class="fas fa-save"></i> Simpan
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Set tanggal default ke hari ini
    $('#tanggal').val(new Date().toISOString().split('T')[0]);

    // Event handler untuk perubahan barang dan gudang
    $('#id_barang, #id_gudang').change(function() {
        if ($('#id_barang').val() && $('#id_gudang').val()) {
            $('#btn-load-stok').prop('disabled', false);
            $('#btn-info-stok').prop('disabled', false);
            loadStokTerakhir();
        } else {
            $('#btn-load-stok').prop('disabled', true);
            $('#btn-info-stok').prop('disabled', true);
            $('#qty_awal').val('');
        }
    });

    // Trigger change event jika sudah ada nilai terpilih
    if ($('#id_barang').val() && $('#id_gudang').val()) {
        $('#id_barang').trigger('change');
    }

    // Form submission
    $('#form-penyesuaian').submit(function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#btn-save').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.status) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: 'Data penyesuaian stok berhasil disimpan.',
                        showConfirmButton: false,
                        timer: 1500
                    }).then(function() {
                        window.location.href = '<?= base_url('penyesuaian') ?>';
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal!',
                        text: 'Terjadi kesalahan saat menyimpan data.'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Terjadi kesalahan sistem.'
                });
            },
            complete: function() {
                $('#btn-save').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan');
            }
        });
    });
});

// Function untuk generate kode otomatis
function generateKode() {
    $.ajax({
        url: '<?= base_url('penyesuaian/generate_kode') ?>',
        type: 'POST',
        dataType: 'json',
        beforeSend: function() {
            $('#btn-generate-kode').html('<i class="fas fa-spinner fa-spin"></i>');
        },
        success: function(response) {
            $('#kode_penyesuaian').val(response.kode);
        },
        complete: function() {
            $('#btn-generate-kode').html('<i class="fas fa-sync"></i>');
        }
    });
}

// Function untuk load stok terakhir
function loadStokTerakhir() {
    var id_barang = $('#id_barang').val();
    var id_gudang = $('#id_gudang').val();
    
    if (!id_barang || !id_gudang) {
        return;
    }
    
    $.ajax({
        url: '<?= base_url('penyesuaian/get_stok_terakhir') ?>',
        type: 'POST',
        data: {
            id_barang: id_barang,
            id_gudang: id_gudang
        },
        dataType: 'json',
        beforeSend: function() {
            $('#btn-load-stok').html('<i class="fas fa-spinner fa-spin"></i>');
        },
        success: function(response) {
            $('#qty_awal').val(response.stok_terakhir || 0);
            updateSelisih();
        },
        complete: function() {
            $('#btn-load-stok').html('<i class="fas fa-sync"></i>');
        }
    });
}

// Function untuk update selisih
function updateSelisih() {
    var qty_awal = parseFloat($('#qty_awal').val()) || 0;
    var qty_baru = parseFloat($('#qty_baru').val()) || 0;
    var selisih = qty_baru - qty_awal;
    
    $('#qty_selisih').val(selisih);
    
    if (selisih > 0) {
        $('#jenis_penyesuaian').val('PENAMBAHAN');
        $('#qty_selisih').removeClass('text-danger').addClass('text-success');
    } else if (selisih < 0) {
        $('#jenis_penyesuaian').val('PENGURANGAN');
        $('#qty_selisih').removeClass('text-success').addClass('text-danger');
    } else {
        $('#jenis_penyesuaian').val('TIDAK ADA PERUBAHAN');
        $('#qty_selisih').removeClass('text-success text-danger');
    }
}

// Function untuk show stok info
function showStokInfo() {
    var id_barang = $('#id_barang').val();
    var id_gudang = $('#id_gudang').val();
    
    if (!id_barang || !id_gudang) {
        return;
    }
    
    // Implementasi modal info stok bisa ditambahkan di sini
    Swal.fire({
        icon: 'info',
        title: 'Info Stok',
        text: 'Fitur info stok detail akan segera tersedia.'
    });
}
</script>