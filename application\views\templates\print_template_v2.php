<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Print Document' ?> - <?= $document_number ?? '' ?></title>
    <style>
        /* Reset dan Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            margin: 0;
            padding: 20px;
        }
        
        /* Print Styles */
        @media print {
            body {
                margin: 0;
                padding: 15px;
                font-size: 11px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            @page {
                margin: 1.5cm;
                size: A4;
            }
        }
        
        /* Header Styles */
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            /* border: 2px solid #000; */
            padding: 15px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #000;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .company-info {
            font-size: 11px;
            color: #000;
            margin-bottom: 10px;
            line-height: 1.3;
        }
        
        .document-title {
            font-size: 18px;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 10px;
            border-top: 1px solid #000;
            padding-top: 10px;
        }
        
        /* Info Section Styles */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
            gap: 15px;
        }
        
        .info-box {
            flex: 1;
            border: 1px solid #000;
            padding: 15px;
            background: #fff;
        }
        
        .info-box-title {
            font-size: 13px;
            font-weight: bold;
            color: #000;
            margin-bottom: 12px;
            padding-bottom: 5px;
            border-bottom: 1px solid #000;
        }
        
        .info-item {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
        }
        
        .info-label {
            font-weight: bold;
            color: #000;
            min-width: 120px;
            margin-right: 10px;
        }
        
        .info-separator {
            margin-right: 10px;
            color: #000;
        }
        
        .info-value {
            flex: 1;
            color: #000;
        }
        
        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border: 1px solid #000;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #fff;
            color: #000;
        }
        
        /* Table Styles */
        .table-container {
            margin: 25px 0;
        }
        
        .table-title {
            font-size: 14px;
            font-weight: bold;
            color: #000;
            margin-bottom: 10px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            /* border: 2px solid #000; */
        }
        
        .data-table th {
            background: #fff;
            color: #000;
            font-weight: bold;
            padding: 10px 8px;
            text-align: center;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            border: 1px solid #000;
        }
        
        .data-table td {
            padding: 8px;
            border: 1px solid #000;
            vertical-align: top;
            background: #fff;
        }
        
        .total-row {
            background: #fff !important;
            font-weight: bold;
        }
        
        .total-row td {
            padding: 10px 8px;
            font-weight: bold;
            color: #000;
            /* border-top: 2px solid #000; */
        }
        
        /* Text Alignment */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        
        /* Summary Section */
        .summary-section {
            display: flex;
            justify-content: space-between;
            margin-top: 25px;
            gap: 15px;
        }
        
        .summary-left {
            flex: 1;
        }
        
        .summary-right {
            width: 300px;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
        }
        
        .summary-table th {
            background: #fff;
            color: #000;
            padding: 8px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #000;
        }
        
        .summary-table td {
            padding: 8px;
            text-align: right;
            border: 1px solid #000;
            background: #fff;
            color: #000;
        }
        
        .summary-table .total-final {
            background: #fff;
            color: #000;
            font-weight: bold;
            font-size: 13px;
            border-top: 2px solid #000;
        }
        
        /* Notes Section */
        .notes-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #000;
            background: #fff;
        }
        
        .notes-title {
            font-weight: bold;
            color: #000;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .notes-content {
            color: #000;
            line-height: 1.5;
        }
        
        /* Signature Section */
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            gap: 15px;
        }
        
        .signature-box {
            flex: 1;
            text-align: center;
            /* border: 1px solid #000; */
            padding: 15px;
            background: #fff;
        }
        
        .signature-title {
            font-weight: bold;
            color: #000;
            margin-bottom: 40px;
            font-size: 12px;
        }
        
        .signature-line {
            border-top: 1px solid #000;
            margin-top: 10px;
            padding-top: 5px;
            font-size: 11px;
            font-weight: bold;
            color: #000;
        }
        
        /* Footer */
        .print-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #000;
            border-top: 1px solid #000;
            padding-top: 10px;
            line-height: 1.3;
        }
        
        /* Print Controls */
        .print-controls {
            text-align: center;
            margin-bottom: 25px;
            padding: 12px;
            background: #f5f5f5;
            border: 1px solid #000;
        }
        
        .btn-print {
            background: #fff;
            color: #000;
            padding: 10px 20px;
            border: 1px solid #000;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            margin: 0 5px;
        }
        
        .btn-print:hover {
            background: #f0f0f0;
        }
        
        /* Responsive Design */
        @media screen and (max-width: 768px) {
            .info-section {
                flex-direction: column;
            }
            
            .summary-section {
                flex-direction: column;
            }
            
            .summary-right {
                width: 100%;
            }
            
            .signature-section {
                flex-direction: column;
            }
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 30px 15px;
            color: #000;
            font-style: italic;
        }
        
        /* Utility Classes */
        .font-bold { font-weight: bold; }
        .font-normal { font-weight: normal; }
        .text-small { font-size: 10px; }
        .text-large { font-size: 14px; }
        
        .mb-10 { margin-bottom: 10px; }
        .mb-15 { margin-bottom: 15px; }
        .mb-20 { margin-bottom: 20px; }
        .mt-10 { margin-top: 10px; }
        .mt-15 { margin-top: 15px; }
        .mt-20 { margin-top: 20px; }
        
        .p-10 { padding: 10px; }
        .p-15 { padding: 15px; }
        .p-20 { padding: 20px; }
        
        .border-top { border-top: 1px solid #000; }
        .border-bottom { border-bottom: 1px solid #000; }
        
        /* Divider Lines */
        .divider {
            border-top: 1px solid #000;
            margin: 15px 0;
        }
        
        .divider-thick {
            border-top: 2px solid #000;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <button class="btn-print" onclick="window.print()">
            Cetak Dokumen
        </button>
        <button class="btn-print" onclick="window.close()">
            Tutup
        </button>
    </div>

    <!-- Header -->
    <div class="print-header">
        <div class="company-name"><?= $company_name ?? 'TOKO ELEKTRONIK' ?></div>
        <div class="company-info">
            <?= $company_address ?? 'Jl. Contoh No. 123, Kota Contoh' ?><br>
            Telp: <?= $company_phone ?? '(021) 1234567' ?> | 
            Email: <?= $company_email ?? '<EMAIL>' ?>
        </div>
        <div class="document-title"><?= $document_title ?? 'DOKUMEN' ?></div>
    </div>

    <!-- Content akan diisi oleh view yang menggunakan template ini -->
    <?= $content ?? '' ?>

    <!-- Footer -->
    <div class="print-footer">
        <div>
            <strong>Dokumen ini dicetak pada:</strong> <?= date('d/m/Y H:i:s') ?><br>
            <strong>Nomor Dokumen:</strong> <?= $document_number ?? '-' ?><br>
            Sistem Manajemen Toko Elektronik - <?= date('Y') ?>
        </div>
    </div>

    <script>
        // Auto print functionality
        if (window.location.search.includes('auto_print=1')) {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }
        
        // Close window after printing (optional)
        window.onafterprint = function() {
            if (window.location.search.includes('auto_close=1')) {
                setTimeout(function() {
                    window.close();
                }, 1000);
            }
        };
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+P for print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape to close
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>