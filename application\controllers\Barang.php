<?php
defined('BASEPATH') or exit('No direct script access allowed');
// require 'vendor/autoload.php';
use Zend\Barcode\Barcode;

/**
 * Create By : Aryo
 * Youtube : Aryo Coding
 */
class Barang extends MY_Controller
{

    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_barang');
        // $this->load->model('dashboard/Mod_dashboard');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');


        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view;
        }
        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'barang/barang', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_barang->get_datatables();
        $data = array();
        $no = $this->input->post('start');
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->kode_barang ?? '-';
            $sub_array[] = $row->nama_barang;
            $sub_array[] = $row->merk ?? '-';
            $sub_array[] = $row->tipe ?? '-';
            $sub_array[] = $row->nama_satuan ?? '-';
            $sub_array[] = $row->nama_pajak ?? '-';
            $sub_array[] = 'Rp ' . number_format($row->harga_jual, 0, ',', '.');
            $sub_array[] = number_format($row->berat ?? 0, 2) . ' kg';
            $sub_array[] = ($row->aktif == 1) ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Tidak Aktif</span>';

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$row->id')\"><i class=\"fas fa-trash\"></i></a>
                             <a class=\"btn btn-xs btn-outline-info barcode\" href=\"javascript:void(0)\" title=\"Barcode\" onclick=\"barcode('$row->id')\"><i class=\"fas fa-barcode\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $this->input->post('draw'),
            "recordsTotal" => $this->Mod_barang->count_all(),
            "recordsFiltered" => $this->Mod_barang->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate kode otomatis jika tidak diisi
        $kode = $this->input->post('kode_barang');
        if (empty($kode)) {
            $kode = $this->Mod_barang->generate_kode();
        }

        $save = array(
            'kode_barang' => $kode,
            'nama_barang' => $this->input->post('nama_barang'),
            'merk' => $this->input->post('merk'),
            'tipe' => $this->input->post('tipe'),
            'spesifikasi' => $this->input->post('spesifikasi'),
            'satuan_id' => $this->input->post('satuan_id'),
            'jenis_pajak_id' => $this->input->post('jenis_pajak_id'),
            'stok_minimum' => $this->input->post('stok_minimum'),
            'harga_beli' => $this->input->post('harga_beli'),
            'harga_jual' => $this->input->post('harga_jual'),
            'berat' => $this->input->post('berat'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        $this->Mod_barang->insert('barang', $save);
        echo json_encode(array("status" => TRUE));
    }


    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'nama_barang' => $this->input->post('nama_barang'),
            'merk' => $this->input->post('merk'),
            'tipe' => $this->input->post('tipe'),
            'spesifikasi' => $this->input->post('spesifikasi'),
            'satuan_id' => $this->input->post('satuan_id'),
            'jenis_pajak_id' => $this->input->post('jenis_pajak_id'),
            'stok_minimum' => $this->input->post('stok_minimum'),
            'harga_beli' => $this->input->post('harga_beli'),
            'harga_jual' => $this->input->post('harga_jual'),
            'berat' => $this->input->post('berat'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );

        $this->Mod_barang->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_barang->get_with_relations($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['satuan_list'] = $this->Mod_barang->get_satuan_dropdown();
        $data['jenis_pajak_list'] = $this->Mod_barang->get_jenis_pajak_dropdown();
        $this->load->view('barang/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_barang->delete($id, 'barang');
        echo json_encode(array("status" => TRUE));
    }
    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        // Validasi kode barang
        $kode = $this->input->post('kode_barang');
        $id = $this->input->post('id');
        if (!empty($kode)) {
            if ($this->Mod_barang->check_kode_exists($kode, $id)) {
                $data['inputerror'][] = 'kode_barang';
                $data['error_string'][] = 'Kode Barang Sudah Ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi nama barang (wajib)
        if ($this->input->post('nama_barang') == '') {
            $data['inputerror'][] = 'nama_barang';
            $data['error_string'][] = 'Nama Barang Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        // Validasi satuan (wajib)
        if ($this->input->post('satuan_id') == '') {
            $data['inputerror'][] = 'satuan_id';
            $data['error_string'][] = 'Satuan Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        // Validasi jenis pajak (wajib)
        if ($this->input->post('jenis_pajak_id') == '') {
            $data['inputerror'][] = 'jenis_pajak_id';
            $data['error_string'][] = 'Jenis Pajak Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        // Validasi harga beli (wajib)
        $harga_beli = $this->input->post('harga_beli');
        if ($harga_beli == '' || $harga_beli <= 0) {
            $data['inputerror'][] = 'harga_beli';
            $data['error_string'][] = 'Harga Beli Tidak Boleh Kosong atau Nol';
            $data['status'] = FALSE;
        }

        // Validasi harga jual (wajib dan harus lebih besar atau sama dengan harga beli)
        $harga_jual = $this->input->post('harga_jual');
        if ($harga_jual == '' || $harga_jual <= 0) {
            $data['inputerror'][] = 'harga_jual';
            $data['error_string'][] = 'Harga Jual Tidak Boleh Kosong atau Nol';
            $data['status'] = FALSE;
        } elseif (!is_numeric($harga_jual)) {
            $data['inputerror'][] = 'harga_jual';
            $data['error_string'][] = 'Harga Jual Harus Berupa Angka';
            $data['status'] = FALSE;
        } elseif (!empty($harga_beli) && is_numeric($harga_beli) && $harga_jual < $harga_beli) {
            $data['inputerror'][] = 'harga_jual';
            $data['error_string'][] = 'Harga Jual Harus Lebih Besar atau Sama dengan Harga Beli';
            $data['status'] = FALSE;
        }

        // Validasi berat (wajib)
        $berat = $this->input->post('berat');
        if ($berat == '' || $berat <= 0) {
            $data['inputerror'][] = 'berat';
            $data['error_string'][] = 'Berat Tidak Boleh Kosong atau Nol';
            $data['status'] = FALSE;
        } elseif (!is_numeric($berat)) {
            $data['inputerror'][] = 'berat';
            $data['error_string'][] = 'Berat Harus Berupa Angka';
            $data['status'] = FALSE;
        } elseif ($berat > 1000) {
            $data['inputerror'][] = 'berat';
            $data['error_string'][] = 'Berat Tidak Boleh Lebih dari 1000 kg';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Method untuk generate kode otomatis via AJAX
    public function generate_kode()
    {
        $kode = $this->Mod_barang->generate_kode();
        echo json_encode(array('kode' => $kode));
    }

    // Method untuk toggle status aktif
    public function toggle_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $result = $this->Mod_barang->update_status($id, $status);
        echo json_encode(array("status" => $result));
    }

    public function print_barcode()
    {
        $id = $this->input->post('id');
        $barang = $this->Mod_barang->get($id);

        $data['jumlah'] = $this->input->post('jumlah') ?? 1;
        $data['code'] = $barang->kode_barang;
        $data['nama_barang'] = $barang->nama_barang;
        $data['barcode'] = $this->set_barcode($barang->kode_barang);

        $this->load->view('barang/print_barcode', $data);
    }

    public function barcode($id)
    {
        $data['barang'] = $this->Mod_barang->get($id);
        $this->load->view('barang/barcode_form', $data);
    }

    public function set_barcode($code)
    {
        $generator = new Picqer\Barcode\BarcodeGeneratorHTML();
        return $generator->getBarcode($code, $generator::TYPE_CODE_128, 2, 42);
    }

    public function set_barcodePNG($code, $h, $w)
    {
        $generator = new Picqer\Barcode\BarcodeGeneratorPNG();
        echo '<img src="data:image/png;base64,' . base64_encode($generator->getBarcode($code, $generator::TYPE_CODE_128, 1, 42)) . '">';
        // return $generator->getBarcode($code, $generator::TYPE_EAN_13, 2, 42);
    }
}
