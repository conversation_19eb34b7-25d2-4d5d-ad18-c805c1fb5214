<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary">
                        <h3 class="card-title"><i class="fa fa-cogs text-white"></i> COA Integration Setup</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Informasi Setup</h5>
                            Halaman ini digunakan untuk setup integrasi otomatis COA dengan modul operasional.
                            Pastikan backup database sebelum menjalankan setup.
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success">
                                        <h3 class="card-title">Setup Additional Accounts</h3>
                                    </div>
                                    <div class="card-body">
                                        <p>Menambahkan akun-akun COA tambahan yang diperlukan untuk integrasi:</p>
                                        <ul>
                                            <li>Per<PERSON><PERSON><PERSON> per kategori barang</li>
                                            <li>Piutang dan hutang detail</li>
                                            <li>Akun pendapatan service</li>
                                            <li>Akun beban operasional tambahan</li>
                                            <li>E-wallet dan payment gateway</li>
                                        </ul>
                                        <button type="button" class="btn btn-success btn-block" onclick="setupAccounts()">
                                            <i class="fas fa-plus"></i> Setup Additional Accounts
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-warning">
                                        <h3 class="card-title">Test Integration</h3>
                                    </div>
                                    <div class="card-body">
                                        <p>Test integrasi otomatis dengan membuat jurnal test:</p>
                                        <ul>
                                            <li>Test jurnal penjualan</li>
                                            <li>Test jurnal pembelian</li>
                                            <li>Test jurnal pembayaran</li>
                                            <li>Test jurnal penyesuaian</li>
                                        </ul>
                                        <button type="button" class="btn btn-warning btn-block" onclick="testIntegration()">
                                            <i class="fas fa-flask"></i> Test Integration
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-info">
                                        <h3 class="card-title">Account Balances</h3>
                                    </div>
                                    <div class="card-body">
                                        <p>Cek saldo akun-akun utama:</p>
                                        <button type="button" class="btn btn-info" onclick="checkBalances()">
                                            <i class="fas fa-balance-scale"></i> Check Account Balances
                                        </button>
                                        
                                        <div id="balance-results" class="mt-3" style="display: none;">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Kode Akun</th>
                                                        <th>Nama Akun</th>
                                                        <th>Saldo</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="balance-table-body">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary">
                                        <h3 class="card-title">Integration Status</h3>
                                    </div>
                                    <div class="card-body">
                                        <div id="integration-status">
                                            <div class="alert alert-secondary">
                                                <i class="fas fa-info-circle"></i> Klik tombol di atas untuk melihat status integrasi.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function setupAccounts() {
    Swal.fire({
        title: 'Konfirmasi Setup',
        text: 'Apakah Anda yakin ingin menjalankan setup additional accounts? Pastikan sudah backup database.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Ya, Setup!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?= site_url('coa_setup/setup_additional_accounts') ?>',
                type: 'POST',
                dataType: 'json',
                beforeSend: function() {
                    Swal.fire({
                        title: 'Processing...',
                        text: 'Sedang menjalankan setup, mohon tunggu...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading()
                        }
                    });
                },
                success: function(response) {
                    if (response.status === 'success') {
                        let message = response.message;
                        if (response.errors && response.errors.length > 0) {
                            message += '\n\nErrors:\n' + response.errors.join('\n');
                        }
                        
                        Swal.fire({
                            title: 'Setup Berhasil!',
                            text: message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                        
                        updateIntegrationStatus('Setup additional accounts completed successfully.');
                    } else {
                        Swal.fire({
                            title: 'Setup Gagal!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function testIntegration() {
    $.ajax({
        url: '<?= site_url('coa_setup/test_integration') ?>',
        type: 'POST',
        dataType: 'json',
        beforeSend: function() {
            Swal.fire({
                title: 'Testing...',
                text: 'Sedang menjalankan test integrasi...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading()
                }
            });
        },
        success: function(response) {
            if (response.status === 'success') {
                let results = response.results;
                let message = 'Test Integration Results:\n\n';
                
                for (let test in results) {
                    let result = results[test];
                    message += test + ': ' + (result.success ? 'SUCCESS' : 'FAILED - ' + result.message) + '\n';
                }
                
                Swal.fire({
                    title: 'Test Completed!',
                    text: message,
                    icon: 'info',
                    confirmButtonText: 'OK'
                });
                
                updateIntegrationStatus('Integration test completed. Check logs for details.');
            } else {
                Swal.fire({
                    title: 'Test Gagal!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function() {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan sistem!',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function checkBalances() {
    $.ajax({
        url: '<?= site_url('coa_setup/check_balances') ?>',
        type: 'POST',
        dataType: 'json',
        beforeSend: function() {
            $('#balance-results').hide();
        },
        success: function(response) {
            if (response.status === 'success') {
                let tbody = $('#balance-table-body');
                tbody.empty();
                
                response.balances.forEach(function(account) {
                    let row = '<tr>' +
                        '<td>' + account.code + '</td>' +
                        '<td>' + account.name + '</td>' +
                        '<td>Rp ' + formatNumber(account.balance) + '</td>' +
                        '</tr>';
                    tbody.append(row);
                });
                
                $('#balance-results').show();
                updateIntegrationStatus('Account balances checked successfully.');
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: response.message,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function() {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan sistem!',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function updateIntegrationStatus(message) {
    let timestamp = new Date().toLocaleString();
    let statusHtml = '<div class="alert alert-success">' +
        '<i class="fas fa-check-circle"></i> ' + message +
        '<br><small>Last updated: ' + timestamp + '</small>' +
        '</div>';
    $('#integration-status').html(statusHtml);
}

function formatNumber(num) {
    return parseFloat(num).toLocaleString('id-ID');
}
</script>
