<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Settlement Stok</h3>
                <div class="card-tools">
                    <a href="<?= site_url('settlement/stok') ?>" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Barang</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Kode Barang</th>
                                        <td><?= $barang->kode_barang ?></td>
                                    </tr>
                                    <tr>
                                        <th><PERSON>a <PERSON></th>
                                        <td><?= $barang->nama_barang ?></td>
                                    </tr>
                                    <tr>
                                        <th>Merk</th>
                                        <td><?= $barang->merk ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tipe</th>
                                        <td><?= $barang->tipe ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Satuan</th>
                                        <td><?= $barang->nama_satuan ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-info">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Gudang</h3>
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Kode Gudang</th>
                                        <td><?= $gudang->kode_gudang ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nama Gudang</th>
                                        <td><?= $gudang->nama_gudang ?></td>
                                    </tr>
                                    <tr>
                                        <th>Alamat</th>
                                        <td><?= $gudang->alamat ?? '-' ?></td>
                                    </tr>
                                    <tr>
                                        <th>Penanggung Jawab</th>
                                        <td><?= $gudang->penanggung_jawab ?? '-' ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Informasi Stok</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-box bg-info">
                                            <span class="info-box-icon"><i class="fas fa-database"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Stok Sistem</span>
                                                <span class="info-box-number"><?= number_format($stok_sistem->stok_tersedia ?? 0, 2) ?> <?= $barang->nama_satuan ?></span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    Stok yang tercatat di sistem
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-box bg-success">
                                            <span class="info-box-icon"><i class="fas fa-box"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Stok Fisik (Opname Terakhir)</span>
                                                <span class="info-box-number"><?= $stok_opname ? number_format($stok_opname->qty_fisik, 2) . ' ' . $barang->nama_satuan : 'Belum Pernah Opname' ?></span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    <?= $stok_opname ? 'Tanggal Opname: ' . $stok_opname->tanggal_opname : 'Belum ada data stok opname' ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($stok_opname): ?>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="info-box <?= $stok_opname->selisih == 0 ? 'bg-success' : 'bg-danger' ?>">
                                            <span class="info-box-icon"><i class="fas fa-balance-scale"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Selisih Stok</span>
                                                <span class="info-box-number"><?= number_format($stok_opname->selisih, 2) ?> <?= $barang->nama_satuan ?></span>
                                                <div class="progress">
                                                    <div class="progress-bar" style="width: 100%"></div>
                                                </div>
                                                <span class="progress-description">
                                                    <?= $stok_opname->selisih == 0 ? 'Stok fisik sesuai dengan stok sistem' : 'Ada perbedaan antara stok fisik dan stok sistem' ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if ($stok_opname && $stok_opname->selisih != 0): ?>
                                <div class="alert alert-warning mt-3">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                                    <p>Terdapat selisih antara stok fisik dan stok sistem. Silakan lakukan penyesuaian stok untuk memperbaiki data.</p>
                                    <a href="<?= site_url('settlement/stok_create_penyesuaian/' . $barang->id . '/' . $gudang->id) ?>" class="btn btn-warning"><i class="fas fa-sync-alt"></i> Buat Penyesuaian Stok</a>
                                </div>
                                <?php elseif (!$stok_opname): ?>
                                <div class="alert alert-info mt-3">
                                    <h5><i class="icon fas fa-info"></i> Informasi</h5>
                                    <p>Belum ada data stok opname untuk barang ini di gudang ini. Silakan lakukan stok opname untuk memastikan keakuratan data stok.</p>
                                    <a href="<?= site_url('StokOpname/create') ?>" class="btn btn-primary"><i class="fas fa-clipboard-check"></i> Buat Stok Opname</a>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-success mt-3">
                                    <h5><i class="icon fas fa-check"></i> Informasi</h5>
                                    <p>Stok fisik sesuai dengan stok sistem.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card card-warning">
                            <div class="card-header">
                                <h3 class="card-title">Mutasi Stok</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th>Tanggal</th>
                                                <th>Jenis Transaksi</th>
                                                <th>Nomor Referensi</th>
                                                <th>Qty Masuk</th>
                                                <th>Qty Keluar</th>
                                                <th>Saldo Stok</th>
                                                <th>Keterangan</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            $saldo = 0;
                                            if (!empty($mutasi)):
                                                foreach ($mutasi as $row): 
                                                    $saldo += ($row->qty_masuk - $row->qty_keluar);
                                            ?>
                                            <tr>
                                                <td><?= $no++ ?></td>
                                                <td><?= $row->tanggal ?></td>
                                                <td><?= $row->jenis_transaksi ?></td>
                                                <td><?= $row->nomor_referensi ?></td>
                                                <td class="text-right"><?= $row->qty_masuk > 0 ? number_format($row->qty_masuk, 2) . ' ' . $barang->nama_satuan : '-' ?></td>
                                                <td class="text-right"><?= $row->qty_keluar > 0 ? number_format($row->qty_keluar, 2) . ' ' . $barang->nama_satuan : '-' ?></td>
                                                <td class="text-right"><?= number_format($saldo, 2) . ' ' . $barang->nama_satuan ?></td>
                                                <td><?= $row->keterangan ?? '-' ?></td>
                                            </tr>
                                            <?php 
                                                endforeach;
                                            else:
                                            ?>
                                            <tr>
                                                <td colspan="8" class="text-center">Tidak ada data mutasi stok</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>