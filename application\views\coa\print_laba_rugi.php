<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Laba Rugi',
    'document_number' => 'LR-' . date('Ymd-His'),
    'document_title' => 'LAPORAN LABA RUGI'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Laporan',
        'items' => [
            ['label' => 'Periode', 'value' => '<strong>' . format_date_indonesia($tanggal_awal) . ' s/d ' . format_date_indonesia($tanggal_akhir) . '</strong>'],
            ['label' => 'Tanggal Cetak', 'value' => format_date_indonesia(date('Y-m-d'), true)],
            ['label' => 'Mata Uang', 'value' => 'IDR (Rupiah)'],
            ['label' => 'Metode', 'value' => 'Akrual']
        ]
    ]
];

// Initialize totals
$total_pendapatan = 0;
$total_hpp = 0;
$total_beban_operasional = 0;
$total_pendapatan_lain = 0;
$total_beban_lain = 0;

// Build content
$content = '';
$content .= create_info_section($info_sections);

// PENDAPATAN SECTION
$pendapatan_headers = [
    ['label' => 'Kode Akun', 'width' => '15%'],
    ['label' => 'Nama Akun', 'width' => '65%'],
    ['label' => 'Jumlah', 'width' => '20%', 'align' => 'right']
];

$pendapatan_data = [];
if (!empty($pendapatan_list)) {
    foreach ($pendapatan_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $pendapatan_data[] = [
            $item->kode_akun,
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_pendapatan += $item->saldo;
    }
}

$pendapatan_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL PENDAPATAN</strong>', 'colspan' => '2', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_pendapatan) . '</strong>', 'align' => 'right']
    ]
];

$content .= create_data_table('<span class="icon-money"></span>PENDAPATAN', $pendapatan_headers, $pendapatan_data, $pendapatan_options);

// HPP SECTION
if (!empty($hpp_list)) {
    $hpp_data = [];
    foreach ($hpp_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $hpp_data[] = [
            $item->kode_akun,
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_hpp += $item->saldo;
    }
    
    $hpp_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL HARGA POKOK PENJUALAN</strong>', 'colspan' => '2', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_hpp) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-package"></span>HARGA POKOK PENJUALAN', $pendapatan_headers, $hpp_data, $hpp_options);
}

// Calculate Laba Kotor
$laba_kotor = $total_pendapatan - $total_hpp;
$content .= '<div class="summary-section mb-20">';
$content .= '<div class="summary-right">';
$laba_kotor_items = [
    ['label' => 'LABA KOTOR', 'value' => format_currency($laba_kotor), 'class' => 'total-final']
];
$content .= create_summary_table($laba_kotor_items);
$content .= '</div>';
$content .= '</div>';

// BEBAN OPERASIONAL SECTION
if (!empty($beban_operasional_list)) {
    $beban_data = [];
    foreach ($beban_operasional_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $beban_data[] = [
            $item->kode_akun,
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_beban_operasional += $item->saldo;
    }
    
    $beban_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL BEBAN OPERASIONAL</strong>', 'colspan' => '2', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_beban_operasional) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-warning"></span>BEBAN OPERASIONAL', $pendapatan_headers, $beban_data, $beban_options);
}

// Calculate Laba Operasional
$laba_operasional = $laba_kotor - $total_beban_operasional;
$content .= '<div class="summary-section mb-20">';
$content .= '<div class="summary-right">';
$laba_operasional_items = [
    ['label' => 'LABA OPERASIONAL', 'value' => format_currency($laba_operasional), 'class' => 'total-final']
];
$content .= create_summary_table($laba_operasional_items);
$content .= '</div>';
$content .= '</div>';

// PENDAPATAN LAIN-LAIN SECTION
if (!empty($pendapatan_lain_list)) {
    $pendapatan_lain_data = [];
    foreach ($pendapatan_lain_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $pendapatan_lain_data[] = [
            $item->kode_akun,
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_pendapatan_lain += $item->saldo;
    }
    
    $pendapatan_lain_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL PENDAPATAN LAIN-LAIN</strong>', 'colspan' => '2', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_pendapatan_lain) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-money"></span>PENDAPATAN LAIN-LAIN', $pendapatan_headers, $pendapatan_lain_data, $pendapatan_lain_options);
}

// BEBAN LAIN-LAIN SECTION
if (!empty($beban_lain_list)) {
    $beban_lain_data = [];
    foreach ($beban_lain_list as $item) {
        $nama_akun = $item->nama_akun;
        if ($item->level > 1) {
            $nama_akun = str_repeat('&nbsp;&nbsp;&nbsp;', ($item->level - 1)) . $nama_akun;
        }
        
        $beban_lain_data[] = [
            $item->kode_akun,
            $nama_akun,
            format_currency($item->saldo)
        ];
        
        $total_beban_lain += $item->saldo;
    }
    
    $beban_lain_options = [
        'total_row' => [
            ['value' => '<strong>TOTAL BEBAN LAIN-LAIN</strong>', 'colspan' => '2', 'align' => 'right'],
            ['value' => '<strong>' . format_currency($total_beban_lain) . '</strong>', 'align' => 'right']
        ]
    ];
    
    $content .= create_data_table('<span class="icon-warning"></span>BEBAN LAIN-LAIN', $pendapatan_headers, $beban_lain_data, $beban_lain_options);
}

// Calculate Laba Bersih
$laba_bersih = $laba_operasional + $total_pendapatan_lain - $total_beban_lain;

// SUMMARY SECTION
$content .= '<div class="summary-section">';
$content .= '<div class="summary-left">';

$analysis_content = 'Analisis Kinerja Keuangan:' . "\n";
$analysis_content .= '• Total Pendapatan: ' . format_currency($total_pendapatan) . "\n";
$analysis_content .= '• Harga Pokok Penjualan: ' . format_currency($total_hpp) . "\n";
$analysis_content .= '• Gross Profit Margin: ' . ($total_pendapatan > 0 ? number_format(($laba_kotor / $total_pendapatan) * 100, 2) : '0') . '%' . "\n";
$analysis_content .= '• Operating Profit Margin: ' . ($total_pendapatan > 0 ? number_format(($laba_operasional / $total_pendapatan) * 100, 2) : '0') . '%' . "\n";
$analysis_content .= '• Net Profit Margin: ' . ($total_pendapatan > 0 ? number_format(($laba_bersih / $total_pendapatan) * 100, 2) : '0') . '%' . "\n\n";

if ($laba_bersih > 0) {
    $analysis_content .= 'Status: ✅ PERUSAHAAN MERAIH LABA' . "\n";
    $analysis_content .= 'Kinerja keuangan menunjukkan hasil positif.';
} elseif ($laba_bersih < 0) {
    $analysis_content .= 'Status: ❌ PERUSAHAAN MENGALAMI RUGI' . "\n";
    $analysis_content .= 'Perlu evaluasi strategi bisnis dan efisiensi operasional.';
} else {
    $analysis_content .= 'Status: ⚖️ PERUSAHAAN IMPAS (BREAK EVEN)' . "\n";
    $analysis_content .= 'Pendapatan sama dengan total beban.';
}

$content .= create_notes_section('Analisis Kinerja', $analysis_content);

$content .= '</div>';
$content .= '<div class="summary-right">';

// Final summary table
$final_summary_items = [
    ['label' => 'Total Pendapatan', 'value' => format_currency($total_pendapatan)],
    ['label' => 'Harga Pokok Penjualan', 'value' => format_currency($total_hpp)],
    ['label' => 'Laba Kotor', 'value' => format_currency($laba_kotor)],
    ['label' => 'Beban Operasional', 'value' => format_currency($total_beban_operasional)],
    ['label' => 'Laba Operasional', 'value' => format_currency($laba_operasional)],
    ['label' => 'Pendapatan Lain-lain', 'value' => format_currency($total_pendapatan_lain)],
    ['label' => 'Beban Lain-lain', 'value' => format_currency($total_beban_lain)],
    ['label' => 'LABA BERSIH', 'value' => format_currency($laba_bersih), 'class' => 'total-final']
];

$content .= create_summary_table($final_summary_items);

$content .= '</div>';
$content .= '</div>';

// Add profit/loss indicator
$content .= '<div class="notes-section">';
$content .= '<div class="notes-title">Hasil Usaha:</div>';
$content .= '<div class="notes-content text-center">';
if ($laba_bersih > 0) {
    $content .= '<span class="color-success font-bold text-large">✅ LABA BERSIH</span><br>';
    $content .= '<span class="text-large">' . format_currency($laba_bersih) . '</span>';
} elseif ($laba_bersih < 0) {
    $content .= '<span class="color-danger font-bold text-large">❌ RUGI BERSIH</span><br>';
    $content .= '<span class="text-large">' . format_currency(abs($laba_bersih)) . '</span>';
} else {
    $content .= '<span class="color-warning font-bold text-large">⚖️ IMPAS (BREAK EVEN)</span><br>';
    $content .= '<span class="text-large">Rp 0</span>';
}
$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => '(............................)',
        'position' => 'Staff Accounting'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Accounting'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Direktur'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>