<?php
// Load print helper
$this->load->helper('print');

// Get data from controller
$jenis_laporan = $jenis_laporan ?? '';
$filter = $filter ?? array();
$laporan_data = $laporan ?? null;
$info_barang = $info_barang ?? '';
$info_gudang = $info_gudang ?? '';

// Prepare data untuk template
$jenis_names = array(
    'stok_terkini' => 'Stok Terkini',
    'movement' => 'Movement Stok (Kartu Stok)',
    'stok_minimum' => 'Stok Minimum',
    'nilai_stok' => 'Nilai Stok per Gudang',
    'stok_kosong' => 'Barang Stok Kosong'
);

$template_data = [
    'title' => 'Cetak Laporan Stok',
    'document_number' => 'LST-' . date('Ymd-His'),
    'document_title' => 'LAPORAN ' . strtoupper($jenis_names[$jenis_laporan] ?? $jenis_laporan)
];

// Prepare periode info
$periode = 'Semua Data';
if (!empty($filter['tanggal_dari']) && !empty($filter['tanggal_sampai'])) {
    $periode = date('d/m/Y', strtotime($filter['tanggal_dari'])) . ' - ' . date('d/m/Y', strtotime($filter['tanggal_sampai']));
}

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Laporan',
        'items' => [
            ['label' => 'Tanggal Laporan', 'value' => '<strong>' . format_date_indonesia(date('Y-m-d'), true) . '</strong>'],
            ['label' => 'Jenis Laporan', 'value' => '<strong>' . ($jenis_names[$jenis_laporan] ?? $jenis_laporan) . '</strong>'],
            ['label' => 'Periode', 'value' => $periode],
            ['label' => 'Barang', 'value' => $info_barang ?: 'Semua Barang'],
            ['label' => 'Gudang', 'value' => $info_gudang ?: 'Semua Gudang']
        ]
    ]
];

// Add filter info if available
$filter_items = [];
if (!empty($filter_stok_minimum)) {
    $filter_items[] = ['label' => 'Filter Stok Minimum', 'value' => $filter_stok_minimum ? 'Ya' : 'Tidak'];
}
if (!empty($filter_stok_kosong)) {
    $filter_items[] = ['label' => 'Tampilkan Stok Kosong', 'value' => $filter_stok_kosong ? 'Ya' : 'Tidak'];
}
if (!empty($filter_barang_aktif)) {
    $filter_items[] = ['label' => 'Hanya Barang Aktif', 'value' => $filter_barang_aktif ? 'Ya' : 'Tidak'];
}

if (!empty($filter_items)) {
    $info_sections[] = [
        'title' => '<span class="icon-info"></span>Filter Laporan',
        'items' => $filter_items
    ];
}

// Prepare table headers and data based on report type
$table_headers = [];
$table_data = [];
$table_options = [];
$no = 1;

// Initialize totals
$total_items = 0;
$stok_minimum_count = 0;
$stok_kosong_count = 0;

if ($laporan_data && $laporan_data->num_rows() > 0) {
    $total_items = $laporan_data->num_rows();
    
    switch ($jenis_laporan) {
        case 'stok_terkini':
            $table_headers = [
                ['label' => 'No', 'width' => '4%', 'align' => 'center'],
                ['label' => 'Kode Barang', 'width' => '10%'],
                ['label' => 'Nama Barang', 'width' => '20%'],
                ['label' => 'Merk', 'width' => '10%'],
                ['label' => 'Tipe', 'width' => '10%'],
                ['label' => 'Gudang', 'width' => '12%'],
                ['label' => 'Satuan', 'width' => '6%', 'align' => 'center'],
                ['label' => 'Stok', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Min', 'width' => '6%', 'align' => 'right'],
                ['label' => 'Status', 'width' => '8%', 'align' => 'center'],
                ['label' => 'Nilai Stok', 'width' => '12%', 'align' => 'right']
            ];
            
            $total_qty = 0;
            $total_nilai = 0;
            
            foreach ($laporan_data->result() as $data) {
                $nama_barang = $data->nama_barang;
                
                // Status stok
                $status_display = $data->status_stok;
                if ($data->status_stok == 'Kosong') {
                    $status_display = '<span class="color-danger font-bold">Kosong</span>';
                    $stok_kosong_count++;
                } elseif ($data->status_stok == 'Minimum') {
                    $status_display = '<span class="color-warning font-bold">Minimum</span>';
                    $stok_minimum_count++;
                } else {
                    $status_display = '<span class="color-success font-bold">Normal</span>';
                }
                
                $table_data[] = [
                    $no++,
                    $data->kode_barang,
                    $nama_barang,
                    $data->merk ?? '-',
                    $data->tipe ?? '-',
                    $data->nama_gudang,
                    $data->nama_satuan,
                    number_format($data->qty_terakhir, 0, ',', '.'),
                    number_format($data->stok_minimum, 0, ',', '.'),
                    $status_display,
                    'Rp ' . number_format($data->nilai_stok_beli, 0, ',', '.')
                ];
                
                $total_qty += $data->qty_terakhir;
                $total_nilai += $data->nilai_stok_beli;
            }
            
            $table_options = [
                'total_row' => [
                    ['value' => '<strong>TOTAL:</strong>', 'colspan' => '7', 'align' => 'right'],
                    ['value' => '<strong>' . number_format($total_qty, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '', 'align' => 'right'],
                    ['value' => '', 'align' => 'center'],
                    ['value' => '<strong>Rp ' . number_format($total_nilai, 0, ',', '.') . '</strong>', 'align' => 'right']
                ]
            ];
            break;
            
        case 'movement':
            $table_headers = [
                ['label' => 'No', 'width' => '4%', 'align' => 'center'],
                ['label' => 'Tanggal', 'width' => '12%'],
                ['label' => 'Kode Barang', 'width' => '10%'],
                ['label' => 'Nama Barang', 'width' => '20%'],
                ['label' => 'Gudang', 'width' => '12%'],
                ['label' => 'Tipe Transaksi', 'width' => '12%'],
                ['label' => 'Masuk', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Keluar', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Satuan', 'width' => '6%', 'align' => 'center'],
                ['label' => 'Referensi', 'width' => '8%']
            ];
            
            $total_masuk = 0;
            $total_keluar = 0;
            
            foreach ($laporan_data->result() as $data) {
                $table_data[] = [
                    $no++,
                    date('d/m/Y H:i', strtotime($data->tanggal)),
                    $data->kode_barang,
                    $data->nama_barang,
                    $data->nama_gudang,
                    ucwords(str_replace('_', ' ', $data->tipe_transaksi)),
                    $data->qty_in > 0 ? number_format($data->qty_in, 0, ',', '.') : '-',
                    $data->qty_out > 0 ? number_format($data->qty_out, 0, ',', '.') : '-',
                    $data->nama_satuan,
                    $data->ref_transaksi
                ];
                
                $total_masuk += $data->qty_in;
                $total_keluar += $data->qty_out;
            }
            
            $table_options = [
                'total_row' => [
                    ['value' => '<strong>TOTAL:</strong>', 'colspan' => '6', 'align' => 'right'],
                    ['value' => '<strong>' . number_format($total_masuk, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '<strong>' . number_format($total_keluar, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '', 'colspan' => '2']
                ]
            ];
            break;
            
        case 'stok_minimum':
            $table_headers = [
                ['label' => 'No', 'width' => '4%', 'align' => 'center'],
                ['label' => 'Kode Barang', 'width' => '12%'],
                ['label' => 'Nama Barang', 'width' => '25%'],
                ['label' => 'Merk', 'width' => '12%'],
                ['label' => 'Gudang', 'width' => '12%'],
                ['label' => 'Satuan', 'width' => '7%', 'align' => 'center'],
                ['label' => 'Stok', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Min', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Kurang', 'width' => '8%', 'align' => 'right'],
                ['label' => 'Est. Pembelian', 'width' => '12%', 'align' => 'right']
            ];
            
            $total_kekurangan = 0;
            $total_estimasi = 0;
            
            foreach ($laporan_data->result() as $data) {
                $estimasi_pembelian = $data->kekurangan * $data->harga_beli;
                
                $table_data[] = [
                    $no++,
                    $data->kode_barang,
                    $data->nama_barang,
                    $data->merk ?? '-',
                    $data->nama_gudang,
                    $data->nama_satuan,
                    number_format($data->qty_terakhir, 0, ',', '.'),
                    number_format($data->stok_minimum, 0, ',', '.'),
                    '<span class="color-danger font-bold">' . number_format($data->kekurangan, 0, ',', '.') . '</span>',
                    'Rp ' . number_format($estimasi_pembelian, 0, ',', '.')
                ];
                
                $total_kekurangan += $data->kekurangan;
                $total_estimasi += $estimasi_pembelian;
            }
            
            $table_options = [
                'total_row' => [
                    ['value' => '<strong>TOTAL:</strong>', 'colspan' => '8', 'align' => 'right'],
                    ['value' => '<strong>' . number_format($total_kekurangan, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '<strong>Rp ' . number_format($total_estimasi, 0, ',', '.') . '</strong>', 'align' => 'right']
                ]
            ];
            break;
            
        case 'nilai_stok':
            $table_headers = [
                ['label' => 'No', 'width' => '5%', 'align' => 'center'],
                ['label' => 'Kode Gudang', 'width' => '15%'],
                ['label' => 'Nama Gudang', 'width' => '25%'],
                ['label' => 'Total Item', 'width' => '10%', 'align' => 'right'],
                ['label' => 'Total Qty', 'width' => '10%', 'align' => 'right'],
                ['label' => 'Nilai Stok (Beli)', 'width' => '15%', 'align' => 'right'],
                ['label' => 'Nilai Stok (Jual)', 'width' => '15%', 'align' => 'right'],
                ['label' => 'Potensi Laba', 'width' => '15%', 'align' => 'right']
            ];
            
            $grand_total_item = 0;
            $grand_total_qty = 0;
            $grand_total_beli = 0;
            $grand_total_jual = 0;
            $grand_total_laba = 0;
            
            foreach ($laporan_data->result() as $data) {
                $table_data[] = [
                    $no++,
                    $data->kode_gudang,
                    $data->nama_gudang,
                    number_format($data->total_item, 0, ',', '.'),
                    number_format($data->total_qty, 0, ',', '.'),
                    'Rp ' . number_format($data->total_nilai_beli, 0, ',', '.'),
                    'Rp ' . number_format($data->total_nilai_jual, 0, ',', '.'),
                    'Rp ' . number_format($data->total_potensi_laba, 0, ',', '.')
                ];
                
                $grand_total_item += $data->total_item;
                $grand_total_qty += $data->total_qty;
                $grand_total_beli += $data->total_nilai_beli;
                $grand_total_jual += $data->total_nilai_jual;
                $grand_total_laba += $data->total_potensi_laba;
            }
            
            $table_options = [
                'total_row' => [
                    ['value' => '<strong>GRAND TOTAL:</strong>', 'colspan' => '3', 'align' => 'right'],
                    ['value' => '<strong>' . number_format($grand_total_item, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '<strong>' . number_format($grand_total_qty, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '<strong>Rp ' . number_format($grand_total_beli, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '<strong>Rp ' . number_format($grand_total_jual, 0, ',', '.') . '</strong>', 'align' => 'right'],
                    ['value' => '<strong>Rp ' . number_format($grand_total_laba, 0, ',', '.') . '</strong>', 'align' => 'right']
                ]
            ];
            break;
            
        case 'stok_kosong':
            $table_headers = [
                ['label' => 'No', 'width' => '8%', 'align' => 'center'],
                ['label' => 'Kode Barang', 'width' => '20%'],
                ['label' => 'Nama Barang', 'width' => '40%'],
                ['label' => 'Kode Gudang', 'width' => '16%'],
                ['label' => 'Nama Gudang', 'width' => '16%']
            ];
            
            foreach ($laporan_data->result() as $data) {
                $table_data[] = [
                    $no++,
                    $data->kode_barang,
                    $data->nama_barang,
                    $data->kode_gudang,
                    $data->nama_gudang
                ];
                $stok_kosong_count++;
            }
            break;
    }
}

// Build content
$content = '';
$content .= create_info_section($info_sections);

// Add table only if there's data
if (!empty($table_data)) {
    $table_title = '<span class="icon-package"></span>Detail ' . ($jenis_names[$jenis_laporan] ?? $jenis_laporan);
    $content .= create_data_table($table_title, $table_headers, $table_data, $table_options);
} else {
    $content .= '<div class="notes-section">';
    $content .= '<div class="notes-title">Tidak Ada Data</div>';
    $content .= '<div class="notes-content">Tidak ada data yang ditemukan berdasarkan filter yang dipilih.</div>';
    $content .= '</div>';
}

// Add summary section only if there's data
if (!empty($table_data)) {
    $content .= '<div class="summary-section">';
    $content .= '<div class="summary-left">';

    // Add analysis notes based on report type
    $analysis_content = 'Ringkasan Laporan ' . ($jenis_names[$jenis_laporan] ?? $jenis_laporan) . ':' . "\n";
    $analysis_content .= '• Total item yang dilaporkan: ' . number_format($total_items, 0, ',', '.') . ' item' . "\n";
    
    switch ($jenis_laporan) {
        case 'stok_terkini':
            if ($stok_kosong_count > 0) {
                $analysis_content .= '• Item dengan stok kosong: ' . number_format($stok_kosong_count, 0, ',', '.') . ' item' . "\n";
            }
            if ($stok_minimum_count > 0) {
                $analysis_content .= '• Item dengan stok minimum: ' . number_format($stok_minimum_count, 0, ',', '.') . ' item' . "\n";
            }
            if (isset($total_nilai)) {
                $analysis_content .= '• Total nilai stok: Rp ' . number_format($total_nilai, 0, ',', '.') . "\n";
            }
            break;
            
        case 'movement':
            if (isset($total_masuk)) {
                $analysis_content .= '• Total pergerakan masuk: ' . number_format($total_masuk, 0, ',', '.') . "\n";
            }
            if (isset($total_keluar)) {
                $analysis_content .= '• Total pergerakan keluar: ' . number_format($total_keluar, 0, ',', '.') . "\n";
            }
            break;
            
        case 'stok_minimum':
            if (isset($total_kekurangan)) {
                $analysis_content .= '• Total kekurangan stok: ' . number_format($total_kekurangan, 0, ',', '.') . "\n";
            }
            if (isset($total_estimasi)) {
                $analysis_content .= '• Estimasi nilai pembelian: Rp ' . number_format($total_estimasi, 0, ',', '.') . "\n";
            }
            break;
            
        case 'nilai_stok':
            if (isset($grand_total_item)) {
                $analysis_content .= '• Total item di semua gudang: ' . number_format($grand_total_item, 0, ',', '.') . "\n";
            }
            if (isset($grand_total_qty)) {
                $analysis_content .= '• Total quantity: ' . number_format($grand_total_qty, 0, ',', '.') . "\n";
            }
            if (isset($grand_total_laba)) {
                $analysis_content .= '• Total potensi laba: Rp ' . number_format($grand_total_laba, 0, ',', '.') . "\n";
            }
            break;
            
        case 'stok_kosong':
            $analysis_content .= '• Semua item dalam laporan ini memiliki stok kosong' . "\n";
            $analysis_content .= '• Perlu segera dilakukan pengadaan barang' . "\n";
            break;
    }

    // Add warnings if applicable
    if ($stok_kosong_count > 0 || $stok_minimum_count > 0) {
        $analysis_content .= "\n" . 'Perhatian:' . "\n";
        if ($stok_kosong_count > 0) {
            $analysis_content .= '• Terdapat ' . $stok_kosong_count . ' item dengan stok kosong (ditandai merah)' . "\n";
        }
        if ($stok_minimum_count > 0) {
            $analysis_content .= '• Terdapat ' . $stok_minimum_count . ' item mendekati stok minimum (ditandai kuning)' . "\n";
        }
        $analysis_content .= '• Segera lakukan pemesanan untuk item-item tersebut';
    }

    $content .= create_notes_section('Analisis Laporan', $analysis_content);

    $content .= '</div>';
    $content .= '<div class="summary-right">';

    // Summary table based on report type
    $summary_items = [];
    $summary_items[] = ['label' => 'Total Item', 'value' => number_format($total_items, 0, ',', '.') . ' item'];
    
    switch ($jenis_laporan) {
        case 'stok_terkini':
            if (isset($total_qty)) {
                $summary_items[] = ['label' => 'Total Stok', 'value' => number_format($total_qty, 0, ',', '.')];
            }
            if (isset($total_nilai)) {
                $summary_items[] = ['label' => 'Total Nilai', 'value' => 'Rp ' . number_format($total_nilai, 0, ',', '.'), 'class' => 'total-final'];
            }
            if ($stok_kosong_count > 0) {
                $summary_items[] = ['label' => 'Stok Kosong', 'value' => number_format($stok_kosong_count, 0, ',', '.') . ' item'];
            }
            if ($stok_minimum_count > 0) {
                $summary_items[] = ['label' => 'Stok Minimum', 'value' => number_format($stok_minimum_count, 0, ',', '.') . ' item'];
            }
            break;
            
        case 'movement':
            if (isset($total_masuk)) {
                $summary_items[] = ['label' => 'Total Masuk', 'value' => number_format($total_masuk, 0, ',', '.')];
            }
            if (isset($total_keluar)) {
                $summary_items[] = ['label' => 'Total Keluar', 'value' => number_format($total_keluar, 0, ',', '.')];
            }
            break;
            
        case 'stok_minimum':
            if (isset($total_kekurangan)) {
                $summary_items[] = ['label' => 'Total Kekurangan', 'value' => number_format($total_kekurangan, 0, ',', '.')];
            }
            if (isset($total_estimasi)) {
                $summary_items[] = ['label' => 'Est. Pembelian', 'value' => 'Rp ' . number_format($total_estimasi, 0, ',', '.'), 'class' => 'total-final'];
            }
            break;
            
        case 'nilai_stok':
            if (isset($grand_total_qty)) {
                $summary_items[] = ['label' => 'Total Qty', 'value' => number_format($grand_total_qty, 0, ',', '.')];
            }
            if (isset($grand_total_beli)) {
                $summary_items[] = ['label' => 'Nilai Beli', 'value' => 'Rp ' . number_format($grand_total_beli, 0, ',', '.')];
            }
            if (isset($grand_total_jual)) {
                $summary_items[] = ['label' => 'Nilai Jual', 'value' => 'Rp ' . number_format($grand_total_jual, 0, ',', '.')];
            }
            if (isset($grand_total_laba)) {
                $summary_items[] = ['label' => 'Potensi Laba', 'value' => 'Rp ' . number_format($grand_total_laba, 0, ',', '.'), 'class' => 'total-final'];
            }
            break;
    }

    $content .= create_summary_table($summary_items);

    $content .= '</div>';
    $content .= '</div>';

    // Add legend for stok terkini and stok minimum reports
    if (in_array($jenis_laporan, ['stok_terkini', 'stok_minimum'])) {
        $content .= '<div class="notes-section">';
        $content .= '<div class="notes-title">Keterangan Warna:</div>';
        $content .= '<div class="notes-content">';
        $content .= '<span class="color-danger">■</span> Stok Kosong (0) - Perlu segera diisi ulang<br>';
        $content .= '<span class="color-warning">■</span> Stok Minimum - Mendekati batas minimum<br>';
        $content .= '<span class="color-success">■</span> Stok Normal - Stok dalam kondisi baik';
        $content .= '</div>';
        $content .= '</div>';
    }
}

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => '(............................)',
        'position' => 'Staff Gudang'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Gudang'
    ],
    [
        'title' => 'Mengetahui',
        'name' => '(............................)',
        'position' => 'Manager'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>