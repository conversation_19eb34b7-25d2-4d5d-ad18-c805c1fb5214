<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * COA Setup Controller
 * For setting up additional COA accounts and integration
 */
class Coa_setup extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_coa');
        $this->load->dbforge();
    }

    /**
     * Setup additional COA accounts for better integration
     */
    public function setup_additional_accounts()
    {
        // Check if user has admin access
        if ($this->session->userdata('id_level') != 1) {
            show_error('Access denied. Admin only.', 403);
            return;
        }

        try {
            // Read and execute SQL file
            $sql_file = FCPATH . 'DB/coa_additional_accounts.sql';
            
            if (!file_exists($sql_file)) {
                echo json_encode(array(
                    'status' => 'error',
                    'message' => 'SQL file not found: ' . $sql_file
                ));
                return;
            }

            $sql_content = file_get_contents($sql_file);
            
            // Split SQL statements
            $statements = explode(';', $sql_content);
            
            $executed = 0;
            $errors = array();
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }
                
                try {
                    $this->db->query($statement);
                    $executed++;
                } catch (Exception $e) {
                    $errors[] = 'Error executing: ' . substr($statement, 0, 50) . '... - ' . $e->getMessage();
                }
            }
            
            echo json_encode(array(
                'status' => 'success',
                'message' => "Setup completed. Executed {$executed} statements.",
                'errors' => $errors
            ));
            
        } catch (Exception $e) {
            echo json_encode(array(
                'status' => 'error',
                'message' => 'Setup failed: ' . $e->getMessage()
            ));
        }
    }

    /**
     * Test COA integration
     */
    public function test_integration()
    {
        // Check if user has admin access
        if ($this->session->userdata('id_level') != 1) {
            show_error('Access denied. Admin only.', 403);
            return;
        }

        try {
            $this->load->library('Coa_integration');
            
            $test_results = array();
            
            // Test 1: Create test sales journal
            $sales_data = array(
                'tanggal_faktur' => date('Y-m-d'),
                'nomor_faktur' => 'TEST-SALES-001',
                'total_faktur' => 1000000,
                'total_cogs' => 750000,
                'payment_method' => 'kredit'
            );
            
            $sales_result = $this->coa_integration->process_sales_transaction($sales_data);
            $test_results['sales_test'] = $sales_result;
            
            // Test 2: Create test purchase journal
            $purchase_data = array(
                'tanggal_pembelian' => date('Y-m-d'),
                'nomor_pembelian' => 'TEST-PURCHASE-001',
                'total_akhir' => 800000,
                'payment_method' => 'kredit'
            );
            
            $purchase_result = $this->coa_integration->process_purchase_transaction($purchase_data);
            $test_results['purchase_test'] = $purchase_result;
            
            // Test 3: Create test payment journal
            $payment_data = array(
                'tanggal_pembayaran' => date('Y-m-d'),
                'referensi' => 'TEST-PAYMENT-001',
                'jumlah_pembayaran' => 500000,
                'type' => 'receivable'
            );
            
            $payment_result = $this->coa_integration->process_payment_transaction($payment_data);
            $test_results['payment_test'] = $payment_result;
            
            // Test 4: Create test inventory adjustment journal
            $adjustment_data = array(
                'tanggal' => date('Y-m-d'),
                'referensi' => 'TEST-ADJUSTMENT-001',
                'adjustment_value' => 100000
            );
            
            $adjustment_result = $this->coa_integration->process_inventory_adjustment($adjustment_data);
            $test_results['adjustment_test'] = $adjustment_result;
            
            echo json_encode(array(
                'status' => 'success',
                'message' => 'Integration test completed',
                'results' => $test_results
            ));
            
        } catch (Exception $e) {
            echo json_encode(array(
                'status' => 'error',
                'message' => 'Integration test failed: ' . $e->getMessage()
            ));
        }
    }

    /**
     * Check account balances
     */
    public function check_balances()
    {
        // Check if user has admin access
        if ($this->session->userdata('id_level') != 1) {
            show_error('Access denied. Admin only.', 403);
            return;
        }

        try {
            $this->load->library('Coa_integration');
            
            $key_accounts = array(
                '1-1101' => 'Kas',
                '1-1200' => 'Piutang Usaha',
                '1-1500' => 'Persediaan',
                '2-1100' => 'Hutang Usaha',
                '4-1100' => 'Penjualan',
                '5-1100' => 'Pembelian (HPP)'
            );
            
            $balances = array();
            foreach ($key_accounts as $code => $name) {
                $balance = $this->coa_integration->get_account_balance($code);
                $balances[] = array(
                    'code' => $code,
                    'name' => $name,
                    'balance' => $balance
                );
            }
            
            echo json_encode(array(
                'status' => 'success',
                'balances' => $balances
            ));
            
        } catch (Exception $e) {
            echo json_encode(array(
                'status' => 'error',
                'message' => 'Balance check failed: ' . $e->getMessage()
            ));
        }
    }

    /**
     * Setup page
     */
    public function index()
    {
        // Check if user has admin access
        if ($this->session->userdata('id_level') != 1) {
            show_error('Access denied. Admin only.', 403);
            return;
        }

        $this->template->load('layoutbackend', 'coa/setup');
    }
}
