<!-- Header Info -->
<div class="row mb-3">
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Nomor Transfer</th>
                <td><?= $transfer->nomor_transfer ?></td>
            </tr>
            <tr>
                <th>Tanggal</th>
                <td><?= date('d/m/Y', strtotime($transfer->tanggal_transfer)) ?></td>
            </tr>
            <tr>
                <th>Gudang Asal</th>
                <td><?= $transfer->nama_gudang_asal ?> (<?= $transfer->kode_gudang_asal ?>)</td>
            </tr>
            <tr>
                <th>Gudang Tujuan</th>
                <td><?= $transfer->nama_gudang_tujuan ?> (<?= $transfer->kode_gudang_tujuan ?>)</td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Status</th>
                <td>
                    <?php if ($transfer->status == 'draft'): ?>
                        <span class="badge badge-warning">DRAFT</span>
                    <?php elseif ($transfer->status == 'dikirim'): ?>
                        <span class="badge badge-info">DIKIRIM</span>
                    <?php elseif ($transfer->status == 'diterima'): ?>
                        <span class="badge badge-success">DITERIMA</span>
                    <?php else: ?>
                        <span class="badge badge-danger">BATAL</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <th>Total Item</th>
                <td><?= number_format($transfer->total_item, 0) ?> item</td>
            </tr>
            <tr>
                <th>Total Qty</th>
                <td><?= number_format($transfer->total_qty, 0) ?></td>
            </tr>
            <tr>
                <th>Keterangan</th>
                <td><?= $transfer->keterangan ?></td>
            </tr>
        </table>
    </div>
</div>

<!-- Action buttons moved to modal footer -->
<script>
    $(document).ready(function() {
        // Add action buttons to the modal footer
        var actionButtons = '';
        
        // Print button always appears
        actionButtons += '<button type="button" class="btn btn-primary mr-2" onclick="print_transfer(<?= $transfer->id ?>)">Print</button>';
        
        // Status-specific buttons
        <?php if ($transfer->status == 'draft'): ?>
            actionButtons += '<button type="button" class="btn btn-success mr-2" onclick="kirimFromDetail()"><i class="fas fa-paper-plane"></i> Kirim Transfer</button>';
        <?php elseif ($transfer->status == 'dikirim'): ?>
            actionButtons += '<button type="button" class="btn btn-success mr-2" onclick="terimaFromDetail()"><i class="fas fa-check"></i> Terima Transfer</button>';
        <?php endif; ?>
        
        $('#detail-action-buttons').html(actionButtons);
    });
    
    // Function to print transfer
    function print_transfer(id) {
        window.open("<?= site_url('TransferStok/cetak_transfer/') ?>" + id, "_blank");
    }
</script>

<!-- Detail Table -->
<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title">Item Transfer</h3>
                <?php if ($transfer->status == 'draft'): ?>
                <div class="card-tools">
                    <button type="button" class="btn btn-sm btn-primary" onclick="addDetailItem()">
                        <i class="fas fa-plus"></i> Tambah Item
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="tbl_detail_transfer" class="table table-bordered table-striped table-sm">
                        <thead>
                            <tr class="bg-info">
                                <th>No</th>
                                <th>Kode Barang</th>
                                <th>Nama Barang</th>
                                <th>Qty</th>
                                <th>Satuan</th>
                                <th>Keterangan</th>
                                <?php if ($transfer->status == 'draft'): ?>
                                <th>Aksi</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($detail_list)): ?>
                            <tr>
                                <td colspan="<?= ($transfer->status == 'draft') ? '7' : '6' ?>" class="text-center">Belum ada item</td>
                            </tr>
                            <?php else: ?>
                            <?php $no = 1; foreach ($detail_list as $row): ?>
                            <tr>
                                <td class="text-center"><?= $no++ ?></td>
                                <td><?= $row->kode_barang ?></td>
                                <td><?= $row->nama_barang ?></td>
                                <td class="text-right"><?= number_format($row->qty, 0) ?></td>
                                <td><?= $row->nama_satuan ?></td>
                                <td><?= $row->keterangan ?></td>
                                <?php if ($transfer->status == 'draft'): ?>
                                <td class="text-center">
                                    <button type="button" class="btn btn-xs btn-info" onclick="editDetail(<?= $row->id ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-danger" onclick="deleteDetail(<?= $row->id ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                                <?php endif; ?>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                        <tfoot>
                            <tr class="bg-light">
                                <th colspan="3" class="text-right">Total:</th>
                                <th class="text-right"><?= number_format($transfer->total_qty, 0) ?></th>
                                <th colspan="<?= ($transfer->status == 'draft') ? '3' : '2' ?>"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Form Detail -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Item Transfer</h4>
                <button type="button" class="close" onclick="closeDetailItemModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form_detail">
                    <input type="hidden" name="id" id="detail_id">
                    <input type="hidden" name="id_transfer" value="<?= $transfer->id ?>">
                    <input type="hidden" name="id_gudang" value="<?= $transfer->gudang_asal_id ?>">
                    <input type="hidden" name="satuan_id" id="satuan_id">
                    
                    <div class="form-group" id="barang_group">
                        <label>Barang</label>
                        <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                            <option value="">-- Pilih Barang --</option>
                            <?php foreach ($barang_available as $barang): ?>
                                <option value="<?= $barang->id ?>" data-satuan="<?= $barang->satuan_id ?>">
                                    <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Stok Tersedia</label>
                        <input type="number" class="form-control" name="stok" id="stok" readonly>
                        <small class="form-text text-muted">Akan terisi otomatis setelah memilih barang.</small>
                    </div>
                    
                    <div class="form-group">
                        <label>Qty</label>
                        <input type="number" class="form-control" name="qty" id="qty" required min="0" step="1">
                    </div>
                    
                    <div class="form-group">
                        <label>Keterangan</label>
                        <textarea class="form-control" name="keterangan" id="keterangan" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDetailItemModal()">Batal</button>
                <button type="button" id="btnSaveDetail" onclick="saveDetail()" class="btn btn-primary">Simpan</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Fix for nested modals - improve z-index handling
        $(document).on('show.bs.modal', '.modal', function() {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
        
        // Initialize select2
        $('.select2').select2({
            dropdownParent: $("#modal_form_detail")
        });
    });
    
    var save_method_detail;

    function addDetailItem() {
        save_method_detail = 'add';
        $('#form_detail')[0].reset(); // reset form on modals
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal_form_detail').modal('show'); // show bootstrap modal
        $('.modal-title').text('Tambah Item'); // Set Title to Bootstrap modal title
        $('#btnSaveDetail').text('Simpan'); // change button text
        $('#btnSaveDetail').attr('disabled', false); // set button enable
        $('#barang_group').show(); // show barang selection
    }

    function editDetail(id) {
        save_method_detail = 'update';
        $('#form_detail')[0].reset(); // reset form on modals
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo site_url('TransferStok/edit_detail/') ?>" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="id"]').val(data.id);
                $('[name="id_barang"]').val(data.barang_id).trigger('change');
                $('[name="qty"]').val(data.qty);
                $('[name="keterangan"]').val(data.keterangan);
                $('#satuan_id').val(data.satuan_id); // Set satuan_id dari data yang di-load

                $('#modal_form_detail').modal('show'); // show bootstrap modal when complete loaded
                $('.modal-title').text('Edit Item'); // Set title to Bootstrap modal title
                $('#btnSaveDetail').text('Update'); // change button text
                $('#btnSaveDetail').attr('disabled', false); // set button enable
                $('#barang_group').hide(); // hide barang selection
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function saveDetail() {
        $('#btnSaveDetail').text('saving...'); //change button text
        $('#btnSaveDetail').attr('disabled', true); //set button disable
        var url;

        if (save_method_detail == 'add') {
            url = "<?php echo site_url('TransferStok/insert_detail') ?>";
        } else {
            url = "<?php echo site_url('TransferStok/update_detail') ?>";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form_detail').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    // Close the detail item modal properly
                    closeDetailItemModal();
                    // Update the detail table without closing the parent modal
                    reloadDetailTable();

                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data item berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                    }
                }
                $('#btnSaveDetail').text('save'); //change button text
                $('#btnSaveDetail').attr('disabled', false); //set button enable
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSaveDetail').text('save'); //change button text
                $('#btnSaveDetail').attr('disabled', false); //set button enable
            }
        });
    }

    function deleteDetail(id) {
        Swal.fire({
            title: 'Apakah Anda yakin?',
            text: "Data item yang dihapus tidak dapat dikembalikan!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // Kirim request delete
                $.ajax({
                    url: "<?php echo site_url('TransferStok/delete_detail/') ?>" + id,
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        <?php echo $this->security->get_csrf_token_name(); ?>: '<?php echo $this->security->get_csrf_hash(); ?>'
                    },
                    success: function(response) {
                        if (response.status) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message || 'Data item berhasil dihapus.',
                                icon: 'success'
                            }).then(() => {
                                // Reload tabel detail, bukan seluruh halaman
                                reloadDetailTable(); 
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.message || 'Terjadi kesalahan saat menghapus data.',
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Delete error:', xhr.responseText); // Debug log
                        
                        let errorMessage = 'Terjadi kesalahan saat menghapus data.';
                        
                        // Coba parse response untuk mendapatkan pesan error yang lebih spesifik
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            // Jika tidak bisa parse JSON, gunakan pesan default
                            if (xhr.status === 404) {
                                errorMessage = 'Method tidak ditemukan. Silakan hubungi administrator.';
                            } else if (xhr.status === 500) {
                                errorMessage = 'Terjadi kesalahan server. Silakan coba lagi.';
                            }
                        }
                        
                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    }

    function reloadDetailTable() {
        // Get only the table part to update without closing the modal
        $.ajax({
            url: "<?php echo site_url('TransferStok/get_detail_table/') ?>" + <?= $transfer->id ?>,
            type: "GET",
            success: function(data) {
                // Replace only the table content
                $('.table-responsive').html(data);
                
                // Update the main table in the background if it exists
                if (typeof table !== 'undefined') {
                    table.ajax.reload(null, false);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Reload error:', textStatus, errorThrown);
                
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat ulang data.',
                    icon: 'error'
                });
            }
        });
    }

    function kirimFromDetail() {
        kirim(<?= $transfer->id ?>);
    }

    function terimaFromDetail() {
        terima(<?= $transfer->id ?>);
    }

    // Event handler untuk select barang
    $('#id_barang').change(function() {
        var id_barang = $(this).val();
        var id_gudang = $('[name="id_gudang"]').val();
        var selectedOption = $(this).find('option:selected');
        var satuan_id = selectedOption.data('satuan');
        
        $('#satuan_id').val(satuan_id); // Set hidden input satuan_id

        if (id_barang) {
            $.ajax({
                url: "<?php echo site_url('TransferStok/get_stok_barang') ?>",
                type: "POST",
                dataType: "JSON",
                data: {
                    id_barang: id_barang,
                    id_gudang: id_gudang
                },
                success: function(data) {
                    $('[name="stok"]').val(data.stok);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengambil data stok.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        } else {
            $('[name="stok"]').val('');
        }
    });

    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            dropdownParent: $('#modal_form_detail'),
            placeholder: "-- Pilih --",
            allowClear: true
        });
        
        // Fix for nested modals
        $('#modal_form_detail').on('hidden.bs.modal', function(e) {
            // Prevent event propagation to parent modal
            e.stopPropagation();
            
            // Keep parent modal visible and scrollable
            $('body').addClass('modal-open');
            
            // Fix for backdrop
            if ($('.modal:visible').length > 0) {
                // Ensure parent modal is still visible
                $('#modal_detail').css('display', 'block');
            }
        });
    });
    
    // Function to close only the detail item modal without affecting the parent modal
    function closeDetailItemModal() {
        // Hide the modal without using data-dismiss which can affect parent modals
        $('#modal_form_detail').modal('hide');
        
        // Reset form
        $('#form_detail')[0].reset();
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        
        // Ensure parent modal backdrop stays visible
        $('.modal-backdrop').last().remove();
        
        // Fix scrolling
        setTimeout(function() {
            if ($('.modal:visible').length > 0) {
                $('body').addClass('modal-open');
            }
        }, 100);
    }
</script>