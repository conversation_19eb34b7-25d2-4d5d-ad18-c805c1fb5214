<form action="#" id="form_pembayaran" class="form-horizontal">
    <input type="hidden" name="id_pembelian" value="<?= $pembelian->id ?>">
    
    <!-- Info Pembelian -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Informasi Pembelian</h5>
                <table class="table table-borderless table-sm mb-0">
                    <tr>
                        <td width="25%"><strong>Nomor Pembelian</strong></td>
                        <td>: <?= $pembelian->nomor_pembelian ?></td>
                        <td width="25%"><strong>Supplier</strong></td>
                        <td>: <?= $pembelian->nama_supplier ?></td>
                    </tr>
                    <tr>
                        <td><strong>Total Pembelian</strong></td>
                        <td>: <strong>Rp <?= number_format($pembelian->total_akhir, 0, ',', '.') ?></strong></td>
                        <td><strong>Total Dibayar</strong></td>
                        <td>: Rp <?= number_format($pembelian->total_dibayar, 0, ',', '.') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Sisa Pembayaran</strong></td>
                        <td colspan="3">: <strong class="text-danger">Rp <?= number_format($pembelian->sisa_pembayaran, 0, ',', '.') ?></strong></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="nomor_pembayaran" class="control-label">Nomor Pembayaran <span class="text-red">*</span></label>
                <input type="text" class="form-control" id="nomor_pembayaran" name="nomor_pembayaran" 
                       value="<?= $nomor_pembayaran ?>" readonly>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="tanggal_pembayaran" class="control-label">Tanggal Pembayaran <span class="text-red">*</span></label>
                <input type="date" class="form-control" id="tanggal_pembayaran" name="tanggal_pembayaran" 
                       value="<?= date('Y-m-d') ?>" required>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="jumlah_bayar" class="control-label">Jumlah Bayar <span class="text-red">*</span></label>
                <input type="number" class="form-control" id="jumlah_bayar" name="jumlah_bayar" 
                       placeholder="0" min="0" step="1" max="<?= $pembelian->sisa_pembayaran ?>" required>
                <span class="help-block"></span>
                <small class="text-muted">Maksimal: Rp <?= number_format($pembelian->sisa_pembayaran, 0, ',', '.') ?></small>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="metode_pembayaran" class="control-label">Metode Pembayaran <span class="text-red">*</span></label>
                <select class="form-control select2" id="metode_pembayaran" name="metode_pembayaran" style="width: 100%;" required>
                    <option value="">-- Pilih Metode --</option>
                    <option value="tunai">Tunai</option>
                    <option value="transfer" selected>Transfer Bank</option>
                    <option value="kredit">Kartu Kredit</option>
                    <option value="cek">Cek</option>
                    <option value="giro">Giro</option>
                </select>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="nomor_referensi" class="control-label">Nomor Referensi</label>
                <input type="text" class="form-control" id="nomor_referensi" name="nomor_referensi" 
                       placeholder="Nomor transaksi/referensi">
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="bank_pengirim" class="control-label">Bank Pengirim</label>
                <input type="text" class="form-control" id="bank_pengirim" name="bank_pengirim" 
                       placeholder="Nama bank pengirim">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="bank_penerima" class="control-label">Bank Penerima</label>
                <input type="text" class="form-control" id="bank_penerima" name="bank_penerima" 
                       placeholder="Nama bank penerima">
                <span class="help-block"></span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="jumlah_bayar_display" class="control-label">Jumlah Bayar (Display)</label>
                <input type="text" class="form-control" id="jumlah_bayar_display" 
                       placeholder="Rp 0" readonly style="font-weight: bold; color: #28a745;">
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label for="keterangan" class="control-label">Keterangan</label>
                <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                          placeholder="Keterangan pembayaran"></textarea>
                <span class="help-block"></span>
            </div>
        </div>
    </div>

    <!-- Quick Amount Buttons -->
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label class="control-label">Quick Amount:</label>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="setQuickAmount(<?= $pembelian->sisa_pembayaran ?>)">
                        Lunas (Rp <?= number_format($pembelian->sisa_pembayaran, 0, ',', '.') ?>)
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickAmount(<?= $pembelian->sisa_pembayaran / 2 ?>)">
                        50% (Rp <?= number_format($pembelian->sisa_pembayaran / 2, 0, ',', '.') ?>)
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickAmount(<?= $pembelian->sisa_pembayaran / 4 ?>)">
                        25% (Rp <?= number_format($pembelian->sisa_pembayaran / 4, 0, ',', '.') ?>)
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    $('.select2').select2({
        dropdownParent: $('#modal_pembayaran')
    });

    // Event handler untuk format jumlah bayar
    $('#jumlah_bayar').on('input', function() {
        var jumlah = parseFloat($(this).val()) || 0;
        $('#jumlah_bayar_display').val(formatRupiah(jumlah));
    });

    // Event handler untuk metode pembayaran
    $('#metode_pembayaran').on('change', function() {
        var metode = $(this).val();
        if (metode === 'transfer') {
            $('#nomor_referensi').attr('placeholder', 'Nomor transaksi transfer');
            $('#bank_pengirim').closest('.form-group').show();
            $('#bank_penerima').closest('.form-group').show();
        } else if (metode === 'cek' || metode === 'giro') {
            $('#nomor_referensi').attr('placeholder', 'Nomor ' + metode);
            $('#bank_pengirim').closest('.form-group').show();
            $('#bank_penerima').closest('.form-group').show();
        } else {
            $('#nomor_referensi').attr('placeholder', 'Nomor referensi');
            $('#bank_pengirim').closest('.form-group').hide();
            $('#bank_penerima').closest('.form-group').hide();
        }
    });

    // Trigger change event untuk setup awal
    $('#metode_pembayaran').trigger('change');

    // Fungsi untuk format rupiah
    function formatRupiah(angka) {
        var number_string = angka.toString().replace(/[^,\d]/g, '');
        var split = number_string.split(',');
        var sisa = split[0].length % 3;
        var rupiah = split[0].substr(0, sisa);
        var ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        if (ribuan) {
            var separator = sisa ? '.' : '';
            rupiah += separator + ribuan.join('.');
        }

        rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
        return 'Rp ' + rupiah;
    }

    // Set default keterangan
    $('#keterangan').val('Pembayaran <?= $pembelian->status_pembayaran == "belum_bayar" ? "pertama" : "lanjutan" ?> untuk pembelian <?= $pembelian->nomor_pembelian ?>');
});

// Fungsi untuk set quick amount
function setQuickAmount(amount) {
    $('#jumlah_bayar').val(amount).trigger('input');
}
</script>