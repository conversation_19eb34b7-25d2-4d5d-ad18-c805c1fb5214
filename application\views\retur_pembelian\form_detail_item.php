<?php if (isset($error_message)) : ?>
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle"></i> <?= $error_message ?>
    </div>
<?php else : ?>
    <?php if (empty($received_items)) : ?>
        <div class="alert alert-info">
            <i class="fa fa-info-circle"></i> Tidak ada item yang tersedia untuk diretur.
        </div>
    <?php else : ?>
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr class="bg-light">
                        <th width="5%">No</th>
                        <th width="15%">Kode Barang</th>
                        <th width="20%">Nama Barang</th>
                        <th width="10%">Gudang</th>
                        <th width="10%">Qty Diterima</th>
                        <th width="10%">Qty Retur</th>
                        <th width="10%">Kondisi</th>
                        <th width="10%">Harga Satuan</th>
                        <th width="10%">Keterangan</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $no = 1; foreach ($received_items as $item) : ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td>
                                <?= $item->kode_barang ?>
                                <input type="hidden" name="id_penerimaan_detail[]" value="<?= $item->id ?>">
                                <input type="hidden" name="id_barang[]" value="<?= $item->id_barang ?>">
                            </td>
                            <td><?= $item->nama_barang ?> <?= $item->merk ? '- '.$item->merk : '' ?> <?= $item->tipe ? '- '.$item->tipe : '' ?></td>
                            <td>
                                <select name="id_gudang[]" class="form-control form-control-sm">
                                    <?php foreach ($gudang_list as $gudang) : ?>
                                        <option value="<?= $gudang->id ?>" <?= ($gudang->id == $item->id_gudang_draft) ? 'selected' : '' ?>><?= $gudang->nama_gudang ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm qty-diterima" name="qty_diterima[]" value="<?= $item->qty_available ?>" readonly>
                                <small class="text-muted d-block mt-1">
                                    <i class="fa fa-info-circle"></i> 
                                    Tersedia: <strong><?= number_format($item->qty_available, 0) ?></strong> dari <?= number_format($item->qty_diterima, 0) ?>
                                    <?php if ($item->qty_already_returned > 0) : ?>
                                        <br><span class="text-warning">Sudah diretur (selesai): <?= number_format($item->qty_already_returned, 0) ?></span>
                                    <?php endif; ?>
                                    <?php if ($item->qty_retur_draft > 0) : ?>
                                        <br><span class="text-info">Draft saat ini: <?= number_format($item->qty_retur_draft, 0) ?></span>
                                    <?php endif; ?>
                                </small>
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm qty-retur" name="qty_retur[]" value="<?= $item->qty_retur_draft ?>" min="0" max="<?= $item->qty_available ?>" step="1" onchange="validateQty(this, <?= $item->qty_available ?>)">
                                <small class="text-muted d-block mt-1">
                                    <i class="fa fa-exclamation-triangle text-warning"></i> 
                                    Maksimal: <strong><?= number_format($item->qty_available, 0) ?></strong>
                                </small>
                            </td>
                            <td>
                                <select name="kondisi_barang[]" class="form-control form-control-sm">
                                    <option value="baik" <?= ($item->kondisi_barang_draft == 'baik') ? 'selected' : '' ?>>Baik</option>
                                    <option value="rusak" <?= ($item->kondisi_barang_draft == 'rusak') ? 'selected' : '' ?>>Rusak</option>
                                    <option value="cacat" <?= ($item->kondisi_barang_draft == 'cacat') ? 'selected' : '' ?>>Cacat</option>
                                </select>
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" name="harga_satuan[]" value="<?= $item->harga_satuan ?>" readonly>
                            </td>
                            <td>
                                <input type="text" class="form-control form-control-sm" name="keterangan_item[]" value="<?= $item->keterangan_item_draft ?>" placeholder="Keterangan">
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> Masukkan jumlah barang yang akan diretur. Jumlah tidak boleh melebihi qty yang tersedia.
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>

<script>
    function validateQty(input, max) {
        var value = parseFloat(input.value);
        var row = $(input).closest('tr');
        var namaBarang = row.find('td:nth-child(3)').text().trim();
        
        // Reset border color
        $(input).removeClass('border-danger border-success');
        
        if (isNaN(value) || value < 0) {
            input.value = 0;
            $(input).addClass('border-danger');
            showToast('error', 'Qty retur tidak boleh negatif!');
        } else if (value > max) {
            input.value = max;
            $(input).addClass('border-danger');
            Swal.fire({
                title: 'Peringatan!',
                html: `Qty retur untuk <strong>${namaBarang}</strong> tidak boleh melebihi qty tersedia (<strong>${max}</strong>)<br><small class="text-muted">Nilai telah disesuaikan ke maksimal yang diizinkan</small>`,
                icon: 'warning',
                confirmButtonText: 'OK',
                timer: 5000,
                timerProgressBar: true
            });
        } else if (value > 0) {
            $(input).addClass('border-success');
            //showToast('success', `Qty retur ${namaBarang}: ${value}`);
        }
        

    }
    
    function showToast(type, message) {
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });
        
        Toast.fire({
            icon: type,
            title: message
        });
    }
    

    
    // Initialize validation on page load
    $(document).ready(function() {
        $('.qty-retur').on('input', function() {
            var max = parseFloat($(this).attr('max'));
            validateQty(this, max);
        });
        
        // Initial calculation
        updateTotalCalculation();
    });
</script>