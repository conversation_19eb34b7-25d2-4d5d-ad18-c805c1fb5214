<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Laporan Penjualan
 * Mengatur berbagai jenis laporan penjualan
 */
class Mod_laporan_penjualan extends CI_Model
{
    /**
     * Get pelanggan list untuk dropdown
     */
    function get_pelanggan_list()
    {
        $this->db->select('id, kode, nama');
        $this->db->from('pelanggan');
        $this->db->where('status_aktif', 1);
        $this->db->order_by('nama', 'ASC');
        return $this->db->get();
    }

    /**
     * Get barang list untuk dropdown
     */
    function get_barang_list()
    {
        $this->db->select('id, kode_barang, nama_barang');
        $this->db->from('barang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_barang', 'ASC');
        return $this->db->get();
    }

    /**
     * Get status list untuk dropdown
     */
    function get_status_list()
    {
        return array(
            'draft' => 'Draft',
            'final' => 'Final',
            'dibatalkan' => 'Dibatalkan'
        );
    }

    /**
     * Get summary penjualan untuk dashboard
     */
    function get_summary_penjualan()
    {
        $query = $this->db->query("
            SELECT 
                COUNT(*) as total_transaksi,
                COUNT(DISTINCT id_pelanggan) as total_pelanggan,
                SUM(total_item) as total_item,
                SUM(total_qty) as total_qty,
                SUM(total_faktur) as total_nilai_penjualan,
                COALESCE(SUM(pp.total_dibayar), 0) as total_dibayar,
                SUM(total_faktur) - COALESCE(SUM(pp.total_dibayar), 0) as total_outstanding
            FROM faktur_penjualan fp
            LEFT JOIN (
                SELECT id_faktur_penjualan, SUM(jumlah_pembayaran) as total_dibayar
                FROM faktur_penjualan_pembayaran 
                GROUP BY id_faktur_penjualan
            ) pp ON fp.id = pp.id_faktur_penjualan
            WHERE fp.status != 'dibatalkan'
        ");
        
        if ($query) {
            return $query->row();
        } else {
            // Return default values if query fails
            return (object) array(
                'total_transaksi' => 0,
                'total_pelanggan' => 0,
                'total_item' => 0,
                'total_qty' => 0,
                'total_nilai_penjualan' => 0,
                'total_dibayar' => 0,
                'total_outstanding' => 0
            );
        }
    }

    /**
     * Laporan Penjualan Periode
     */
    function get_laporan_penjualan_periode($id_pelanggan = null, $status = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            fp.id,
            fp.nomor_faktur,
            fp.tanggal_faktur,
            fp.status,
            fp.total_item,
            fp.total_qty,
            fp.subtotal,
            fp.diskon,
            fp.pajak,
            fp.total_faktur,
            p.kode as kode_pelanggan,
            p.nama as nama_pelanggan,
            COALESCE(pp.total_dibayar, 0) as total_dibayar,
            fp.total_faktur - COALESCE(pp.total_dibayar, 0) as sisa_pembayaran,
            CASE 
                WHEN COALESCE(pp.total_dibayar, 0) = 0 THEN "belum_bayar"
                WHEN COALESCE(pp.total_dibayar, 0) >= fp.total_faktur THEN "lunas"
                ELSE "sebagian"
            END as status_pembayaran
        ');
        $this->db->from('faktur_penjualan fp');
        $this->db->join('pelanggan p', 'fp.id_pelanggan = p.id', 'left');
        $this->db->join('(SELECT id_faktur_penjualan, SUM(jumlah_pembayaran) as total_dibayar FROM faktur_penjualan_pembayaran GROUP BY id_faktur_penjualan) pp', 'fp.id = pp.id_faktur_penjualan', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_pelanggan)) {
            $this->db->where('fp.id_pelanggan', $id_pelanggan);
        }
        
        if (!empty($status)) {
            $this->db->where('fp.status', $status);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('fp.tanggal_faktur >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('fp.tanggal_faktur <=', $tanggal_sampai);
        }
        
        $this->db->order_by('fp.tanggal_faktur', 'DESC');
        $this->db->order_by('fp.nomor_faktur', 'ASC');
        
        return $this->db->get();
    }

    /**
     * Laporan Detail Penjualan (Item per Item)
     */
    function get_laporan_detail_penjualan($id_pelanggan = null, $id_barang = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            fp.nomor_faktur,
            fp.tanggal_faktur,
            p.kode as kode_pelanggan,
            p.nama as nama_pelanggan,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            sat.nama_satuan,
            fpd.qty,
            fpd.harga_satuan,
            fpd.diskon_persen,
            fpd.diskon_nilai,
            fpd.subtotal,
            fpd.pajak_persen,
            fpd.pajak_nilai,
            fpd.total
        ');
        $this->db->from('faktur_penjualan_detail fpd');
        $this->db->join('faktur_penjualan fp', 'fpd.id_faktur_penjualan = fp.id');
        $this->db->join('pelanggan p', 'fp.id_pelanggan = p.id', 'left');
        $this->db->join('barang b', 'fpd.id_barang = b.id');
        $this->db->join('satuan sat', 'b.satuan_id = sat.id', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_pelanggan)) {
            $this->db->where('fp.id_pelanggan', $id_pelanggan);
        }
        
        if (!empty($id_barang)) {
            $this->db->where('fpd.id_barang', $id_barang);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('fp.tanggal_faktur >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('fp.tanggal_faktur <=', $tanggal_sampai);
        }
        
        $this->db->where('fp.status !=', 'dibatalkan');
        $this->db->order_by('fp.tanggal_faktur', 'DESC');
        $this->db->order_by('fp.nomor_faktur', 'ASC');
        $this->db->order_by('b.nama_barang', 'ASC');
        
        return $this->db->get();
    }

    /**
     * Laporan Penjualan per Pelanggan (Summary)
     */
    function get_laporan_penjualan_pelanggan($id_pelanggan = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            p.kode as kode_pelanggan,
            p.nama as nama_pelanggan,
            p.alamat,
            p.no_telepon as telepon,
            p.email,
            COUNT(fp.id) as total_transaksi,
            SUM(fp.total_item) as total_item,
            SUM(fp.total_qty) as total_qty,
            SUM(fp.total_faktur) as total_penjualan,
            COALESCE(SUM(pp.total_dibayar), 0) as total_dibayar,
            SUM(fp.total_faktur) - COALESCE(SUM(pp.total_dibayar), 0) as sisa_pembayaran,
            AVG(fp.total_faktur) as rata_rata_penjualan,
            MIN(fp.tanggal_faktur) as penjualan_pertama,
            MAX(fp.tanggal_faktur) as penjualan_terakhir
        ');
        $this->db->from('pelanggan p');
        $this->db->join('faktur_penjualan fp', 'p.id = fp.id_pelanggan');
        $this->db->join('(SELECT id_faktur_penjualan, SUM(jumlah_pembayaran) as total_dibayar FROM faktur_penjualan_pembayaran GROUP BY id_faktur_penjualan) pp', 'fp.id = pp.id_faktur_penjualan', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_pelanggan)) {
            $this->db->where('p.id', $id_pelanggan);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('fp.tanggal_faktur >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('fp.tanggal_faktur <=', $tanggal_sampai);
        }
        
        $this->db->where('fp.status !=', 'dibatalkan');
        $this->db->group_by('p.id, p.kode, p.nama, p.alamat, p.no_telepon, p.email');
        $this->db->order_by('total_penjualan', 'DESC');
        
        return $this->db->get();
    }

    /**
     * Laporan Penjualan per Barang (Summary)
     */
    function get_laporan_penjualan_barang($id_barang = null, $id_pelanggan = null, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            sat.nama_satuan,
            SUM(fpd.qty) as total_qty,
            AVG(fpd.harga_satuan) as harga_rata_rata,
            MIN(fpd.harga_satuan) as harga_terendah,
            MAX(fpd.harga_satuan) as harga_tertinggi,
            SUM(fpd.total) as total_penjualan,
            COUNT(DISTINCT fp.id_pelanggan) as jumlah_pelanggan,
            COUNT(fp.id) as jumlah_transaksi,
            MIN(fp.tanggal_faktur) as penjualan_pertama,
            MAX(fp.tanggal_faktur) as penjualan_terakhir
        ');
        $this->db->from('barang b');
        $this->db->join('faktur_penjualan_detail fpd', 'b.id = fpd.id_barang');
        $this->db->join('faktur_penjualan fp', 'fpd.id_faktur_penjualan = fp.id');
        $this->db->join('satuan sat', 'b.satuan_id = sat.id', 'left');
        
        // Filter berdasarkan parameter
        if (!empty($id_barang)) {
            $this->db->where('b.id', $id_barang);
        }
        
        if (!empty($id_pelanggan)) {
            $this->db->where('fp.id_pelanggan', $id_pelanggan);
        }
        
        if (!empty($tanggal_dari)) {
            $this->db->where('fp.tanggal_faktur >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('fp.tanggal_faktur <=', $tanggal_sampai);
        }
        
        $this->db->where('fp.status !=', 'dibatalkan');
        $this->db->group_by('b.id, b.kode_barang, b.nama_barang, b.merk, b.tipe, sat.nama_satuan');
        $this->db->order_by('total_penjualan', 'DESC');
        
        return $this->db->get();
    }

    /**
     * Laporan Outstanding Invoice
     */
    function get_laporan_outstanding_invoice($id_pelanggan = null)
    {
        $this->db->select('
            fp.nomor_faktur,
            fp.tanggal_faktur,
            fp.jatuh_tempo,
            fp.status,
            fp.total_faktur,
            p.kode as kode_pelanggan,
            p.nama as nama_pelanggan,
            COALESCE(pp.total_dibayar, 0) as total_dibayar,
            fp.total_faktur - COALESCE(pp.total_dibayar, 0) as sisa_pembayaran,
            CASE 
                WHEN COALESCE(pp.total_dibayar, 0) = 0 THEN "belum_bayar"
                WHEN COALESCE(pp.total_dibayar, 0) >= fp.total_faktur THEN "lunas"
                ELSE "sebagian"
            END as status_pembayaran,
            DATEDIFF(CURDATE(), fp.tanggal_faktur) as umur_invoice,
            CASE 
                WHEN fp.jatuh_tempo IS NOT NULL AND CURDATE() > fp.jatuh_tempo THEN "Overdue"
                WHEN fp.jatuh_tempo IS NOT NULL AND DATEDIFF(fp.jatuh_tempo, CURDATE()) <= 7 THEN "Akan Jatuh Tempo"
                ELSE "Normal"
            END as status_jatuh_tempo
        ');
        $this->db->from('faktur_penjualan fp');
        $this->db->join('pelanggan p', 'fp.id_pelanggan = p.id', 'left');
        $this->db->join('(SELECT id_faktur_penjualan, SUM(jumlah_pembayaran) as total_dibayar FROM faktur_penjualan_pembayaran GROUP BY id_faktur_penjualan) pp', 'fp.id = pp.id_faktur_penjualan', 'left');
        
        // Filter hanya invoice yang belum lunas
        $this->db->where('fp.status', 'final');
        $this->db->having('sisa_pembayaran >', 0);
        
        // Filter berdasarkan parameter
        if (!empty($id_pelanggan)) {
            $this->db->where('fp.id_pelanggan', $id_pelanggan);
        }
        
        $this->db->order_by('fp.tanggal_faktur', 'ASC');
        
        return $this->db->get();
    }

    /**
     * Get laporan aging penjualan (berdasarkan umur)
     */
    function get_laporan_aging_penjualan($id_pelanggan = null)
    {
        $this->db->select('
            fp.nomor_faktur,
            fp.tanggal_faktur,
            fp.total_faktur,
            fp.total_faktur - COALESCE(pp.total_dibayar, 0) as sisa_pembayaran,
            p.nama as nama_pelanggan,
            DATEDIFF(CURDATE(), fp.tanggal_faktur) as umur_hari,
            CASE 
                WHEN DATEDIFF(CURDATE(), fp.tanggal_faktur) <= 30 THEN "0-30 Hari"
                WHEN DATEDIFF(CURDATE(), fp.tanggal_faktur) <= 60 THEN "31-60 Hari"
                WHEN DATEDIFF(CURDATE(), fp.tanggal_faktur) <= 90 THEN "61-90 Hari"
                ELSE "Lebih dari 90 Hari"
            END as kategori_aging
        ');
        $this->db->from('faktur_penjualan fp');
        $this->db->join('pelanggan p', 'fp.id_pelanggan = p.id', 'left');
        $this->db->join('(SELECT id_faktur_penjualan, SUM(jumlah_pembayaran) as total_dibayar FROM faktur_penjualan_pembayaran GROUP BY id_faktur_penjualan) pp', 'fp.id = pp.id_faktur_penjualan', 'left');
        $this->db->where('fp.status !=', 'dibatalkan');
        $this->db->having('sisa_pembayaran >', 0);
        
        if (!empty($id_pelanggan)) {
            $this->db->where('fp.id_pelanggan', $id_pelanggan);
        }
        
        $this->db->order_by('umur_hari', 'DESC');
        
        return $this->db->get();
    }

    /**
     * Get top pelanggan berdasarkan nilai penjualan
     */
    function get_top_pelanggan($limit = 10, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            p.nama as nama_pelanggan,
            p.kode as kode_pelanggan,
            COUNT(fp.id) as total_transaksi,
            SUM(fp.total_faktur) as total_penjualan,
            AVG(fp.total_faktur) as rata_rata_penjualan
        ');
        $this->db->from('pelanggan p');
        $this->db->join('faktur_penjualan fp', 'p.id = fp.id_pelanggan');
        
        if (!empty($tanggal_dari)) {
            $this->db->where('fp.tanggal_faktur >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('fp.tanggal_faktur <=', $tanggal_sampai);
        }
        
        $this->db->where('fp.status !=', 'dibatalkan');
        $this->db->group_by('p.id, p.nama, p.kode');
        $this->db->order_by('total_penjualan', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get();
    }

    /**
     * Get top barang berdasarkan qty penjualan
     */
    function get_top_barang($limit = 10, $tanggal_dari = null, $tanggal_sampai = null)
    {
        $this->db->select('
            b.nama_barang,
            b.kode_barang,
            b.merk,
            sat.nama_satuan,
            SUM(fpd.qty) as total_qty,
            SUM(fpd.total) as total_penjualan,
            AVG(fpd.harga_satuan) as harga_rata_rata
        ');
        $this->db->from('barang b');
        $this->db->join('faktur_penjualan_detail fpd', 'b.id = fpd.id_barang');
        $this->db->join('faktur_penjualan fp', 'fpd.id_faktur_penjualan = fp.id');
        $this->db->join('satuan sat', 'b.satuan_id = sat.id', 'left');
        
        if (!empty($tanggal_dari)) {
            $this->db->where('fp.tanggal_faktur >=', $tanggal_dari);
        }
        
        if (!empty($tanggal_sampai)) {
            $this->db->where('fp.tanggal_faktur <=', $tanggal_sampai);
        }
        
        $this->db->where('fp.status !=', 'dibatalkan');
        $this->db->group_by('b.id, b.nama_barang, b.kode_barang, b.merk, sat.nama_satuan');
        $this->db->order_by('total_qty', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get();
    }

    /**
     * Get statistik penjualan bulanan
     */
    function get_statistik_bulanan($tahun = null)
    {
        if (empty($tahun)) {
            $tahun = date('Y');
        }
        
        $this->db->select('
            MONTH(tanggal_faktur) as bulan,
            MONTHNAME(tanggal_faktur) as nama_bulan,
            COUNT(*) as total_transaksi,
            SUM(total_faktur) as total_penjualan,
            AVG(total_faktur) as rata_rata_penjualan
        ');
        $this->db->from('faktur_penjualan');
        $this->db->where('YEAR(tanggal_faktur)', $tahun);
        $this->db->where('status !=', 'dibatalkan');
        $this->db->group_by('MONTH(tanggal_faktur), MONTHNAME(tanggal_faktur)');
        $this->db->order_by('bulan', 'ASC');
        
        return $this->db->get();
    }
}