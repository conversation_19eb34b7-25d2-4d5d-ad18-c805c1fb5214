<div class="modal-header">
    <h4 class="modal-title">Detail Faktur Penjualan</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Informasi Faktur</h3>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-sm">
                        <tr>
                            <th style="width: 150px">Nomor Faktur</th>
                            <td><?= $faktur->nomor_faktur ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal Faktur</th>
                            <td><?= date('d/m/Y', strtotime($faktur->tanggal_faktur)) ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <?php
                                switch ($faktur->status) {
                                    case 'draft':
                                        echo '<span class="badge badge-warning">Draft</span>';
                                        break;
                                    case 'final':
                                        echo '<span class="badge badge-success">Final</span>';
                                        break;
                                    case 'batal':
                                        echo '<span class="badge badge-danger">Batal</span>';
                                        break;
                                    default:
                                        echo '<span class="badge badge-secondary">' . ucfirst($faktur->status) . '</span>';
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Status Pembayaran</th>
                            <td>
                                <?php
                                switch ($faktur->status_pembayaran) {
                                    case 'belum_bayar':
                                        echo '<span class="badge badge-danger">Belum Bayar</span>';
                                        break;
                                    case 'sebagian':
                                        echo '<span class="badge badge-warning">Sebagian</span>';
                                        break;
                                    case 'lunas':
                                        echo '<span class="badge badge-success">Lunas</span>';
                                        break;
                                    default:
                                        echo '<span class="badge badge-secondary">' . ucfirst($faktur->status_pembayaran) . '</span>';
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Jatuh Tempo</th>
                            <td><?= $faktur->jatuh_tempo ? date('d/m/Y', strtotime($faktur->jatuh_tempo)) : '-' ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Informasi Pelanggan</h3>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-sm">
                        <tr>
                            <th style="width: 150px">Pelanggan</th>
                            <td><?= $faktur->nama_pelanggan ? $faktur->nama_pelanggan . ' (' . $faktur->kode_pelanggan . ')' : '-' ?></td>
                        </tr>
                        <tr>
                            <th>Alamat</th>
                            <td><?= $faktur->alamat_pelanggan ?: '-' ?></td>
                        </tr>
                        <tr>
                            <th>Telepon</th>
                            <td><?= $faktur->telepon ?: '-' ?></td>
                        </tr>
                        <tr>
                            <th>Email</th>
                            <td><?= $faktur->email ?: '-' ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Informasi Pengiriman</h3>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-sm">
                        <tr>
                            <th style="width: 150px">No. Pengiriman</th>
                            <td><?= $faktur->nomor_pengiriman ?: '-' ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal Pengiriman</th>
                            <td><?= $faktur->tanggal_pengiriman ? date('d/m/Y', strtotime($faktur->tanggal_pengiriman)) : '-' ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal Diterima</th>
                            <td><?= $faktur->tanggal_diterima ? date('d/m/Y H:i', strtotime($faktur->tanggal_diterima)) : '-' ?></td>
                        </tr>
                        <tr>
                            <th>Penerima</th>
                            <td><?= $faktur->penerima ?: '-' ?></td>
                        </tr>
                        <tr>
                            <th>Alamat Pengiriman</th>
                            <td><?= $faktur->alamat_pengiriman ?: '-' ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Informasi Pesanan</h3>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-sm">
                        <tr>
                            <th style="width: 150px">No. Pesanan</th>
                            <td><?= $faktur->nomor_pesanan ?: '-' ?></td>
                        </tr>
                        <tr>
                            <th>Tanggal Pesanan</th>
                            <td><?= $faktur->tanggal_pesanan ? date('d/m/Y', strtotime($faktur->tanggal_pesanan)) : '-' ?></td>
                        </tr>
                        <tr>
                            <th>Total Item</th>
                            <td><?= number_format($faktur->total_item, 0) ?> item</td>
                        </tr>
                        <tr>
                            <th>Total Qty</th>
                            <td><?= number_format($faktur->total_qty, 0) ?></td>
                        </tr>
                        <tr>
                            <th>Subtotal</th>
                            <td>Rp <?= number_format($faktur->subtotal, 0, ',', '.') ?></td>
                        </tr>
                        <tr>
                            <th>Diskon</th>
                            <td>Rp <?= number_format($faktur->diskon, 0, ',', '.') ?></td>
                        </tr>
                        <tr>
                            <th>PPN</th>
                            <td>Rp <?= number_format($faktur->pajak, 0, ',', '.') ?></td>
                        </tr>
                        <tr>
                            <th>Total Faktur</th>
                            <td><strong>Rp <?= number_format($faktur->total_faktur, 0, ',', '.') ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($faktur->keterangan)): ?>
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title">Keterangan</h3>
                    </div>
                    <div class="card-body">
                        <?= nl2br($faktur->keterangan) ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Item Faktur</h3>
                    <?php if ($faktur->status == 'draft'): ?>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-primary" onclick="addDetailItem()">
                                <i class="fas fa-plus"></i> Tambah Item
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr class="bg-info">
                                    <th>No</th>
                                    <th>Kode Barang</th>
                                    <th>Nama Barang</th>
                                    <th>Qty</th>
                                    <th>Satuan</th>
                                    <th>Harga Satuan</th>
                                    <th>Subtotal</th>
                                    <th>Diskon</th>
                                    <th>Pajak</th>
                                    <th>Total</th>
                                    <?php if ($faktur->status == 'draft'): ?>
                                        <th>Aksi</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($faktur_detail)): ?>
                                    <tr>
                                        <td colspan="<?= ($faktur->status == 'draft') ? '11' : '10' ?>" class="text-center">Belum ada item</td>
                                    </tr>
                                <?php else: ?>
                                    <?php $no = 1;
                                    $total_nilai = 0;
                                    foreach ($faktur_detail as $d): ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td><?= $d->kode_barang ?></td>
                                            <td>
                                                <?= $d->nama_barang ?>
                                                <?php if ($d->merk || $d->tipe): ?>
                                                    <br><small class="text-muted"><?= trim($d->merk . ' ' . $d->tipe) ?></small>
                                                <?php endif; ?>
                                                <?php if ($d->keterangan): ?>
                                                    <br><small class="text-info">Ket: <?= $d->keterangan ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-right"><?= number_format($d->qty, 0) ?></td>
                                            <td><?= $d->nama_satuan ?: '-' ?></td>
                                            <td class="text-right">Rp <?= number_format($d->harga_satuan, 0, ',', '.') ?></td>
                                            <td class="text-right">Rp <?= number_format($d->subtotal, 0, ',', '.') ?></td>
                                            <td class="text-right">
                                                <?php if ($d->diskon_nilai > 0): ?>
                                                    <?= number_format($d->diskon_persen, 1) ?>% <br>
                                                    <small>Rp <?= number_format($d->diskon_nilai, 0, ',', '.') ?></small>
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-right">
                                                <?php if ($d->pajak_nilai > 0): ?>
                                                    <?= number_format($d->pajak_persen, 1) ?>% <br>
                                                    <small>Rp <?= number_format($d->pajak_nilai, 0, ',', '.') ?></small>
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-right">Rp <?= number_format($d->total, 0, ',', '.') ?></td>
                                            <?php if ($faktur->status == 'draft'): ?>
                                                <td>
                                                    <button type="button" class="btn btn-xs btn-info" onclick="editDetailItem(<?= $d->id ?>)"><i class="fas fa-edit"></i></button>
                                                    <button type="button" class="btn btn-xs btn-danger" onclick="deleteDetailItem(<?= $d->id ?>)"><i class="fas fa-trash"></i></button>
                                                </td>
                                            <?php endif; ?>
                                        </tr>
                                        <?php $total_nilai += $d->total; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr class="bg-light">
                                    <th colspan="3" class="text-right">Total:</th>
                                    <th class="text-right"><?= number_format($faktur->total_qty, 0) ?></th>
                                    <th colspan="2"></th>
                                    <th class="text-right">Rp <?= number_format($faktur->subtotal, 0, ',', '.') ?></th>
                                    <th class="text-right">Rp <?= number_format($faktur->diskon, 0, ',', '.') ?></th>
                                    <th class="text-right">Rp <?= number_format($faktur->pajak, 0, ',', '.') ?></th>
                                    <th class="text-right">Rp <?= number_format($faktur->total_faktur, 0, ',', '.') ?></th>
                                    <th colspan="<?= ($faktur->status == 'draft') ? '1' : '0' ?>"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pembayaran Section -->
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Riwayat Pembayaran</h3>
                    <?php if ($faktur->status == 'final' && $faktur->status_pembayaran != 'lunas'): ?>
                        <div class="card-tools">
                            <button type="button" class="btn btn-sm btn-success" onclick="addPayment(<?= $faktur->id ?>)">
                                <i class="fas fa-plus"></i> Tambah Pembayaran
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr class="bg-info">
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Jumlah</th>
                                    <th>Metode</th>
                                    <th>Referensi</th>
                                    <th>Keterangan</th>
                                    <?php if ($faktur->status == 'final'): ?>
                                        <th>Aksi</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($pembayaran_list)): ?>
                                    <tr>
                                        <td colspan="<?= ($faktur->status == 'final') ? '7' : '6' ?>" class="text-center">Belum ada pembayaran</td>
                                    </tr>
                                <?php else: ?>
                                    <?php $no = 1;
                                    $total_pembayaran = 0;
                                    foreach ($pembayaran_list as $p): ?>
                                        <tr>
                                            <td><?= $no++ ?></td>
                                            <td><?= date('d/m/Y', strtotime($p->tanggal_pembayaran)) ?></td>
                                            <td class="text-right">Rp <?= number_format($p->jumlah_pembayaran, 0, ',', '.') ?></td>
                                            <td>
                                                <?php
                                                switch ($p->metode_pembayaran) {
                                                    case 'tunai':
                                                        echo 'Tunai';
                                                        break;
                                                    case 'transfer':
                                                        echo 'Transfer Bank';
                                                        break;
                                                    case 'cek':
                                                        echo 'Cek/Giro';
                                                        break;
                                                    case 'kartu_kredit':
                                                        echo 'Kartu Kredit';
                                                        break;
                                                    case 'kartu_debit':
                                                        echo 'Kartu Debit';
                                                        break;
                                                    default:
                                                        echo ucfirst($p->metode_pembayaran);
                                                }
                                                ?>
                                            </td>
                                            <td><?= $p->referensi_pembayaran ?: '-' ?></td>
                                            <td><?= $p->keterangan ?: '-' ?></td>
                                            <?php if ($faktur->status == 'final'): ?>
                                                <td>
                                                    <button type="button" class="btn btn-xs btn-danger" onclick="deletePayment(<?= $p->id ?>)"><i class="fas fa-trash"></i></button>
                                                </td>
                                            <?php endif; ?>
                                        </tr>
                                        <?php $total_pembayaran += $p->jumlah_pembayaran; ?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr class="bg-light">
                                    <th colspan="2" class="text-right">Total Pembayaran:</th>
                                    <th class="text-right">Rp <?= number_format($total_pembayaran ?? 0, 0, ',', '.') ?></th>
                                    <th colspan="<?= ($faktur->status == 'final') ? '4' : '3' ?>"></th>
                                </tr>
                                <tr class="bg-light">
                                    <th colspan="2" class="text-right">Sisa Tagihan:</th>
                                    <th class="text-right">Rp <?= number_format(($faktur->total_faktur - ($total_pembayaran ?? 0)), 0, ',', '.') ?></th>
                                    <th colspan="<?= ($faktur->status == 'final') ? '4' : '3' ?>"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="card-title">Informasi Tambahan</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered table-sm">
                                <tr>
                                    <th style="width: 150px">Dibuat oleh</th>
                                    <td><?= $faktur->created_by ?: '-' ?></td>
                                </tr>
                                <tr>
                                    <th>Tanggal dibuat</th>
                                    <td><?= $faktur->created_at ? date('d/m/Y H:i:s', strtotime($faktur->created_at)) : '-' ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered table-sm">
                                <tr>
                                    <th style="width: 150px">Diupdate oleh</th>
                                    <td><?= $faktur->updated_by ?: '-' ?></td>
                                </tr>
                                <tr>
                                    <th>Tanggal update</th>
                                    <td><?= $faktur->updated_at ? date('d/m/Y H:i:s', strtotime($faktur->updated_at)) : '-' ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Pembayaran -->
    <div class="modal fade" id="modal_payment" tabindex="-1" role="dialog" aria-labelledby="modal_payment_label" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal_payment_label">Tambah Pembayaran</h5>
                    <button type="button" class="close" onclick="closePaymentModal()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="form_payment">
                        <input type="hidden" name="id_faktur_penjualan" id="payment_faktur_id" value="<?= $faktur->id ?>">

                        <div class="form-group">
                            <label for="tanggal_pembayaran">Tanggal Pembayaran</label>
                            <input type="date" class="form-control" id="tanggal_pembayaran" name="tanggal_pembayaran" value="<?= date('Y-m-d') ?>">
                        </div>

                        <div class="form-group">
                            <label for="jumlah_pembayaran">Jumlah Pembayaran</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">Rp</span>
                                </div>
                                <input type="number" class="form-control" id="jumlah_pembayaran" name="jumlah_pembayaran" min="0" step="1" required>
                            </div>
                            <div class="btn-group btn-group-sm mt-2 w-100">
                                <button type="button" class="btn btn-outline-secondary" onclick="setPaymentAmount('50percent')">50%</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPaymentAmount('remaining')">Sisa</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="setPaymentAmount('all')">Semua</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="metode_pembayaran">Metode Pembayaran</label>
                            <select class="form-control" id="metode_pembayaran" name="metode_pembayaran" required>
                                <option value="tunai">Tunai</option>
                                <option value="transfer">Transfer Bank</option>
                                <option value="kartu_debit">Kartu Debit</option>
                                <option value="kartu_kredit">Kartu Kredit</option>
                                <option value="cek">Cek</option>
                                <option value="lainnya">Lainnya</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="keterangan">Keterangan</label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closePaymentModal()">Batal</button>
                    <button type="button" class="btn btn-primary" id="btnSavePayment" onclick="savePaymentFromDetail()">Simpan</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Modal Form Detail Item -->
<div class="modal fade" id="modal_form_detail_item" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Item Faktur</h4>
                <button type="button" class="close" onclick="closeItemModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="#" id="form_detail_item">
                    <input type="hidden" name="id" id="detail_item_id">
                    <input type="hidden" name="id_faktur_penjualan" value="<?= $faktur->id ?>">

                    <div class="form-group" id="barang_group">
                        <label>Barang</label>
                        <select name="id_barang" id="id_barang" class="form-control select2" style="width: 100%;">
                            <option value="">-- Pilih Barang --</option>
                            <?php foreach ($barang_list as $barang): ?>
                                <option value="<?= $barang->id ?>" data-harga="<?= $barang->harga_jual ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Qty</label>
                        <input type="number" class="form-control" id="qty" name="qty" min="1" step="1" onchange="updateTotalHarga()">
                    </div>

                    <div class="form-group">
                        <label>Harga Satuan</label>
                        <input type="number" class="form-control" id="harga_satuan" name="harga_satuan" min="0" step="1" onchange="updateTotalHarga()">
                    </div>

                    <div class="form-group">
                        <label>Diskon (%)</label>
                        <input type="number" class="form-control" id="diskon_persen" name="diskon_persen" min="0" max="100" step="1" value="0" onchange="updateTotalHarga()">
                    </div>

                    <div class="form-group">
                        <label>Pajak (%)</label>
                        <input type="number" class="form-control" id="pajak_persen" name="pajak_persen" min="0" max="100" step="1" value="0" onchange="updateTotalHarga()">
                    </div>

                    <div class="form-group">
                        <label>Subtotal</label>
                        <div class="form-control-plaintext">
                            <strong id="subtotal_display">0</strong>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Total</label>
                        <div class="form-control-plaintext">
                            <strong id="total_display">0</strong>
                            <small class="text-muted ml-2">(Setelah diskon dan pajak)</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Keterangan</label>
                        <textarea class="form-control" id="keterangan_detail_item" name="keterangan" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeItemModal()">Batal</button>
                <button type="button" class="btn btn-primary" id="btnSaveDetailItem" onclick="saveDetailItem()">Simpan</button>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-info" onclick="printFaktur(<?= $faktur->id ?>)">Print</button>
    <?php if ($faktur->status == 'draft'): ?>
        <button type="button" class="btn btn-success" onclick="updateStatus(<?= $faktur->id ?>, 'final')"><i class="fas fa-check"></i> Finalisasi Faktur</button>
    <?php endif; ?>
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
</div>

<script>
    var save_method_detail_item;
    // current_faktur_id sudah dideklarasikan sebagai variabel global di faktur_penjualan.php
    if (typeof current_faktur_id === 'undefined') {
        var current_faktur_id = <?= $faktur->id ?>;
    }

    $(document).ready(function() {
        // Fix for nested modals - improve z-index handling
        $(document).on('show.bs.modal', '.modal', function() {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });

        // Event handler untuk modal payment telah dipindahkan ke faktur_penjualan.php untuk menghindari konflik

        // Prevent modal backdrop issues when item modal is closed
        $('#modal_form_detail_item').on('hidden.bs.modal', function(e) {
            // Prevent event propagation to parent modal
            e.stopPropagation();

            // Keep parent modal visible and scrollable
            $('body').addClass('modal-open');

            // Fix for backdrop
            if ($('.modal:visible').length > 0) {
                // Ensure parent modal is still visible
                $('#modal_detail').css('display', 'block');

                // Fix backdrop if needed
                if ($('.modal-backdrop').length === 0) {
                    $('body').append('<div class="modal-backdrop fade show"></div>');
                    $('.modal-backdrop').css('z-index', parseInt($('#modal_detail').css('z-index')) - 1);
                }
            }
        });

        // Initialize select2
        $('.select2').select2({
            dropdownParent: $('#modal_form_detail_item')
        });

        // Handle barang selection
        $('#id_barang').change(function() {
            var harga = $(this).find(':selected').data('harga') || 0;
            $('#harga_satuan').val(harga);
            updateTotalHarga();
        });
    });

    function addDetailItem() {
        // Load form in modal
        $.ajax({
            url: "<?php echo site_url('FakturPenjualan/form_detail_item') ?>?id_faktur=<?= $faktur->id ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal_form_detail_item .modal-body').html(data);

                save_method_detail_item = 'add';
                $('#detail_item_id').val('');
                $('.form-group').removeClass('has-error');
                $('.help-block').empty();
                $('#modal_form_detail_item').modal('show');
                $('.modal-title').text('Tambah Item Faktur');

                // Reset select2
                $('#id_barang').val('').trigger('change');

                // Enable barang selection
                $('#barang_group').show();
                $('#id_barang').prop('disabled', false);

                // Initialize select2
                $('.select2').select2({
                    dropdownParent: $('#modal_form_detail_item')
                });

                // Handle barang selection
                $('#id_barang').change(function() {
                    var harga = $(this).find(':selected').data('harga') || 0;
                    $('#harga_satuan').val(harga);
                    updateTotalHarga();
                });

                updateTotalHarga();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat memuat form.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            }
        });
    }

    function editDetailItem(id) {
        // Load form in modal
        $.ajax({
            url: "<?php echo site_url('FakturPenjualan/form_detail_item') ?>?id_faktur=<?= $faktur->id ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal_form_detail_item .modal-body').html(data);

                save_method_detail_item = 'update';
                $('.form-group').removeClass('has-error');
                $('.help-block').empty();

                // Initialize select2
                $('.select2').select2({
                    dropdownParent: $('#modal_form_detail_item')
                });

                // Get detail item data
                $.ajax({
                    url: "<?php echo site_url('FakturPenjualan/get_detail_item') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('#detail_item_id').val(data.id);
                        $('#id_barang').val(data.id_barang).trigger('change');
                        $('#qty').val(data.qty);
                        $('#harga_satuan').val(data.harga_satuan);
                        $('#diskon_persen').val(data.diskon_persen);
                        $('#pajak_persen').val(data.pajak_persen);
                        $('#keterangan').val(data.keterangan);

                        // Disable barang selection on edit
                        $('#barang_group').hide();
                        $('#id_barang').prop('disabled', true);

                        // Handle barang selection
                        $('#id_barang').change(function() {
                            var harga = $(this).find(':selected').data('harga') || 0;
                            $('#harga_satuan').val(harga);
                            updateTotalHarga();
                        });

                        updateTotalHarga();

                        $('#modal_form_detail_item').modal('show');
                        $('.modal-title').text('Edit Item Faktur');
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengambil data dari server.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat memuat form.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            }
        });
    }

    function closeItemModal() {
        $('#modal_form_detail_item').modal('hide');
    }

    function updateTotalHarga() {
        var qty = parseFloat($('#qty').val()) || 0;
        var harga = parseFloat($('#harga_satuan').val()) || 0;
        var diskon_persen = parseFloat($('#diskon_persen').val()) || 0;
        var pajak_persen = parseFloat($('#pajak_persen').val()) || 0;

        var subtotal = qty * harga;
        var diskon_nilai = (diskon_persen / 100) * subtotal;
        var nilai_setelah_diskon = subtotal - diskon_nilai;
        var pajak_nilai = (pajak_persen / 100) * nilai_setelah_diskon;
        var total = nilai_setelah_diskon + pajak_nilai;

        $('#subtotal_display').text('Rp ' + formatNumber(subtotal));
        $('#total_display').text('Rp ' + formatNumber(total));
    }

    function saveDetailItem() {
        $('#btnSaveDetailItem').text('saving...'); //change button text
        $('#btnSaveDetailItem').attr('disabled', true); //set button disable 

        var url;
        if (save_method_detail_item == 'add') {
            url = "<?php echo site_url('FakturPenjualan/add_detail') ?>";
        } else {
            url = "<?php echo site_url('FakturPenjualan/update_detail') ?>";
        }

        var formData = new FormData($('#form_detail_item')[0]);
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(data) {
                if (data.status == 'success') {
                    // Close modal item properly
                    closeItemModal();

                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        // Refresh modal detail without closing it
                        refreshDetailModal(current_faktur_id);
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
                $('#btnSaveDetailItem').text('Simpan'); //change button text
                $('#btnSaveDetailItem').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSaveDetailItem').text('Simpan'); //change button text
                $('#btnSaveDetailItem').attr('disabled', false); //set button enable 
            }
        });
    }

    function deleteDetailItem(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: "Apakah Anda yakin ingin menghapus item ini?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?php echo site_url('FakturPenjualan/delete_detail') ?>",
                    type: "POST",
                    data: {
                        id: id,
                        id_faktur_penjualan: current_faktur_id
                    },
                    dataType: "JSON",
                    success: function(data) {
                        if (data.status == 'success') {
                            Swal.fire({
                                title: 'Terhapus!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                // Update the detail modal content without closing it
                                refreshDetailModal(current_faktur_id);
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    // Function deletePayment dipindahkan ke faktur_penjualan.php untuk akses global

    // Format number function untuk currency display
    function formatNumber(num) {
        if (isNaN(num) || num === null || num === undefined) {
            return '0';
        }
        return parseFloat(num).toLocaleString('id-ID', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
    }

    // Fungsi addPayment dipindahkan ke faktur_penjualan.php untuk menghindari konflik

    // Fungsi untuk menutup modal pembayaran dengan benar
    function closePaymentModal() {
        // Reset form pembayaran
        $('#form_payment')[0].reset();

        // Simply hide the modal - let Bootstrap and event handlers handle the rest
        $('#modal_payment').modal('hide');
    }

    // Fungsi untuk mengatur jumlah pembayaran otomatis
    function setPaymentAmount(type) {
        var totalFaktur = <?= $faktur->total_faktur; ?>;
        var totalPembayaran = <?= $total_pembayaran ?? 0; ?>;
        var sisaTagihan = totalFaktur - totalPembayaran;

        switch (type) {
            case '50percent':
                $('#jumlah_pembayaran').val(sisaTagihan * 0.5);
                break;
            case 'remaining':
                $('#jumlah_pembayaran').val(sisaTagihan);
                break;
            case 'all':
                $('#jumlah_pembayaran').val(totalFaktur);
                break;
        }
    }

    // Fungsi savePaymentFromDetail telah dipindahkan ke faktur_penjualan.php untuk akses global

    function updateStatus(id, status) {
        var statusText = '';
        switch (status) {
            case 'final':
                statusText = 'difinalisasi';
                break;
            case 'batal':
                statusText = 'dibatalkan';
                break;
            default:
                statusText = status;
        }

        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin mengubah status faktur menjadi ' + statusText + '?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Ubah Status',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: '<?= site_url('FakturPenjualan/update_status') ?>',
                    type: 'POST',
                    data: {
                        id: id,
                        status: status
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status == 'success') {
                            Swal.fire({
                                title: 'Status faktur berhasil diubah!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                // Update the detail modal content without closing it
                                refreshDetailModal(current_faktur_id);

                                // Refresh main table
                                if (typeof table !== 'undefined') {
                                    table.ajax.reload(null, false);
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan sistem!',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    // Function to close only the item modal without affecting the parent modal
    function closeItemModal() {
        // Hide the modal without using data-dismiss which can affect parent modals
        $('#modal_form_detail_item').modal('hide');

        // Ensure body keeps modal-open class for parent modal
        $('body').addClass('modal-open');

        // Ensure parent modal backdrop stays visible
        $('.modal-backdrop').last().remove();

        // Fix scrolling
        setTimeout(function() {
            if ($('.modal:visible').length > 0) {
                $('body').addClass('modal-open');
            }
        }, 100);
    }

    // Function refreshDetailModal telah dipindahkan ke faktur_penjualan.php untuk akses global
</script>