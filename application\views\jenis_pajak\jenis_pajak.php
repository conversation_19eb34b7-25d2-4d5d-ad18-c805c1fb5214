<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-list text-blue"></i> Data <PERSON><PERSON></h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_jenis_pajak" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Kode</th>
                                    <th><PERSON><PERSON></th>
                                    <th>Tarif (%)</th>
                                    <th>Tanggal <PERSON>lai</th>
                                    <th>Tanggal Selesai</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>



<script type="text/javascript">
    var save_method; //for save method string
    var table;

    $(document).ready(function() {

        //datatables
        table = $("#tbl_jenis_pajak").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Jenis Pajak Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "jenis_pajak/ajax_list",
                "type": "POST"
            },

        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });

    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $.ajax({
            url: "jenis_pajak/form_input",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);

                // Enable kode field dan show tombol generate saat add
                setTimeout(function() {
                    $('[name="kode_pajak"]').prop('readonly', false);
                    $('#btn-generate-kode').show();
                }, 100);

                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                $('.modal-title').text('Add Jenis Pajak'); // Set title to Bootstrap modal title

            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
        $('#modal_form').modal({
            backdrop: 'static',
            keyboard: false
        }); // show bootstrap modal
        $('.modal-title').text('Add Jenis Pajak'); // Set Title to Bootstrap modal title
    }

    function edit(id) {
        save_method = 'update';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $.ajax({
            url: "jenis_pajak/form_input",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                //Ajax Load data from ajax
                $.ajax({
                    url: "jenis_pajak/edit/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="kode_pajak"]').val(data.kode_pajak);
                        $('[name="nama_pajak"]').val(data.nama_pajak);
                        $('[name="tarif_persen"]').val(data.tarif_persen);
                        $('[name="tanggal_mulai"]').val(data.tanggal_mulai);
                        $('[name="tanggal_selesai"]').val(data.tanggal_selesai);
                        $('[name="keterangan"]').val(data.keterangan);
                        $('[name="aktif"]').prop('checked', data.aktif == 1);

                        // Disable kode field dan hide tombol generate saat edit
                        $('[name="kode_pajak"]').prop('readonly', true);
                        $('#btn-generate-kode').hide();

                        $('#modal_form').modal('show'); // show bootstrap modal when complete loaded
                        $('.modal-title').text('Edit Jenis Pajak'); // Set title to Bootstrap modal title

                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat mengambil data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function reload_table() {
        table.ajax.reload(null, false); //reload datatable ajax
    }

    function save() {
        $('#btnSave').text('saving...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable
        var url;

        if (save_method == 'add') {
            url = "jenis_pajak/insert";
        } else {
            url = "jenis_pajak/update";
        }

        // ajax adding data to database
        var formData = new FormData($('#form')[0]);
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            dataType: "JSON",
            success: function(data) {

                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    reload_table();
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data jenis pajak berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    // Tampilkan error di SweetAlert dan navigasi ke tab yang bermasalah
                    handleValidationErrors(data);
                }
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable


            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('Error saving data:', textStatus, errorThrown);
                console.log('Response:', jqXHR.responseText);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data jenis pajak.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('save'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable

            }
        });
    }

    function hapus(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus data jenis pajak ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // ajax delete data to database
                $.ajax({
                    url: "jenis_pajak/delete",
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        id: id
                    },
                    success: function(data) {
                        if (data.status) {
                            reload_table();
                            Swal.fire({
                                title: 'Berhasil!',
                                text: 'Data jenis pajak berhasil dihapus.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: 'Gagal menghapus data jenis pajak.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    // Function untuk generate kode otomatis
    function generateKode() {
        $.ajax({
            url: "jenis_pajak/generate_kode",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="kode_pajak"]').val(data.kode);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat membuat kode jenis pajak otomatis.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    // Function untuk handle validation errors dengan navigasi tab
    function handleValidationErrors(data) {
        // Mapping field ke tab untuk modul jenis_pajak
        var fieldToTab = {
            'kode_pajak': 'basic',
            'nama_pajak': 'basic',
            'tarif_persen': 'period',
            'tanggal_mulai': 'period',
            'tanggal_selesai': 'period',
            'keterangan': 'detail'
        };

        var errorMessages = [];
        var firstErrorTab = null;

        // Proses setiap error
        for (var i = 0; i < data.inputerror.length; i++) {
            var fieldName = data.inputerror[i];
            var errorMessage = data.error_string[i];

            // Tambahkan class invalid dan pesan error
            $('[name="' + fieldName + '"]').addClass('is-invalid');
            $('[name="' + fieldName + '"]').closest('.kosong').find('.help-block').text(errorMessage);

            // Kumpulkan pesan error untuk toast
            errorMessages.push('• ' + errorMessage);

            // Tentukan tab pertama yang bermasalah
            if (firstErrorTab === null && fieldToTab[fieldName]) {
                firstErrorTab = fieldToTab[fieldName];
            }
        }

        // Tampilkan semua error dalam SweetAlert
        var allErrors = errorMessages.join('<br>');
        Swal.fire({
            title: 'Gagal Menyimpan Data!',
            html: '<div style="text-align: left;"><strong>Terdapat kesalahan pengisian:</strong><br><br>' + allErrors + '</div>',
            icon: 'error',
            confirmButtonText: 'OK',
            width: '500px'
        });

        // Navigasi ke tab yang bermasalah
        if (firstErrorTab) {
            $('#jenisPajakTab a[href="#' + firstErrorTab + '"]').tab('show');

            // Highlight tab yang bermasalah
            $('#jenisPajakTab a[href="#' + firstErrorTab + '"]').addClass('text-danger');
            setTimeout(function() {
                $('#jenisPajakTab a[href="#' + firstErrorTab + '"]').removeClass('text-danger');
            }, 3000);
        }
    }


</script>

<!-- Bootstrap modal -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title"></h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- content akan diload di sini -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- End Bootstrap modal -->
