<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Pesanan',
    'document_number' => $pesanan->nomor_pesanan,
    'document_title' => 'PESANAN PELANGGAN'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Pesanan',
        'items' => [
            ['label' => 'Nomor Pesanan', 'value' => '<strong>' . $pesanan->nomor_pesanan . '</strong>'],
            ['label' => 'Tanggal Pesanan', 'value' => format_date_indonesia($pesanan->tanggal_pesanan)],
            ['label' => '<PERSON><PERSON> Pesanan', 'value' => ucfirst($pesanan->jenis_pesanan)],
            ['label' => 'Status', 'value' => get_status_badge($pesanan->status)]
        ]
    ],
    [
        'title' => '<span class="icon-user"></span>Informasi Pelanggan',
        'items' => [
            ['label' => 'Nama Pelanggan', 'value' => '<strong>' . $pesanan->nama_pelanggan . '</strong> (' . $pesanan->kode_pelanggan . ')'],
            ['label' => 'Alamat', 'value' => $pesanan->alamat_pelanggan ?: '-'],
            ['label' => 'Total Item', 'value' => number_format($pesanan->total_item, 0) . ' item'],
            ['label' => 'Total Qty', 'value' => number_format($pesanan->total_qty, 0)]
        ]
    ],
    [
        'title' => '<span class="icon-calculator"></span>Ringkasan Pembayaran',
        'items' => [
            ['label' => 'Subtotal', 'value' => format_currency($pesanan->subtotal ?? 0)],
            ['label' => 'PPN (' . number_format($pesanan->ppn_persen ?? 11, 1) . '%)', 'value' => format_currency($pesanan->ppn_nominal ?? 0)],
            ['label' => 'Total Akhir', 'value' => '<strong>' . format_currency($pesanan->total_akhir ?? 0) . '</strong>']
        ]
    ]
];

// Add notes if available
if (!empty($pesanan->keterangan)) {
    $info_sections[0]['items'][] = ['label' => 'Keterangan', 'value' => $pesanan->keterangan];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '4%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '32%'],
    ['label' => 'Qty', 'width' => '8%', 'align' => 'right'],
    ['label' => 'Satuan', 'width' => '8%', 'align' => 'center'],
    ['label' => 'Harga Satuan', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Subtotal', 'width' => '12%', 'align' => 'right'],
    ['label' => 'PPN', 'width' => '12%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_subtotal = 0;
$total_ppn = 0;
$total_akhir = 0;

if (!empty($detail)) {
    foreach ($detail as $d) {
        $nama_barang = $d->nama_barang;
        if ($d->merk || $d->tipe) {
            $nama_barang .= '<br><small class="color-muted">' . trim($d->merk . ' ' . $d->tipe) . '</small>';
        }
        if ($d->keterangan) {
            $nama_barang .= '<br><small class="color-muted font-italic">Ket: ' . $d->keterangan . '</small>';
        }
        
        // Hitung PPN per item
        $ppn_item = $d->ppn_nominal ?? 0;
        $total_item = $d->total_setelah_ppn ?? $d->subtotal;
        
        $table_data[] = [
            $no++,
            $d->kode_barang,
            $nama_barang,
            number_format($d->qty, 0),
            $d->nama_satuan ?: '-',
            format_currency($d->harga_satuan),
            format_currency($d->subtotal),
            format_currency($ppn_item)
        ];
        
        $total_subtotal += $d->subtotal;
        $total_ppn += $ppn_item;
        $total_akhir += $total_item;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL</strong>', 'colspan' => '6', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_subtotal) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_ppn) . '</strong>', 'align' => 'right']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-package"></span>Detail Item Pesanan', $table_headers, $table_data, $table_options);

// Tambahkan ringkasan total
$summary_items = [
    ['label' => 'Subtotal', 'value' => format_currency($total_subtotal)],
    ['label' => 'PPN (' . number_format($pesanan->ppn_persen ?? 11, 1) . '%)', 'value' => format_currency($total_ppn)],
    ['label' => 'TOTAL AKHIR', 'value' => format_currency($pesanan->total_akhir ?? $total_akhir), 'class' => 'total-final']
];
$content .= '<div style="margin-top: 20px; float: right; width: 300px;">';
$content .= create_summary_table($summary_items);
$content .= '</div>';
$content .= '<div style="clear: both;"></div>';

// Add notes section
$content .= create_notes_section('Catatan Penting', 
    '• Pesanan ini berlaku sesuai dengan syarat dan ketentuan yang berlaku' . "\n" .
    '• Harga sudah termasuk PPN (' . number_format($pesanan->ppn_persen ?? 11, 1) . '%)' . "\n" .
    '• Harap simpan dokumen ini sebagai bukti pesanan' . "\n" .
    '• Untuk informasi lebih lanjut, hubungi customer service kami' . "\n" .
    '• Konfirmasi ketersediaan barang akan diberikan dalam 1x24 jam');

// Signatures
$signatures = [
    [
        'title' => 'Pelanggan',
        'name' => $pesanan->nama_pelanggan,
        'position' => 'Pemesan'
    ],
    [
        'title' => 'Petugas',
        'name' => $pesanan->created_by ?: '(............................)',
        'position' => 'Customer Service'
    ]
];

$content .= create_signature_section($signatures);

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>