<?php
// Load print helper
$this->load->helper('print');

// Get data from controller
$pembelian = $pembelian ?? null;
$detail = $detail ?? [];

if (!$pembelian) {
    echo '<div class="alert alert-danger">Data pembelian tidak ditemukan!</div>';
    return;
}

// Prepare data untuk template
$template_data = [
    'title' => 'Print Purchase Order',
    'document_number' => $pembelian->nomor_pembelian ?? 'PO-' . date('Ymd-His'),
    'document_title' => 'PURCHASE ORDER'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Purchase Order',
        'items' => [
            ['label' => 'Nomor Pembelian', 'value' => '<strong>' . ($pembelian->nomor_pembelian ?? '-') . '</strong>'],
            ['label' => 'Tanggal Pembelian', 'value' => format_date_indonesia($pembelian->tanggal_pembelian ?? date('Y-m-d'))],
            ['label' => 'Jenis Pembelian', 'value' => ucfirst($pembelian->jenis_pembelian ?? 'reguler')],
            ['label' => 'Status', 'value' => get_status_badge($pembelian->status ?? 'draft')],
            ['label' => 'Jatuh Tempo', 'value' => !empty($pembelian->tanggal_jatuh_tempo) ? format_date_indonesia($pembelian->tanggal_jatuh_tempo) : '-']
        ]
    ]
];

// Add supplier info if available
if (!empty($pembelian->nama_supplier)) {
    $info_sections[] = [
        'title' => '<span class="icon-user"></span>Informasi Supplier',
        'items' => [
            ['label' => 'Nama Supplier', 'value' => '<strong>' . $pembelian->nama_supplier . '</strong>'],
            ['label' => 'Kode Supplier', 'value' => $pembelian->kode_supplier ?? '-'],
            ['label' => 'Alamat', 'value' => $pembelian->alamat_supplier ?? '-'],
            ['label' => 'Telepon', 'value' => $pembelian->telepon_supplier ?? '-'],
            ['label' => 'Email', 'value' => $pembelian->email_supplier ?? '-']
        ]
    ];
}

// Add delivery info if available
$delivery_items = [];
if (!empty($pembelian->alamat_pengiriman)) {
    $delivery_items[] = ['label' => 'Alamat Pengiriman', 'value' => $pembelian->alamat_pengiriman];
}
if (!empty($pembelian->syarat_pembayaran)) {
    $delivery_items[] = ['label' => 'Syarat Pembayaran', 'value' => $pembelian->syarat_pembayaran];
}
if (!empty($pembelian->metode_pembayaran)) {
    $delivery_items[] = ['label' => 'Metode Pembayaran', 'value' => ucfirst($pembelian->metode_pembayaran)];
}
if (!empty($pembelian->nomor_po_supplier)) {
    $delivery_items[] = ['label' => 'No. PO Supplier', 'value' => $pembelian->nomor_po_supplier];
}

if (!empty($delivery_items)) {
    $info_sections[] = [
        'title' => '<span class="icon-truck"></span>Informasi Tambahan',
        'items' => $delivery_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Barang', 'width' => '12%'],
    ['label' => 'Nama Barang', 'width' => '30%'],
    ['label' => 'Gudang', 'width' => '12%'],
    ['label' => 'Satuan', 'width' => '8%', 'align' => 'center'],
    ['label' => 'Qty', 'width' => '8%', 'align' => 'right'],
    ['label' => 'Harga Satuan', 'width' => '12%', 'align' => 'right'],
    ['label' => 'Diskon', 'width' => '8%', 'align' => 'right'],
    ['label' => 'Total', 'width' => '13%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_qty = 0;
$total_nilai = 0;

if (!empty($detail)) {
    foreach ($detail as $item) {
        $nama_barang = $item->nama_barang ?? 'Barang tidak diketahui';
        if (!empty($item->merk)) {
            $nama_barang .= ' - ' . $item->merk;
        }
        if (!empty($item->tipe)) {
            $nama_barang .= ' (' . $item->tipe . ')';
        }
        if (!empty($item->spesifikasi)) {
            $nama_barang .= '<br><small class="color-muted">Spec: ' . $item->spesifikasi . '</small>';
        }
        
        $qty = $item->qty ?? 0;
        $harga_satuan = $item->harga_satuan ?? 0;
        $diskon_nominal = $item->diskon_nominal ?? 0;
        $diskon_persen = $item->diskon_persen ?? 0;
        $total_akhir = $item->total_akhir ?? ($qty * $harga_satuan - $diskon_nominal);
        
        $diskon_display = '-';
        if ($diskon_nominal > 0) {
            $diskon_display = number_format($diskon_persen, 1) . '%<br>';
            $diskon_display .= '<small>' . format_currency($diskon_nominal) . '</small>';
        }
        
        $table_data[] = [
            $no++,
            $item->kode_barang ?? '-',
            $nama_barang,
            $item->nama_gudang ?? '-',
            $item->nama_satuan ?? '-',
            number_format($qty, 0, ',', '.'),
            format_currency($harga_satuan),
            $diskon_display,
            format_currency($total_akhir)
        ];
        
        $total_qty += $qty;
        $total_nilai += $total_akhir;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL:</strong>', 'colspan' => '5', 'align' => 'right'],
        ['value' => '<strong>' . number_format($total_qty, 0, ',', '.') . '</strong>', 'align' => 'right'],
        ['value' => '', 'colspan' => '2', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_nilai) . '</strong>', 'align' => 'right']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);

// Add table only if there's data
if (!empty($table_data)) {
    $content .= create_data_table('<span class="icon-package"></span>Detail Item Purchase Order', $table_headers, $table_data, $table_options);
} else {
    $content .= '<div class="notes-section">';
    $content .= '<div class="notes-title">Tidak Ada Data</div>';
    $content .= '<div class="notes-content">Tidak ada detail item dalam purchase order ini.</div>';
    $content .= '</div>';
}

// Add summary section only if there's data
if (!empty($table_data)) {
    $content .= '<div class="summary-section">';

    // Left side - Terms and conditions
    $content .= '<div class="summary-left">';

    // Terms and conditions
    $terms_content = '';
    if (!empty($pembelian->syarat_pembayaran)) {
        $terms_content .= 'Syarat Pembayaran: ' . $pembelian->syarat_pembayaran . "\n";
    }
    if (!empty($pembelian->metode_pembayaran)) {
        $terms_content .= 'Metode Pembayaran: ' . ucfirst($pembelian->metode_pembayaran) . "\n";
    }
    $terms_content .= "\n" . 'Ketentuan Umum:' . "\n";
    $terms_content .= '• Barang harus dikirim sesuai dengan spesifikasi yang diminta' . "\n";
    $terms_content .= '• Pengiriman harus tepat waktu sesuai jadwal yang disepakati' . "\n";
    $terms_content .= '• Barang yang tidak sesuai spesifikasi akan dikembalikan' . "\n";
    $terms_content .= '• Pembayaran akan dilakukan setelah barang diterima dengan baik' . "\n";
    $terms_content .= '• Supplier wajib memberikan garansi sesuai ketentuan yang berlaku';

    $content .= create_notes_section('Syarat dan Ketentuan', $terms_content);

    // Notes if available
    if (!empty($pembelian->keterangan)) {
        $content .= create_notes_section('Catatan Khusus', $pembelian->keterangan);
    }

    $content .= '</div>';

    // Right side - Summary
    $content .= '<div class="summary-right">';

    $summary_items = [
        ['label' => 'Total Item', 'value' => count($table_data) . ' item'],
        ['label' => 'Total Quantity', 'value' => number_format($total_qty, 0, ',', '.')],
        ['label' => 'Total Nilai', 'value' => format_currency($total_nilai), 'class' => 'total-final']
    ];

    // Add additional totals if available
    if (!empty($pembelian->total_akhir)) {
        $summary_items[] = ['label' => 'Grand Total', 'value' => format_currency($pembelian->total_akhir), 'class' => 'total-final'];
    }

    $content .= create_summary_table($summary_items);

    $content .= '</div>';
    $content .= '</div>';
}

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => $pembelian->created_by ?? '(............................)',
        'position' => 'Staff Purchasing'
    ],
    [
        'title' => 'Disetujui Oleh',
        'name' => $pembelian->approved_by ?? '(............................)',
        'position' => 'Manager Purchasing'
    ],
    [
        'title' => 'Diterima Oleh',
        'name' => '(............................)',
        'position' => $pembelian->nama_supplier ?? 'Supplier'
    ]
];

$content .= create_signature_section($signatures);

// Footer note
$content .= '<div class="notes-section mt-20">';
$content .= '<div class="text-center color-muted text-small">';
$content .= 'Purchase Order ini sah dan mengikat kedua belah pihak.<br>';
$content .= 'Harap konfirmasi penerimaan PO ini dalam waktu 2x24 jam.<br>';
$content .= 'Untuk informasi lebih lanjut, hubungi bagian purchasing.';
$content .= '</div>';
$content .= '</div>';

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>