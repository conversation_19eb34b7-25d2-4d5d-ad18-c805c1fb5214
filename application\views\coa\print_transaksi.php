<?php
// Load print helper
$this->load->helper('print');

// Prepare data untuk template
$template_data = [
    'title' => 'Print Transaksi',
    'document_number' => $transaksi->nomor_transaksi,
    'document_title' => 'VOUCHER TRANSAKSI KEUANGAN'
];

// Prepare info sections
$info_sections = [
    [
        'title' => '<span class="icon-document"></span>Informasi Transaksi',
        'items' => [
            ['label' => 'Nomor Transaksi', 'value' => '<strong>' . $transaksi->nomor_transaksi . '</strong>'],
            ['label' => 'Tanggal Transaksi', 'value' => format_date_indonesia($transaksi->tanggal_transaksi)],
            ['label' => 'Jenis Transaksi', 'value' => ucfirst($transaksi->jenis_transaksi)],
            ['label' => 'Status', 'value' => get_status_badge($transaksi->status)]
        ]
    ],
    [
        'title' => '<span class="icon-money"></span>Informasi Keuangan',
        'items' => [
            ['label' => 'Total Debit', 'value' => '<strong>' . format_currency($transaksi->total_debit) . '</strong>'],
            ['label' => 'Total Kredit', 'value' => '<strong>' . format_currency($transaksi->total_kredit) . '</strong>'],
            ['label' => 'Selisih', 'value' => format_currency($transaksi->total_debit - $transaksi->total_kredit)],
            ['label' => 'Mata Uang', 'value' => $transaksi->mata_uang ?? 'IDR']
        ]
    ]
];

// Add reference info if available
if (!empty($transaksi->referensi) || !empty($transaksi->dibuat_oleh)) {
    $ref_items = [];
    if (!empty($transaksi->referensi)) {
        $ref_items[] = ['label' => 'Referensi', 'value' => $transaksi->referensi];
    }
    if (!empty($transaksi->dibuat_oleh)) {
        $ref_items[] = ['label' => 'Dibuat Oleh', 'value' => $transaksi->dibuat_oleh];
    }
    if (!empty($transaksi->disetujui_oleh)) {
        $ref_items[] = ['label' => 'Disetujui Oleh', 'value' => $transaksi->disetujui_oleh];
    }
    if (!empty($transaksi->tanggal_disetujui)) {
        $ref_items[] = ['label' => 'Tanggal Disetujui', 'value' => format_date_indonesia($transaksi->tanggal_disetujui, true)];
    }
    
    $info_sections[] = [
        'title' => '<span class="icon-user"></span>Informasi Tambahan',
        'items' => $ref_items
    ];
}

// Prepare table headers
$table_headers = [
    ['label' => 'No', 'width' => '5%', 'align' => 'center'],
    ['label' => 'Kode Akun', 'width' => '12%'],
    ['label' => 'Nama Akun', 'width' => '30%'],
    ['label' => 'Keterangan', 'width' => '25%'],
    ['label' => 'Debit', 'width' => '14%', 'align' => 'right'],
    ['label' => 'Kredit', 'width' => '14%', 'align' => 'right']
];

// Prepare table data
$table_data = [];
$no = 1;
$total_debit = 0;
$total_kredit = 0;

if (!empty($detail_list)) {
    foreach ($detail_list as $detail) {
        $nama_akun = $detail->nama_akun;
        if ($detail->sub_akun) {
            $nama_akun .= '<br><small class="color-muted">Sub: ' . $detail->sub_akun . '</small>';
        }
        
        $debit_display = $detail->debit > 0 ? format_currency($detail->debit) : '-';
        $kredit_display = $detail->kredit > 0 ? format_currency($detail->kredit) : '-';
        
        $table_data[] = [
            $no++,
            $detail->kode_akun,
            $nama_akun,
            $detail->keterangan ?: '-',
            $debit_display,
            $kredit_display
        ];
        
        $total_debit += $detail->debit;
        $total_kredit += $detail->kredit;
    }
}

// Table options with total row
$table_options = [
    'total_row' => [
        ['value' => '<strong>TOTAL:</strong>', 'colspan' => '4', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_debit) . '</strong>', 'align' => 'right'],
        ['value' => '<strong>' . format_currency($total_kredit) . '</strong>', 'align' => 'right']
    ]
];

// Build content
$content = '';
$content .= create_info_section($info_sections);
$content .= create_data_table('<span class="icon-money"></span>Detail Jurnal Transaksi', $table_headers, $table_data, $table_options);

// Add validation section
$is_balanced = ($total_debit == $total_kredit);
$content .= '<div class="summary-section">';
$content .= '<div class="summary-left">';

// Add notes if available
if (!empty($transaksi->keterangan)) {
    $content .= create_notes_section('Keterangan Transaksi', $transaksi->keterangan);
}

// Add validation notes
$validation_content = 'Validasi Transaksi:' . "\n";
$validation_content .= '• Total Debit: ' . format_currency($total_debit) . "\n";
$validation_content .= '• Total Kredit: ' . format_currency($total_kredit) . "\n";
$validation_content .= '• Selisih: ' . format_currency($total_debit - $total_kredit) . "\n";

if ($is_balanced) {
    $validation_content .= '• Status: ✅ SEIMBANG - Transaksi valid' . "\n";
} else {
    $validation_content .= '• Status: ❌ TIDAK SEIMBANG - Perlu koreksi' . "\n";
}

$validation_content .= "\n" . 'Catatan:' . "\n";
$validation_content .= '• Transaksi harus seimbang (Debit = Kredit)' . "\n";
$validation_content .= '• Semua akun harus sesuai dengan chart of accounts' . "\n";
$validation_content .= '• Dokumen pendukung harus dilampirkan';

$content .= create_notes_section('Validasi & Catatan', $validation_content);

$content .= '</div>';
$content .= '<div class="summary-right">';

// Summary table
$summary_items = [
    ['label' => 'Total Debit', 'value' => format_currency($total_debit)],
    ['label' => 'Total Kredit', 'value' => format_currency($total_kredit)],
    ['label' => 'Selisih', 'value' => format_currency($total_debit - $total_kredit)],
    ['label' => 'Status', 'value' => $is_balanced ? '<span class="color-success">SEIMBANG</span>' : '<span class="color-danger">TIDAK SEIMBANG</span>', 'class' => 'total-final']
];

$content .= create_summary_table($summary_items);

$content .= '</div>';
$content .= '</div>';

// Signatures
$signatures = [
    [
        'title' => 'Dibuat Oleh',
        'name' => $transaksi->dibuat_oleh ?? '(............................)',
        'position' => 'Staff Accounting'
    ],
    [
        'title' => 'Diperiksa Oleh',
        'name' => '(............................)',
        'position' => 'Supervisor Accounting'
    ],
    [
        'title' => 'Disetujui Oleh',
        'name' => $transaksi->disetujui_oleh ?? '(............................)',
        'position' => 'Manager Finance'
    ]
];

$content .= create_signature_section($signatures);

// Add accounting principles note
$content .= '<div class="notes-section mt-20">';
$content .= '<div class="text-center color-muted text-small">';
$content .= 'Voucher ini dibuat berdasarkan prinsip akuntansi yang berlaku umum.<br>';
$content .= 'Setiap transaksi harus didukung dengan dokumen yang sah dan dapat dipertanggungjawabkan.<br>';
$content .= 'Dokumen ini merupakan bagian dari sistem pencatatan keuangan perusahaan.';
$content .= '</div>';
$content .= '</div>';

// Set content to template data
$template_data['content'] = $content;

// Load and display template
echo load_print_template($template_data);
?>