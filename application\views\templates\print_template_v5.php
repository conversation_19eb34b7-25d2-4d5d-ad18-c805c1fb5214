<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'Dokumen Print' ?></title>
    <style>
        /* Reset dan pengaturan dasar */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #1a1a1a;
            background: #ffffff;
            font-weight: 400;
            letter-spacing: 0.3px;
        }

        /* Pengaturan untuk media cetak */
        @media print {
            body {
                margin: 0;
                padding: 12mm;
                font-size: 11px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .print-controls {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-print {
                display: none !important;
            }

            .shadow-element {
                box-shadow: none !important;
            }
        }

        /* Header perusa<PERSON><PERSON> dengan desain elegan */
        .company-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px 30px;
            border: 1px solid #2c3e50;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .company-header::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid #34495e;
            border-radius: 4px;
            pointer-events: none;
        }

        .company-name {
            font-size: 24px;
            font-weight: 800;
            color: #2c3e50;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 3px;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .company-info {
            font-size: 12px;
            color: #495057;
            line-height: 1.6;
            font-weight: 500;
            margin-top: 8px;
        }

        .document-title {
            font-size: 20px;
            font-weight: 700;
            text-align: center;
            margin: 30px 0;
            padding: 18px 25px;
            border: 1px solid #2c3e50;
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            border-radius: 6px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.2);
        }

        .document-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 25px;
            right: 25px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 2px;
        }

        /* Bagian informasi dengan desain modern */
        .info-section {
            margin-bottom: 30px;
        }

        .info-row {
            display: flex;
            margin-bottom: 20px;
            gap: 25px;
        }

        .info-box {
            flex: 1;
            border: 1px solid #34495e;
            padding: 20px 22px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            position: relative;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .info-box::before {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            right: 6px;
            bottom: 6px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            pointer-events: none;
        }

        .info-box h4 {
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.8px;
        }

        .info-item {
            display: flex;
            margin-bottom: 6px;
            font-size: 11px;
            align-items: baseline;
        }

        .info-label {
            width: 110px;
            font-weight: 600;
            color: #000000;
            text-transform: uppercase;
            font-size: 10px;
            letter-spacing: 0.3px;
        }

        .info-value {
            flex: 1;
            color: #1a1a1a;
            font-weight: 500;
            padding-left: 8px;
        }

        .info-separator {
            margin: 0 6px;
            color: #000000;
            font-weight: 700;
        }

        /* Badge status dengan desain modern */
        .status-badge {
            display: inline-block;
            padding: 6px 15px;
            font-size: 11px;
            font-weight: 700;
            border: 1px solid #34495e;
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            color: #2c3e50;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        /* Container tabel dengan header modern */
        .table-container {
            margin-bottom: 30px;
        }

        .table-title {
            font-size: 15px;
            font-weight: 700;
            margin-bottom: 15px;
            padding: 15px 22px;
            border: 1px solid #2c3e50;
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            border-radius: 6px 6px 0 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* Tabel data dengan styling elegan */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 12px;
            border: 1px solid #34495e;
            border-radius: 0 0 6px 6px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: 1px solid #2980b9;
            padding: 14px 10px;
            text-align: left;
            font-weight: 700;
            color: #ffffff;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table td {
            border: 1px solid #bdc3c7;
            padding: 12px 10px;
            vertical-align: top;
            color: #2c3e50;
            font-weight: 500;
        }

        .data-table tr:nth-child(even) {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .data-table tr:nth-child(odd) {
            background: #ffffff;
        }

        .data-table tr:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }

        /* Baris total dengan emphasis modern */
        .total-row {
            font-weight: 700;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
            color: #ffffff !important;
        }

        .total-row td {
            border: 1px solid #c0392b;
            color: #ffffff !important;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
        }

        /* Perataan teks */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }

        /* Bagian ringkasan dengan layout yang balance */
        .summary-section {
            display: flex;
            margin-top: 25px;
            margin-bottom: 25px;
            gap: 25px;
        }

        .summary-left {
            flex: 1;
        }

        .summary-right {
            width: 320px;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000000;
        }

        .summary-table td {
            border: 1px solid #000000;
            padding: 10px 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .summary-table .summary-label {
            background: #f0f0f0;
            font-weight: 700;
            width: 65%;
            color: #000000;
            text-transform: uppercase;
            font-size: 10px;
            letter-spacing: 0.3px;
        }

        .summary-table .summary-value {
            text-align: right;
            font-weight: 600;
            background: #ffffff;
            color: #1a1a1a;
        }

        .summary-table .total-final {
            font-weight: 700;
            background: #000000 !important;
            color: #ffffff !important;
            border: 2px solid #000000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .summary-table .total-final.summary-label {
            background: #000000 !important;
            color: #ffffff !important;
        }

        /* Bagian catatan dengan frame yang jelas */
        .notes-section {
            margin-top: 25px;
            margin-bottom: 25px;
        }

        .notes-title {
            font-size: 13px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #000000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .notes-content {
            border: 2px solid #000000;
            padding: 16px 18px;
            background: #f8f8f8;
            font-size: 11px;
            line-height: 1.5;
            color: #1a1a1a;
            min-height: 70px;
            font-weight: 500;
            position: relative;
        }

        .notes-content::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 1px solid #666666;
            pointer-events: none;
        }

        /* Bagian tanda tangan dengan desain elegan */
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            gap: 60px;
        }

        .signature-box {
            flex: 1;
            text-align: center;
            border: 1px solid #34495e;
            padding: 25px 20px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            position: relative;
            border-radius: 8px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
        }

        .signature-box::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            pointer-events: none;
        }

        .signature-title {
            font-size: 13px;
            font-weight: 700;
            margin-bottom: 70px;
            color: #2c3e50;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .signature-name {
            font-size: 13px;
            font-weight: 700;
            margin-bottom: 6px;
            color: #2c3e50;
            border-top: 2px solid #34495e;
            padding-top: 10px;
        }

        .signature-position {
            font-size: 11px;
            color: #7f8c8d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Footer dengan desain modern */
        .print-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #34495e;
            text-align: center;
            font-size: 11px;
            color: #7f8c8d;
            font-weight: 600;
            position: relative;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 6px;
        }

        .print-footer::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 20px;
            right: 20px;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 1px;
        }

        /* Kontrol cetak dengan styling modern */
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 15px;
            border: 1px solid #34495e;
            border-radius: 8px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .btn-print, .btn-close {
            display: inline-block;
            padding: 12px 20px;
            margin: 0 8px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: 1px solid #2980b9;
            color: #ffffff;
            text-decoration: none;
            font-size: 12px;
            cursor: pointer;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .btn-print:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-close {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border: 1px solid #c0392b;
        }

        .btn-close:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* Responsif untuk layar */
        @media screen and (max-width: 768px) {
            .info-row {
                flex-direction: column;
            }
            
            .summary-section {
                flex-direction: column;
            }
            
            .summary-right {
                width: 100%;
                margin-top: 20px;
            }
            
            .signature-section {
                flex-direction: column;
                gap: 25px;
            }
        }

        /* Status kosong dengan desain modern */
        .empty-state {
            text-align: center;
            padding: 60px 30px;
            color: #7f8c8d;
            font-style: italic;
            border: 1px solid #bdc3c7;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        /* Kelas utilitas dengan konsistensi desain */
        .font-bold { font-weight: 700; }
        .font-black { font-weight: 900; }
        .text-small { font-size: 10px; }
        .text-tiny { font-size: 9px; }
        .mb-10 { margin-bottom: 10px; }
        .mb-15 { margin-bottom: 15px; }
        .mt-10 { margin-top: 10px; }
        .mt-15 { margin-top: 15px; }
        .border-top { border-top: 2px solid #000000; }
        .border-bottom { border-bottom: 2px solid #000000; }
        .border-thick { border: 2px solid #000000; }
        .bg-light { background: #f8f8f8; }
        .bg-dark { background: #000000; color: #ffffff; }
        .text-muted { color: #666666; }
        .text-black { color: #000000; }
        .uppercase { text-transform: uppercase; }
        .letter-spacing { letter-spacing: 0.5px; }
        
        /* Icon dokumen dengan styling premium */
        .icon-document:before {
            content: "📋";
            margin-right: 8px;
            font-size: 16px;
        }

        /* Watermark atau background pattern (opsional) */
        .document-watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 72px;
            color: rgba(0,0,0,0.03);
            font-weight: 900;
            z-index: -1;
            pointer-events: none;
            text-transform: uppercase;
            letter-spacing: 8px;
        }

        /* Divider lines untuk section */
        .section-divider {
            height: 3px;
            background: #000000;
            margin: 20px 0;
            position: relative;
        }

        .section-divider::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 0;
            right: 0;
            height: 1px;
            background: #666666;
        }
    </style>
</head>
<body>
    <!-- Kontrol Cetak -->
    <div class="print-controls no-print">
        <button class="btn-print" onclick="window.print()">🖨️ CETAK</button>
        <button class="btn-close" onclick="window.close()">❌ TUTUP</button>
    </div>

    <!-- Watermark (opsional) -->
    <?php if (isset($show_watermark) && $show_watermark): ?>
    <div class="document-watermark no-print"><?= isset($watermark_text) ? $watermark_text : 'CONFIDENTIAL' ?></div>
    <?php endif; ?>

    <!-- Header Perusahaan -->
    <div class="company-header">
        <div class="company-name"><?= isset($company_name) ? $company_name : 'NAMA PERUSAHAAN' ?></div>
        <div class="company-info">
            <?= isset($company_address) ? $company_address : 'Alamat Perusahaan' ?><br>
            <strong>Telp:</strong> <?= isset($company_phone) ? $company_phone : '(021) 1234567' ?> | 
            <strong>Email:</strong> <?= isset($company_email) ? $company_email : '<EMAIL>' ?>
            <?php if (isset($company_website)): ?>
                | <strong>Web:</strong> <?= $company_website ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Judul Dokumen -->
    <?php if (isset($document_title)): ?>
    <div class="document-title">
        <span class="icon-document"></span><?= $document_title ?>
        <?php if (isset($document_number)): ?>
            <br><span style="font-size: 14px; font-weight: 600; letter-spacing: 1px;"><?= $document_number ?></span>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Section Divider -->
    <div class="section-divider"></div>

    <!-- Area Konten Utama -->
    <div class="content-area">
        <?= isset($content) ? $content : '' ?>
    </div>

    <!-- Section Divider -->
    <div class="section-divider"></div>

    <!-- Footer -->
    <div class="print-footer">
        <div><strong>DICETAK PADA:</strong> <?= strtoupper(date('d/m/Y H:i:s')) ?></div>
        <?php if (isset($footer_text)): ?>
            <div style="margin-top: 5px;"><?= strtoupper($footer_text) ?></div>
        <?php endif; ?>
        <?php if (isset($document_id)): ?>
            <div style="margin-top: 3px; font-size: 9px;"><strong>DOC ID:</strong> <?= $document_id ?></div>
        <?php endif; ?>
    </div>

    <script>
        // Auto print jika diperlukan
        <?php if (isset($auto_print) && $auto_print): ?>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 800);
        };
        <?php endif; ?>

        // Tutup jendela setelah print (opsional)
        <?php if (isset($close_after_print) && $close_after_print): ?>
        window.onafterprint = function() {
            setTimeout(function() {
                window.close();
            }, 1500);
        };
        <?php endif; ?>

        // Shortcut keyboard
        document.addEventListener('keydown', function(e) {
            // Ctrl+P untuk print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape untuk tutup
            if (e.key === 'Escape') {
                window.close();
            }
            // Ctrl+Shift+P untuk print preview
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                window.print();
            }
        });

        // Prevent right click pada mode production
        <?php if (isset($disable_right_click) && $disable_right_click): ?>
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        <?php endif; ?>

        // Print quality optimization
        if (window.matchMedia) {
            var mediaQueryList = window.matchMedia('print');
            mediaQueryList.addListener(function(mql) {
                if (mql.matches) {
                    // Optimasi untuk print
                    document.body.style.fontSize = '11px';
                } else {
                    // Kembalikan untuk screen
                    document.body.style.fontSize = '12px';
                }
            });
        }
    </script>
</body>
</html>