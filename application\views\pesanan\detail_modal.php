<div class="modal-header">
    <h4 class="modal-title">Detail Pesanan</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
<div class="row">
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Nomor <PERSON></th>
                <td><?= $pesanan->nomor_pesanan ?></td>
            </tr>
            <tr>
                <th>Tanggal</th>
                <td><?= date('d/m/Y', strtotime($pesanan->tanggal_pesanan)) ?></td>
            </tr>
            <tr>
                <th>Jenis</th>
                <td>
                    <?php if ($pesanan->jenis_pesanan == 'android'): ?>
                        <span class="badge badge-info">Android</span>
                    <?php else: ?>
                        <span class="badge badge-secondary">Manual</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <th>Pelanggan</th>
                <td><?= $pesanan->nama_pelanggan ? $pesanan->nama_pelanggan . ' (' . $pesanan->kode_pelanggan . ')' : '-' ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <table class="table table-bordered table-sm">
            <tr>
                <th style="width: 150px">Status</th>
                <td>
                    <?php
                    switch ($pesanan->status) {
                        case 'draft':
                            echo '<span class="badge badge-warning">Draft</span>';
                            break;
                        case 'diproses':
                            echo '<span class="badge badge-primary">Diproses</span>';
                            break;
                        case 'dikirim':
                            echo '<span class="badge badge-info">Dikirim</span>';
                            break;
                        case 'selesai':
                            echo '<span class="badge badge-success">Selesai</span>';
                            break;
                        case 'dibatalkan':
                            echo '<span class="badge badge-danger">Dibatalkan</span>';
                            break;
                        default:
                            echo '<span class="badge badge-secondary">' . ucfirst($pesanan->status) . '</span>';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <th>Total Item</th>
                <td><?= number_format($pesanan->total_item, 0) ?> item</td>
            </tr>
            <tr>
                <th>Total Qty</th>
                <td><?= number_format($pesanan->total_qty, 0) ?></td>
            </tr>
            <tr>
                <th>Subtotal</th>
                <td>Rp <?= number_format($pesanan->subtotal ?? 0, 0, ',', '.') ?></td>
            </tr>
            <tr>
                <th>PPN (<?= number_format($pesanan->ppn_persen ?? 11, 1) ?>%)</th>
                <td>Rp <?= number_format($pesanan->ppn_nominal ?? 0, 0, ',', '.') ?></td>
            </tr>
            <tr>
                <th>Total Akhir</th>
                <td><strong>Rp <?= number_format($pesanan->total_akhir ?? 0, 0, ',', '.') ?></strong></td>
            </tr>
        </table>
    </div>
</div>

<?php if (!empty($pesanan->keterangan)): ?>
<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title">Keterangan</h3>
            </div>
            <div class="card-body">
                <?= nl2br($pesanan->keterangan) ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>



<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title">Item Pesanan</h3>
                <?php if ($pesanan->status == 'draft'): ?>
                <div class="card-tools">
                    <button type="button" class="btn btn-sm btn-primary" onclick="addDetailItem()">
                        <i class="fas fa-plus"></i> Tambah Item
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-sm">
                        <thead>
                            <tr class="bg-info">
                                <th>No</th>
                                <th>Kode Barang</th>
                                <th>Nama Barang</th>
                                <th>Qty</th>
                                <th>Satuan</th>
                                <th>Harga Satuan</th>
                                <th>Subtotal</th>
                                <th>PPN</th>
                                <th>Total</th>
                                <?php if ($pesanan->status == 'draft'): ?>
                                <th>Aksi</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($pesanan_detail)): ?>
                            <tr>
                                <td colspan="<?= ($pesanan->status == 'draft') ? '10' : '9' ?>" class="text-center">Belum ada item</td>
                            </tr>
                            <?php else: ?>
                            <?php $no = 1; $total_nilai = 0; foreach ($pesanan_detail as $d): ?>
                            <tr>
                                <td><?= $no++ ?></td>
                                <td><?= $d->kode_barang ?></td>
                                <td>
                                    <?= $d->nama_barang ?>
                                    <?php if ($d->merk || $d->tipe): ?>
                                        <br><small class="text-muted"><?= trim($d->merk . ' ' . $d->tipe) ?></small>
                                    <?php endif; ?>
                                    <?php if ($d->keterangan): ?>
                                        <br><small class="text-info">Ket: <?= $d->keterangan ?></small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-right"><?= number_format($d->qty, 0) ?></td>
                                <td><?= $d->nama_satuan ?: '-' ?></td>
                                <td class="text-right">Rp <?= number_format($d->harga_satuan, 0, ',', '.') ?></td>
                                <td class="text-right">Rp <?= number_format($d->subtotal, 0, ',', '.') ?></td>
                                <td class="text-right">
                                    <?php if (isset($d->ppn_nominal) && $d->ppn_nominal > 0): ?>
                                        Rp <?= number_format($d->ppn_nominal, 0, ',', '.') ?>
                                        <br><small class="text-muted">(<?= number_format($d->ppn_persen ?? 11, 1) ?>%)</small>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td class="text-right">
                                    <strong>Rp <?= number_format($d->total_setelah_ppn ?? $d->subtotal, 0, ',', '.') ?></strong>
                                </td>
                                <?php if ($pesanan->status == 'draft'): ?>
                                <td>
                                    <button type="button" class="btn btn-xs btn-info" onclick="editDetailItem(<?= $d->id ?>)"><i class="fas fa-edit"></i></button>
                                    <button type="button" class="btn btn-xs btn-danger" onclick="deleteDetailItem(<?= $d->id ?>)"><i class="fas fa-trash"></i></button>
                                </td>
                                <?php endif; ?>
                            </tr>
                            <?php $total_nilai += ($d->total_setelah_ppn ?? $d->subtotal); ?>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                        <tfoot>
                            <tr class="bg-light">
                                <th colspan="3" class="text-right">Total:</th>
                                <th class="text-right"><?= number_format($pesanan->total_qty, 0) ?></th>
                                <th colspan="2"></th>
                                <th class="text-right">Rp <?= number_format($pesanan->subtotal ?? 0, 0, ',', '.') ?></th>
                                <th class="text-right">Rp <?= number_format($pesanan->ppn_nominal ?? 0, 0, ',', '.') ?></th>
                                <th class="text-right"><strong>Rp <?= number_format($pesanan->total_akhir ?? 0, 0, ',', '.') ?></strong></th>
                                <th colspan="<?= ($pesanan->status == 'draft') ? '1' : '0' ?>"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-light">
                <h3 class="card-title">Informasi Tambahan</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered table-sm">
                            <tr>
                                <th style="width: 150px">Dibuat oleh</th>
                                <td><?= $pesanan->created_by ?: '-' ?></td>
                            </tr>
                            <tr>
                                <th>Tanggal dibuat</th>
                                <td><?= $pesanan->created_at ? date('d/m/Y H:i:s', strtotime($pesanan->created_at)) : '-' ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered table-sm">
                            <tr>
                                <th style="width: 150px">Diupdate oleh</th>
                                <td><?= $pesanan->updated_by ?: '-' ?></td>
                            </tr>
                            <tr>
                                <th>Tanggal update</th>
                                <td><?= $pesanan->updated_at ? date('d/m/Y H:i:s', strtotime($pesanan->updated_at)) : '-' ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Modal Form Detail Item -->
<div class="modal fade" id="modal_form_detail_item" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Item Pesanan</h4>
                <button type="button" class="close" onclick="closeItemModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="#" id="form_detail_item">
                    <input type="hidden" name="id" id="detail_item_id">
                    <input type="hidden" name="id_pesanan" value="<?= $pesanan->id ?>">
                    
                    <div class="form-group" id="barang_group">
                        <label>Barang</label>
                        <select name="id_barang" id="id_barang" class="form-control select2" style="width: 100%;">
                            <option value="">-- Pilih Barang --</option>
                            <?php foreach ($barang_list as $barang): ?>
                            <option value="<?= $barang->id ?>" 
                                    data-harga="<?= $barang->harga_jual ?>"
                                    data-ppn="<?= $barang->pajak_persen ?? 11 ?>">
                                <?= $barang->kode_barang ?> - <?= $barang->nama_barang ?>
                                <?php if ($barang->pajak_persen): ?>
                                    <small>(PPN <?= number_format($barang->pajak_persen, 1) ?>%)</small>
                                <?php endif; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Qty</label>
                        <input type="number" class="form-control" id="qty" name="qty" min="1" step="1" onchange="updateTotalHarga()">
                    </div>
                    
                    <div class="form-group">
                        <label>Harga Satuan</label>
                        <input type="number" class="form-control" id="harga_satuan" name="harga_satuan" min="0" step="1" onchange="updateTotalHarga()">
                    </div>
                    
                    <div class="form-group">
                        <label>Subtotal</label>
                        <div class="form-control-plaintext">
                            <strong id="subtotal_display">0</strong>
                            <small class="text-muted ml-2">(Qty × Harga Satuan)</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>PPN</label>
                        <div class="form-control-plaintext">
                            <strong id="ppn_display">0</strong>
                            <small class="text-muted ml-2">(<span id="ppn_persen_display">11.0</span>%)</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Total Setelah PPN</label>
                        <div class="form-control-plaintext">
                            <strong id="total_display">0</strong>
                            <small class="text-muted ml-2">(Subtotal + PPN)</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Keterangan</label>
                        <textarea class="form-control" id="keterangan_detail_item" name="keterangan" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeItemModal()">Batal</button>
                <button type="button" class="btn btn-primary" id="btnSaveDetailItem" onclick="saveDetailItem()">Simpan</button>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <?php if ($pesanan->status == 'draft'): ?>
    <button type="button" class="btn btn-success" onclick="updateStatus('diproses')"><i class="fas fa-check"></i> Proses Pesanan</button>
    <?php //elseif ($pesanan->status == 'diproses'): ?>
    <!-- <button type="button" class="btn btn-info" onclick="updateStatus('dikirim')"><i class="fas fa-truck"></i> Kirim Pesanan</button> -->
    <?php //elseif ($pesanan->status == 'dikirim'): ?>
    <!-- <button type="button" class="btn btn-success" onclick="updateStatus('selesai')"><i class="fas fa-check-circle"></i> Selesaikan</button> -->
    <?php endif; ?>
    <button type="button" class="btn btn-info" onclick="printPesanan()">Print</button>
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
</div>

<script>
var save_method_detail_item;
var current_pesanan_id = <?= $pesanan->id ?>;

$(document).ready(function() {
    // Fix for nested modals - improve z-index handling
    $(document).on('show.bs.modal', '.modal', function() {
        var zIndex = 1040 + (10 * $('.modal:visible').length);
        $(this).css('z-index', zIndex);
        setTimeout(function() {
            $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
        }, 0);
    });
    
    // Prevent modal backdrop issues when item modal is closed
    $('#modal_form_detail_item').on('hidden.bs.modal', function(e) {
        // Prevent event propagation to parent modal
        e.stopPropagation();
        
        // Keep parent modal visible and scrollable
        $('body').addClass('modal-open');
        
        // Fix for backdrop
        if ($('.modal:visible').length > 0) {
            // Ensure parent modal is still visible
            $('#modal_detail').css('display', 'block');
            
            // Fix backdrop if needed
            if ($('.modal-backdrop').length === 0) {
                $('body').append('<div class="modal-backdrop fade show"></div>');
                $('.modal-backdrop').css('z-index', parseInt($('#modal_detail').css('z-index')) - 1);
            }
        }
    });
    
    // Initialize Select2 untuk modal detail item
    $('.select2').select2({
        dropdownParent: $("#modal_form_detail_item")
    });

    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var hargaJual = selectedOption.data('harga');
        var ppnPersen = selectedOption.data('ppn') || 11;

        if (hargaJual) {
            $('#harga_satuan').val(hargaJual);
            $('#ppn_persen_display').text(ppnPersen.toFixed(1));
            updateTotalHarga();
        }
    });
});

function addDetailItem() {
    save_method_detail_item = 'add';
    $('#form_detail_item')[0].reset();
    $('#detail_item_id').val('');
    $('#barang_group').show();
    $('#subtotal_display').text('0');
    $('#ppn_display').text('0');
    $('#total_display').text('0');
    $('#ppn_persen_display').text('11.0');
    $('.modal-title').text('Form Item Pesanan');
    $('#modal_form_detail_item').modal('show');
}

function editDetailItem(id) {
    save_method_detail_item = 'update';
    $('#barang_group').show();
    $('.modal-title').text('Form Item Pesanan');

    $.ajax({
    url: "<?php echo site_url('pesanan/get_detail_item') ?>",
    type: "POST",
    data: { id: id },
    dataType: "JSON",
    success: function(data) {
    if (data.status == 'success') {
    $('#detail_item_id').val(data.data.id);
    $('#id_barang').val(data.data.id_barang).trigger('change');
    $('#qty').val(data.data.qty);
    $('#harga_satuan').val(data.data.harga_satuan);
    $('#keterangan_detail_item').val(data.data.keterangan);
    updateTotalHarga();
    $('#modal_form_detail_item').modal('show');
    } else {
    Swal.fire({
    title: 'Error!',
    text: 'Terjadi kesalahan saat memuat data detail.',
    icon: 'error',
    confirmButtonText: 'OK'
    });
    }
    },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat memuat data detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function saveDetailItem() {
    $('#btnSaveDetailItem').text('saving...').prop('disabled', true);

    var url;
    if (save_method_detail_item == 'add') {
        url = "<?php echo site_url('pesanan/add_detail') ?>";
    } else {
        url = "<?php echo site_url('pesanan/update_detail') ?>";
    }

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form_detail_item').serialize(),
        dataType: "JSON",
        success: function(data) {
            if (data.status == 'success') {
                $('#modal_form_detail_item').modal('hide');
                Swal.fire({
                    title: 'Detail item berhasil disimpan!',
                    text: data.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                }).then(() => {
                    // Reload modal detail
                    detail(current_pesanan_id);
                    // Reload main table
                    if (typeof table !== 'undefined') {
                        table.ajax.reload();
                    }
                });
            } else {
                Swal.fire({
                    title: 'Gagal Menyimpan!',
                    text: data.message,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menyimpan detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        },
        complete: function() {
            $('#btnSaveDetailItem').text('Save').prop('disabled', false);
        }
    });
}

function deleteDetailItem(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Detail item akan dihapus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?php echo site_url('pesanan/delete_detail') ?>",
                type: "POST",
                dataType: "JSON",
                data: {
                    id: id,
                    id_pesanan: current_pesanan_id
                },
                success: function(data) {
                    if (data.status == 'success') {
                        Swal.fire({
                            title: 'Terhapus!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_pesanan_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menghapus detail.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function updateStatus(status) {
    var statusText = '';
    switch(status) {
        case 'diproses': statusText = 'diproses'; break;
        case 'dikirim': statusText = 'dikirim'; break;
        case 'selesai': statusText = 'diselesaikan'; break;
        case 'dibatalkan': statusText = 'dibatalkan'; break;
    }

    Swal.fire({
        title: 'Konfirmasi',
        text: 'Apakah Anda yakin ingin mengubah status pesanan menjadi ' + statusText + '?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, ubah!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: '<?= site_url('pesanan/update_status') ?>',
                type: 'POST',
                data: {
                    id: current_pesanan_id,
                    status: status
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Status pesanan berhasil diubah!',
                            text: response.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload modal detail
                            detail(current_pesanan_id);
                            // Reload main table
                            if (typeof table !== 'undefined') {
                                table.ajax.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan sistem!',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function editPesanan(id) {
    // Close detail modal first
    $('#modal_detail').modal('hide');

    // Wait for modal to close then open edit modal
    setTimeout(function() {
        edit(id);
    }, 500);
}

function printPesanan() {
    window.open('<?= site_url('pesanan/print_pesanan') ?>/' + current_pesanan_id, '_blank');
}

function updateTotalHarga() {
    var qty = parseFloat($('#qty').val()) || 0;
    var harga = parseFloat($('#harga_satuan').val()) || 0;
    var ppnPersen = parseFloat($('#ppn_persen_display').text()) || 11;
    
    var subtotal = qty * harga;
    var ppnNominal = Math.round(subtotal * ppnPersen / 100);
    var total = subtotal + ppnNominal;

    $('#subtotal_display').text(subtotal.toLocaleString('id-ID'));
    $('#ppn_display').text(ppnNominal.toLocaleString('id-ID'));
    $('#total_display').text(total.toLocaleString('id-ID'));
}

function closeItemModal() {
    $('#modal_form_detail_item').modal('hide');
}
</script>