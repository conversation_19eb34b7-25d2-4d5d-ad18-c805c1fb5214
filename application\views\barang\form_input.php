<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="barangTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="detail-tab" data-toggle="tab" href="#detail" role="tab">Detail Produk</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="pricing-tab" data-toggle="tab" href="#pricing" role="tab">Harga & Stok</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="barangTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="kode_barang" class="col-sm-3 col-form-label">Kode Barang</label>
                        <div class="col-sm-7 kosong">
                            <input type="text" class="form-control" name="kode_barang" id="kode_barang" placeholder="Kode Barang (Kosongkan untuk auto generate)">
                            <span class="help-block"></span>
                        </div>
                        <div class="col-sm-2">
                            <button type="button" id="btn-generate-kode" class="btn btn-outline-secondary" onclick="generateKode()" title="Generate Kode Otomatis">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="nama_barang" class="col-sm-3 col-form-label">Nama Barang <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="nama_barang" id="nama_barang" placeholder="Nama Barang" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="merk" class="col-sm-3 col-form-label">Merk</label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="merk" id="merk" placeholder="Merk/Brand">
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tipe" class="col-sm-3 col-form-label">Tipe</label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="tipe" id="tipe" placeholder="Tipe/Model">
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="satuan_id" class="col-sm-3 col-form-label">Satuan <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control" name="satuan_id" id="satuan_id" required>
                                <option value="">Pilih Satuan</option>
                                <?php if (isset($satuan_list) && !empty($satuan_list)): ?>
                                    <?php foreach ($satuan_list as $satuan): ?>
                                        <option value="<?= $satuan->id ?>"><?= $satuan->kode_satuan ?> - <?= $satuan->nama_satuan ?></option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="jenis_pajak_id" class="col-sm-3 col-form-label">Jenis Pajak <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control" name="jenis_pajak_id" id="jenis_pajak_id" required>
                                <option value="">Pilih Jenis Pajak</option>
                                <?php if (isset($jenis_pajak_list) && !empty($jenis_pajak_list)): ?>
                                    <?php foreach ($jenis_pajak_list as $pajak): ?>
                                        <option value="<?= $pajak->id ?>"><?= $pajak->nama_pajak ?> (<?= $pajak->tarif_persen ?>%)</option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Detail Produk -->
            <div class="tab-pane fade" id="detail" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="spesifikasi" class="col-sm-3 col-form-label">Spesifikasi</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="spesifikasi" id="spesifikasi" placeholder="Spesifikasi detail produk" rows="4"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>



                </div>
            </div>

            <!-- Tab Harga & Stok -->
            <div class="tab-pane fade" id="pricing" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="harga_beli" class="col-sm-3 col-form-label">Harga Beli <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="harga_beli" id="harga_beli" placeholder="Harga Beli" min="0" step="1" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="harga_jual" class="col-sm-3 col-form-label">Harga Jual <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="harga_jual" id="harga_jual" placeholder="Harga Jual" min="0" step="1" required>
                            <small class="form-text text-muted">Harga jual sebelum PPN (Harga DPP)</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="berat" class="col-sm-3 col-form-label">Berat (kg) <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="berat" id="berat" placeholder="Berat dalam kilogram" min="0" step="0.01" value="0.50" required>
                            <small class="form-text text-muted">Berat barang dalam kilogram (kg) - digunakan untuk perhitungan biaya pengiriman</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="stok_minimum" class="col-sm-3 col-form-label">Stok Minimum</label>
                        <div class="col-sm-9 kosong">
                            <input type="number" class="form-control" name="stok_minimum" id="stok_minimum" placeholder="Stok Minimum" min="0" value="0">
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <hr>
                    <h6 class="text-muted">Status</h6>

                    <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Status Aktif</label>
                        <div class="col-sm-9">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="aktif" id="aktif" value="1" checked>
                                <label class="form-check-label" for="aktif">
                                    Barang Aktif
                                </label>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>
