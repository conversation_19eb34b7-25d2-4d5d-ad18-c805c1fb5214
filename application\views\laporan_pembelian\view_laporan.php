<?php
// Definisi judul laporan
$judul_laporan = array(
    'pembelian_periode' => 'Laporan Pembelian Periode',
    'detail_pembelian' => 'Laporan Detail Pembelian',
    'pembelian_supplier' => 'Laporan Pembelian per Supplier',
    'pembelian_barang' => 'Laporan Pembelian per Barang',
    'outstanding_po' => 'Laporan Outstanding Purchase Order'
);

$periode_text = '';
if (!empty($filter['tanggal_dari']) && !empty($filter['tanggal_sampai'])) {
    $periode_text = ' Periode: ' . date('d/m/Y', strtotime($filter['tanggal_dari'])) . ' - ' . date('d/m/Y', strtotime($filter['tanggal_sampai']));
} elseif (!empty($filter['tanggal_dari'])) {
    $periode_text = ' Dari: ' . date('d/m/Y', strtotime($filter['tanggal_dari']));
} elseif (!empty($filter['tanggal_sampai'])) {
    $periode_text = ' Sampai: ' . date('d/m/Y', strtotime($filter['tanggal_sampai']));
}
?>

<div class="report-header mb-3">
    <h4 class="text-center font-weight-bold"><?= $judul_laporan[$jenis_laporan] ?? 'Laporan Pembelian' ?><?= $periode_text ?></h4>
    
    <?php if (!empty($info_supplier) || !empty($info_barang) || !empty($filter['status'])): ?>
    <div class="filter-info mt-2">
        <small class="text-muted">
            <?php if (!empty($info_supplier)): ?>
                <strong>Supplier:</strong> <?= $info_supplier ?><br>
            <?php endif; ?>
            <?php if (!empty($info_barang)): ?>
                <strong>Barang:</strong> <?= $info_barang ?><br>
            <?php endif; ?>
            <?php if (!empty($filter['status'])): ?>
                <strong>Status:</strong> <?= ucfirst($filter['status']) ?><br>
            <?php endif; ?>
        </small>
    </div>
    <?php endif; ?>
</div>

<?php if ($laporan && $laporan->num_rows() > 0): ?>
    
    <?php if ($jenis_laporan == 'pembelian_periode'): ?>
        <!-- Laporan Pembelian Periode -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover table-sm">
                <thead class="bg-primary text-white">
                    <tr>
                        <th class="text-center" width="5%">No</th>
                        <th>Nomor Pembelian</th>
                        <th width="10%">Tanggal</th>
                        <th>Supplier</th>
                        <th width="10%">Jenis</th>
                        <th width="10%">Status</th>
                        <th width="12%">Status Bayar</th>
                        <th width="8%" class="text-right">Item</th>
                        <th width="8%" class="text-right">Qty</th>
                        <th width="12%" class="text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $no = 1;
                    $total_item = 0;
                    $total_qty = 0;
                    $total_nilai = 0;
                    foreach ($laporan->result() as $row):
                        $total_item += $row->total_item;
                        $total_qty += $row->total_qty;
                        $total_nilai += $row->total_akhir;
                        
                        // Status badge
                        $status_class = '';
                        switch ($row->status) {
                            case 'draft': $status_class = 'badge-secondary'; break;
                            case 'disetujui': $status_class = 'badge-primary'; break;
                            case 'dipesan': $status_class = 'badge-warning'; break;
                            case 'diterima': $status_class = 'badge-info'; break;
                            case 'selesai': $status_class = 'badge-success'; break;
                            case 'dibatalkan': $status_class = 'badge-danger'; break;
                            default: $status_class = 'badge-secondary';
                        }
                        
                        $bayar_class = '';
                        switch ($row->status_pembayaran) {
                            case 'belum_bayar': $bayar_class = 'badge-danger'; break;
                            case 'sebagian': $bayar_class = 'badge-warning'; break;
                            case 'lunas': $bayar_class = 'badge-success'; break;
                            default: $bayar_class = 'badge-secondary';
                        }
                    ?>
                    <tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $row->nomor_pembelian ?></td>
                        <td><?= date('d/m/Y', strtotime($row->tanggal_pembelian)) ?></td>
                        <td><?= $row->nama_supplier ?></td>
                        <td><span class="badge badge-info"><?= ucfirst($row->jenis_pembelian) ?></span></td>
                        <td><span class="badge <?= $status_class ?>"><?= ucfirst($row->status) ?></span></td>
                        <td><span class="badge <?= $bayar_class ?>"><?= ucfirst(str_replace('_', ' ', $row->status_pembayaran)) ?></span></td>
                        <td class="text-right"><?= number_format($row->total_item, 0) ?></td>
                        <td class="text-right"><?= number_format($row->total_qty, 0) ?></td>
                        <td class="text-right">Rp <?= number_format($row->total_akhir, 0) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
                    <tr>
                        <td colspan="7" class="text-right"><strong>TOTAL:</strong></td>
                        <td class="text-right"><strong><?= number_format($total_item, 0) ?></strong></td>
                        <td class="text-right"><strong><?= number_format($total_qty, 0) ?></strong></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_nilai, 0) ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

    <?php elseif ($jenis_laporan == 'detail_pembelian'): ?>
        <!-- Laporan Detail Pembelian -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover table-sm">
                <thead class="bg-primary text-white">
                    <tr>
                        <th class="text-center" width="4%">No</th>
                        <th>No. Pembelian</th>
                        <th width="8%">Tanggal</th>
                        <th>Supplier</th>
                        <th>Kode Barang</th>
                        <th>Nama Barang</th>
                        <th>Merk</th>
                        <th width="6%">Satuan</th>
                        <th width="8%" class="text-right">Qty</th>
                        <th width="10%" class="text-right">Harga</th>
                        <th width="10%" class="text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $no = 1;
                    $total_qty = 0;
                    $total_nilai = 0;
                    foreach ($laporan->result() as $row):
                        $total_qty += $row->qty;
                        $total_nilai += $row->total_akhir;
                    ?>
                    <tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $row->nomor_pembelian ?></td>
                        <td><?= date('d/m/Y', strtotime($row->tanggal_pembelian)) ?></td>
                        <td><?= $row->nama_supplier ?></td>
                        <td><?= $row->kode_barang ?></td>
                        <td><?= $row->nama_barang ?></td>
                        <td><?= $row->merk ?></td>
                        <td><?= $row->nama_satuan ?></td>
                        <td class="text-right"><?= number_format($row->qty, 2) ?></td>
                        <td class="text-right">Rp <?= number_format($row->harga_satuan, 0) ?></td>
                        <td class="text-right">Rp <?= number_format($row->total_akhir, 0) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
                    <tr>
                        <td colspan="8" class="text-right"><strong>TOTAL:</strong></td>
                        <td class="text-right"><strong><?= number_format($total_qty, 2) ?></strong></td>
                        <td class="text-right"></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_nilai, 0) ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

    <?php elseif ($jenis_laporan == 'pembelian_supplier'): ?>
        <!-- Laporan Pembelian per Supplier -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover table-sm">
                <thead class="bg-primary text-white">
                    <tr>
                        <th class="text-center" width="5%">No</th>
                        <th>Kode Supplier</th>
                        <th>Nama Supplier</th>
                        <th width="8%" class="text-right">Transaksi</th>
                        <th width="8%" class="text-right">Item</th>
                        <th width="10%" class="text-right">Qty</th>
                        <th width="12%" class="text-right">Total Beli</th>
                        <th width="12%" class="text-right">Dibayar</th>
                        <th width="12%" class="text-right">Outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $no = 1;
                    $total_transaksi = 0;
                    $total_item = 0;
                    $total_qty = 0;
                    $total_pembelian = 0;
                    $total_dibayar = 0;
                    $total_outstanding = 0;
                    foreach ($laporan->result() as $row):
                        $total_transaksi += $row->total_transaksi;
                        $total_item += $row->total_item;
                        $total_qty += $row->total_qty;
                        $total_pembelian += $row->total_pembelian;
                        $total_dibayar += $row->total_dibayar;
                        $total_outstanding += $row->sisa_pembayaran;
                    ?>
                    <tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $row->kode_supplier ?></td>
                        <td><?= $row->nama_supplier ?></td>
                        <td class="text-right"><?= number_format($row->total_transaksi, 0) ?></td>
                        <td class="text-right"><?= number_format($row->total_item, 0) ?></td>
                        <td class="text-right"><?= number_format($row->total_qty, 0) ?></td>
                        <td class="text-right">Rp <?= number_format($row->total_pembelian, 0) ?></td>
                        <td class="text-right">Rp <?= number_format($row->total_dibayar, 0) ?></td>
                        <td class="text-right">Rp <?= number_format($row->sisa_pembayaran, 0) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
                    <tr>
                        <td colspan="3" class="text-right"><strong>TOTAL:</strong></td>
                        <td class="text-right"><strong><?= number_format($total_transaksi, 0) ?></strong></td>
                        <td class="text-right"><strong><?= number_format($total_item, 0) ?></strong></td>
                        <td class="text-right"><strong><?= number_format($total_qty, 0) ?></strong></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_pembelian, 0) ?></strong></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_dibayar, 0) ?></strong></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_outstanding, 0) ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

    <?php elseif ($jenis_laporan == 'pembelian_barang'): ?>
        <!-- Laporan Pembelian per Barang -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover table-sm">
                <thead class="bg-primary text-white">
                    <tr>
                        <th class="text-center" width="5%">No</th>
                        <th>Kode Barang</th>
                        <th>Nama Barang</th>
                        <th>Merk</th>
                        <th width="8%">Satuan</th>
                        <th width="10%" class="text-right">Total Qty</th>
                        <th width="12%" class="text-right">Harga Rata²</th>
                        <th width="12%" class="text-right">Total Beli</th>
                        <th width="8%" class="text-right">Supplier</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $no = 1;
                    $total_qty = 0;
                    $total_pembelian = 0;
                    foreach ($laporan->result() as $row):
                        $total_qty += $row->total_qty;
                        $total_pembelian += $row->total_pembelian;
                    ?>
                    <tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $row->kode_barang ?></td>
                        <td><?= $row->nama_barang ?></td>
                        <td><?= $row->merk ?></td>
                        <td><?= $row->nama_satuan ?></td>
                        <td class="text-right"><?= number_format($row->total_qty, 2) ?></td>
                        <td class="text-right">Rp <?= number_format($row->harga_rata_rata, 0) ?></td>
                        <td class="text-right">Rp <?= number_format($row->total_pembelian, 0) ?></td>
                        <td class="text-right"><?= number_format($row->jumlah_supplier, 0) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
                    <tr>
                        <td colspan="5" class="text-right"><strong>TOTAL:</strong></td>
                        <td class="text-right"><strong><?= number_format($total_qty, 2) ?></strong></td>
                        <td class="text-right"></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_pembelian, 0) ?></strong></td>
                        <td class="text-right"></td>
                    </tr>
                </tfoot>
            </table>
        </div>

    <?php elseif ($jenis_laporan == 'outstanding_po'): ?>
        <!-- Laporan Outstanding PO -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover table-sm">
                <thead class="bg-primary text-white">
                    <tr>
                        <th class="text-center" width="5%">No</th>
                        <th>Nomor PO</th>
                        <th width="10%">Tanggal</th>
                        <th>Supplier</th>
                        <th width="10%">Status</th>
                        <th width="12%" class="text-right">Total PO</th>
                        <th width="10%">Jatuh Tempo</th>
                        <th width="8%" class="text-right">Umur</th>
                        <th width="12%" class="text-right">Outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $no = 1;
                    $total_po = 0;
                    $total_outstanding = 0;
                    foreach ($laporan->result() as $row):
                        $total_po += $row->total_akhir;
                        $total_outstanding += $row->sisa_pembayaran;
                        $umur_po = floor((strtotime(date('Y-m-d')) - strtotime($row->tanggal_pembelian)) / (60 * 60 * 24));
                        
                        // Status badge
                        $status_class = '';
                        switch ($row->status) {
                            case 'disetujui': $status_class = 'badge-primary'; break;
                            case 'dipesan': $status_class = 'badge-warning'; break;
                            case 'diterima': $status_class = 'badge-info'; break;
                            default: $status_class = 'badge-secondary';
                        }
                        
                        // Umur PO class
                        $umur_class = '';
                        if ($umur_po > 30) {
                            $umur_class = 'text-danger font-weight-bold';
                        } elseif ($umur_po > 14) {
                            $umur_class = 'text-warning font-weight-bold';
                        }
                    ?>
                    <tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $row->nomor_pembelian ?></td>
                        <td><?= date('d/m/Y', strtotime($row->tanggal_pembelian)) ?></td>
                        <td><?= $row->nama_supplier ?></td>
                        <td><span class="badge <?= $status_class ?>"><?= ucfirst($row->status) ?></span></td>
                        <td class="text-right">Rp <?= number_format($row->total_akhir, 0) ?></td>
                        <td><?= !empty($row->tanggal_jatuh_tempo) ? date('d/m/Y', strtotime($row->tanggal_jatuh_tempo)) : '-' ?></td>
                        <td class="text-right <?= $umur_class ?>"><?= $umur_po ?> hari</td>
                        <td class="text-right">Rp <?= number_format($row->sisa_pembayaran, 0) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
                    <tr>
                        <td colspan="5" class="text-right"><strong>TOTAL:</strong></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_po, 0) ?></strong></td>
                        <td colspan="2"></td>
                        <td class="text-right"><strong>Rp <?= number_format($total_outstanding, 0) ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

    <?php endif; ?>

    <!-- Summary Info -->
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> Ringkasan Laporan</h6>
                <p class="mb-0">
                    <strong>Total Data:</strong> <?= number_format($laporan->num_rows(), 0) ?> record<br>
                    <strong>Tanggal Generate:</strong> <?= date('d/m/Y H:i:s') ?><br>
                    <strong>User:</strong> <?= $this->session->userdata('nama_user') ?>
                </p>
            </div>
        </div>
    </div>

<?php else: ?>
    <div class="alert alert-warning text-center">
        <h5><i class="fas fa-exclamation-triangle"></i> Data Tidak Ditemukan</h5>
        <p class="mb-0">Tidak ada data yang sesuai dengan filter yang dipilih. Silakan ubah filter dan coba lagi.</p>
    </div>
<?php endif; ?>