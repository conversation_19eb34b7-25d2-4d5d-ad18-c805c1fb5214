<!-- Header Info -->
<div class="row mb-3">
    <div class="col-md-6">
        <table class="table table-sm table-borderless">
            <tr>
                <td width="140"><strong>Nomor <PERSON></strong></td>
                <td width="10">:</td>
                <td><?= $barang_keluar->nomor_pengeluaran ?></td>
            </tr>
            <tr>
                <td><strong>Tanggal</strong></td>
                <td>:</td>
                <td><?= date('d/m/Y', strtotime($barang_keluar->tanggal)) ?></td>
            </tr>
            <tr>
                <td><strong>Jenis</strong></td>
                <td>:</td>
                <td>
                    <?php
                    $jenis_class = 'badge-primary';
                    switch($barang_keluar->jenis) {
                        case 'penjualan': $jenis_class = 'badge-primary'; break;
                        case 'retur_pembelian': $jenis_class = 'badge-info'; break;
                        case 'transfer_keluar': $jenis_class = 'badge-warning'; break;
                        case 'rusak': $jenis_class = 'badge-danger'; break;
                        case 'hilang': $jenis_class = 'badge-dark'; break;
                        default: $jenis_class = 'badge-secondary'; break;
                    }
                    ?>
                    <span class="badge <?= $jenis_class ?>"><?= strtoupper(str_replace('_', ' ', $barang_keluar->jenis)) ?></span>
                </td>
            </tr>
            <tr>
                <td><strong>Pelanggan</strong></td>
                <td>:</td>
                <td><?= $barang_keluar->nama_pelanggan ? $barang_keluar->nama_pelanggan . ' (' . $barang_keluar->kode_pelanggan . ')' : '-' ?></td>
            </tr>
        </table>
    </div>
    <div class="col-md-6">
        <table class="table table-sm table-borderless">
            <tr>
                <td width="120"><strong>Status</strong></td>
                <td width="10">:</td>
                <td>
                    <?php if ($barang_keluar->status == 'final'): ?>
                        <span class="badge badge-success">FINAL</span>
                    <?php else: ?>
                        <span class="badge badge-warning">DRAFT</span>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>Total Item</strong></td>
                <td>:</td>
                <td><?= number_format($barang_keluar->total_item, 0) ?> item</td>
            </tr>
            <tr>
                <td><strong>Total Qty</strong></td>
                <td>:</td>
                <td><?= number_format($barang_keluar->total_qty, 0) ?></td>
            </tr>
            <tr>
                <td><strong>Ref. Nomor</strong></td>
                <td>:</td>
                <td><?= $barang_keluar->ref_nomor ?: '-' ?></td>
            </tr>
        </table>
    </div>
</div>

<?php if (!empty($barang_keluar->keterangan)): ?>
<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-info">
            <strong>Keterangan:</strong> <?= $barang_keluar->keterangan ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<?php if ($barang_keluar->status == 'draft'): ?>
<div class="row mb-3">
    <div class="col-12">
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addDetailItem()">
            <i class="fas fa-plus"></i> Tambah Item
        </button>
        <button type="button" class="btn btn-sm btn-outline-success" onclick="finalizeFromDetail()">
            <i class="fas fa-check"></i> Finalisasi Barang Keluar
        </button>
    </div>
</div>
<?php endif; ?>

<!-- Detail Table -->
<div class="table-responsive">
    <table id="tbl_detail_barang_keluar" class="table table-bordered table-striped table-sm">
        <thead class="bg-light">
            <tr>
                <th width="5%">No</th>
                <th width="15%">Kode Barang</th>
                <th width="25%">Nama Barang</th>
                <th width="15%">Gudang</th>
                <th width="10%">Qty Keluar</th>
                <th width="8%">Stok</th>
                <th width="10%">Satuan</th>

                <?php if ($barang_keluar->status == 'draft'): ?>
                <th width="10%">Aksi</th>
                <?php endif; ?>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($detail_list)): ?>
                <?php $no = 1; $total_nilai = 0; foreach ($detail_list as $detail): ?>
                <tr>
                    <td><?= $no++ ?></td>
                    <td><?= $detail->kode_barang ?></td>
                    <td>
                        <?= $detail->nama_barang ?>
                        <?php if ($detail->merk || $detail->tipe): ?>
                            <br><small class="text-muted"><?= trim($detail->merk . ' ' . $detail->tipe) ?></small>
                        <?php endif; ?>
                    </td>
                    <td><?= $detail->nama_gudang ?><br><small class="text-muted"><?= $detail->kode_gudang ?></small></td>
                    <td class="text-right"><?= number_format($detail->qty_keluar, 0) ?></td>
                    <td class="text-right">
                        <span class="<?= $detail->stok_tersedia < $detail->qty_keluar ? 'text-danger' : 'text-success' ?>">
                            <?= number_format($detail->stok_tersedia, 0) ?>
                        </span>
                    </td>
                    <td><?= $detail->nama_satuan ?: '-' ?></td>

                    <?php if ($barang_keluar->status == 'draft'): ?>
                    <td>
                        <button type="button" class="btn btn-xs btn-outline-info" onclick="editDetailItem(<?= $detail->id ?>)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(<?= $detail->id ?>)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                    <?php endif; ?>
                </tr>

                <?php endforeach; ?>
                

            <?php else: ?>
                <tr>
                    <td colspan="<?= $barang_keluar->status == 'draft' ? '8' : '7' ?>" class="text-center">
                        Belum ada detail item. 
                        <?php if ($barang_keluar->status == 'draft'): ?>
                            <a href="javascript:void(0)" onclick="addDetailItem()">Klik di sini untuk menambah item</a>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Modal Form Detail -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Item Barang Keluar</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form_detail">
                    <input type="hidden" name="id" id="detail_id">
                    <input type="hidden" name="id_barang_keluar" value="<?= $barang_keluar->id ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group" id="barang_group">
                                <label for="id_barang">Barang <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="id_barang" id="id_barang" required style="width: 100%;">
                                    <option value="">-- Pilih Barang --</option>
                                    <?php foreach ($barang_list as $barang): ?>
                                        <option value="<?= $barang->id ?>" data-satuan="<?= $barang->satuan_id ?>"><?= $barang->kode_barang ?> - <?= $barang->nama_barang ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label for="id_gudang">Gudang <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="id_gudang" id="id_gudang" required style="width: 100%;">
                                    <option value="">-- Pilih Gudang --</option>
                                    <?php foreach ($gudang_list as $gudang): ?>
                                        <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="help-block"></span>
                            </div>

                            <div class="form-group">
                                <label for="id_satuan">Satuan</label>
                                <select class="form-control select2" name="id_satuan" id="id_satuan" style="width: 100%;" readonly disabled>
                                    <option value="">-- Pilih Satuan --</option>
                                    <?php foreach ($satuan_list as $satuan): ?>
                                        <option value="<?= $satuan->id ?>"><?= $satuan->kode_satuan ?> - <?= $satuan->nama_satuan ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="form-text text-muted">Satuan akan terisi otomatis sesuai data barang</small>
                                <span class="help-block"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="qty_keluar">Qty Keluar <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="qty_keluar" id="qty_keluar" required min="0" step="1">
                                <small class="form-text text-muted">Stok tersedia: <span id="stok_tersedia_display">-</span></small>
                                <span class="help-block"></span>
                            </div>




                        </div>
                    </div>

                    <div class="form-group">
                        <label for="keterangan_detail">Keterangan</label>
                        <textarea class="form-control" name="keterangan" id="keterangan_detail" rows="2" placeholder="Keterangan untuk item ini (opsional)"></textarea>
                        <span class="help-block"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button><button type="button" id="btnSaveDetail" onclick="saveDetail()" class="btn btn-primary">Save</button></div>
        </div>
    </div>
</div>

<style>
    /* Ensure proper z-index layering for nested modals */
    #modal_form_detail {
        z-index: 1060 !important;
    }

    #modal_form_detail .modal-backdrop {
        z-index: 1055 !important;
    }
</style>

<script>
var save_method_detail;
var current_barang_keluar_id = <?= $barang_keluar->id ?>;

$(document).ready(function() {
    // Initialize Select2 untuk modal detail
    $('#modal_form_detail').on('shown.bs.modal', function() {
        $('.select2').select2({
            dropdownParent: $('#modal_form_detail'),
            placeholder: "-- Pilih --",
            allowClear: true
        });
    });

    // Prevent parent modal from closing when child modal is active
    $('#modal_form_detail').on('show.bs.modal', function(e) {
        // Set z-index yang lebih tinggi untuk modal child
        $(this).css('z-index', 1060);
        // Disable backdrop click pada modal parent sementara
        $('#modal_detail').off('click.bs.modal.data-api');
    });

    // Restore parent modal behavior when child modal closes
    $('#modal_form_detail').on('hidden.bs.modal', function(e) {
        // Reset z-index
        $(this).css('z-index', '');
        // Re-enable backdrop click pada modal parent
        $('#modal_detail').on('click.bs.modal.data-api', '[data-dismiss="modal"]', function(e) {
            $('#modal_detail').modal('hide');
        });
    });

    // Prevent parent modal from closing when clicking cancel button
    $('#modal_form_detail').on('click', '[data-dismiss="modal"]', function(e) {
        e.stopPropagation();
        $('#modal_form_detail').modal('hide');
        return false;
    });

    // Event handler untuk perubahan barang
    $('#id_barang').on('change', function() {
        var selectedOption = $(this).find('option:selected');
        var satuanId = selectedOption.data('satuan');


        // Atur satuan berdasarkan data barang
        $('#id_satuan').prop('disabled', false).prop('readonly', false);
        
        if (satuanId) {
            $('#id_satuan').val(satuanId).trigger('change');
        }
        
        // Nonaktifkan kembali field satuan setelah diatur
        $('#id_satuan').prop('disabled', true).prop('readonly', true);



        // Update stok tersedia saat barang dan gudang dipilih
        updateStokTersedia();
    });

    // Event handler untuk perubahan gudang
    $('#id_gudang').on('change', function() {
        updateStokTersedia();
    });
});

function updateStokTersedia() {
    var idBarang = $('#id_barang').val();
    var idGudang = $('#id_gudang').val();

    if (idBarang && idGudang) {
        $.ajax({
            url: "<?php echo site_url('BarangKeluar/get_stok_barang') ?>",
            type: "POST",
            data: {
                id_barang: idBarang,
                id_gudang: idGudang
            },
            dataType: "JSON",
            success: function(data) {
                var stok = parseFloat(data.stok_tersedia) || 0;
                $('#stok_tersedia_display').text(stok.toLocaleString('id-ID'));

                // Update color based on stock
                if (stok <= 0) {
                    $('#stok_tersedia_display').removeClass('text-success').addClass('text-danger');
                } else {
                    $('#stok_tersedia_display').removeClass('text-danger').addClass('text-success');
                }
            },
            error: function() {
                $('#stok_tersedia_display').text('-');
            }
        });
    } else {
        $('#stok_tersedia_display').text('-');
    }
}

function addDetailItem() {
    save_method_detail = 'add';
    $('#form_detail')[0].reset();
    $('#detail_id').val('');
    $('#barang_group').show();
    $('#total_harga_display').text('0');
    $('#stok_tersedia_display').text('-');
    
    // Set field satuan ke kondisi awal (disabled)
    $('#id_satuan').prop('disabled', true).prop('readonly', true);
    
    $('.modal-title').text('Tambah Detail Item');
    $('#modal_form_detail').modal('show');
}

function editDetailItem(id) {
    save_method_detail = 'update';
    $('#barang_group').show();
    $('.modal-title').text('Edit Detail Item');

    $.ajax({
        url: "<?php echo site_url('BarangKeluar/edit_detail/') ?>" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('#detail_id').val(data.id);
            $('#id_barang').val(data.id_barang).trigger('change');
            $('#id_gudang').val(data.id_gudang).trigger('change');
            
            // Atur satuan setelah barang dipilih (satuan akan diatur otomatis oleh event handler barang)
            // Field satuan akan tetap readonly/disabled
            
            $('#qty_keluar').val(data.qty_keluar);
            $('#keterangan_detail').val(data.keterangan);
            updateStokTersedia();
            $('#modal_form_detail').modal('show');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat memuat data detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}

function saveDetail() {
    $('#btnSaveDetail').text('saving...').prop('disabled', true);

    var url;
    if (save_method_detail == 'add') {
        url = "<?php echo site_url('BarangKeluar/insert_detail') ?>";
    } else {
        url = "<?php echo site_url('BarangKeluar/update_detail') ?>";
    }

    // Aktifkan field satuan sementara untuk memastikan nilainya dikirim
    $('#id_satuan').prop('disabled', false);
    var formData = $('#form_detail').serialize();
    $('#id_satuan').prop('disabled', true);

    $.ajax({
        url: url,
        type: "POST",
        data: formData,
        dataType: "JSON",
        success: function(data) {
            if (data.status) {
                $('#modal_form_detail').modal('hide');
                refreshDetailTable();
                refreshParentTable();
                Swal.fire({
                    title: 'Berhasil!',
                    text: 'Detail item berhasil disimpan.',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                for (var i = 0; i < data.inputerror.length; i++) {
                    $('[name="' + data.inputerror[i] + '"]').addClass('is-invalid');
                    $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                }

                var errorMessages = data.error_string.join('<br>');
                Swal.fire({
                    title: 'Gagal Menyimpan!',
                    html: 'Terjadi kesalahan:<br>' + errorMessages,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menyimpan detail.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        },
        complete: function() {
            $('#btnSaveDetail').text('Save').prop('disabled', false);
        }
    });
}

function deleteDetailItem(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Detail item akan dihapus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?php echo site_url('BarangKeluar/delete_detail') ?>",
                type: "POST",
                dataType: "JSON",
                data: { id: id },
                success: function(data) {
                    if (data.status) {
                        refreshDetailTable();
                        refreshParentTable();
                        Swal.fire({
                            title: 'Terhapus!',
                            text: 'Detail item berhasil dihapus.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Gagal menghapus detail item.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menghapus detail.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}

function finalizeFromDetail() {
    Swal.fire({
        title: 'Finalisasi Barang Keluar?',
        text: "Setelah difinalisasi, data tidak dapat diubah lagi dan akan otomatis update stok sistem!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, finalisasi!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: "<?php echo site_url('BarangKeluar/finalize') ?>",
                type: "POST",
                dataType: "JSON",
                data: { id: current_barang_keluar_id },
                success: function(data) {
                    if (data.status) {
                        refreshDetailTable();
                        refreshParentTable();
                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Barang keluar berhasil difinalisasi dan stok sistem telah diupdate.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        var errorMessage = data.message || 'Gagal finalisasi barang keluar.';

                        // Jika ada insufficient items, tampilkan detail
                        if (data.insufficient_items && data.insufficient_items.length > 0) {
                            var itemList = '<ul class="text-left">';
                            data.insufficient_items.forEach(function(item) {
                                itemList += '<li><strong>' + item.nama_barang + '</strong> di ' + item.nama_gudang +
                                           '<br>Qty keluar: ' + parseFloat(item.qty_keluar).toLocaleString() +
                                           ', Stok tersedia: ' + parseFloat(item.stok_tersedia).toLocaleString() + '</li>';
                            });
                            itemList += '</ul>';

                            Swal.fire({
                                title: 'Stok Tidak Mencukupi!',
                                html: errorMessage + '<br><br><strong>Detail:</strong>' + itemList,
                                icon: 'error',
                                confirmButtonText: 'OK',
                                width: '600px'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: errorMessage,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat finalisasi.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    });
}



function refreshDetailTable() {
    // Refresh tabel detail barang keluar
    $.ajax({
        url: "<?php echo site_url('BarangKeluar/get_detail_items/') . $barang_keluar->id ?>",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            // Kosongkan tbody tabel
            $('#tbl_detail_barang_keluar tbody').empty();
            
            var total_nilai = 0;
            
            if (data.length > 0) {
                $.each(data, function(index, item) {
                    var no = index + 1;
                    var merkTipe = '';
                    if (item.merk || item.tipe) {
                        merkTipe = '<br><small class="text-muted">' + (item.merk + ' ' + item.tipe).trim() + '</small>';
                    }
                    
                    var stokClass = item.stok_tersedia < item.qty_keluar ? 'text-danger' : 'text-success';
                    
                    var actionButtons = '';
                    <?php if ($barang_keluar->status == 'draft'): ?>
                    actionButtons = '<button type="button" class="btn btn-xs btn-outline-info" onclick="editDetailItem(' + item.id + ')" title="Edit">' +
                                   '<i class="fas fa-edit"></i></button> ' +
                                   '<button type="button" class="btn btn-xs btn-outline-danger" onclick="deleteDetailItem(' + item.id + ')" title="Delete">' +
                                   '<i class="fas fa-trash"></i></button>';
                    <?php endif; ?>
                    
                    var row = '<tr>' +
                        '<td>' + no + '</td>' +
                        '<td>' + item.kode_barang + '</td>' +
                        '<td>' + item.nama_barang + merkTipe + '</td>' +
                        '<td>' + item.nama_gudang + '<br><small class="text-muted">' + item.kode_gudang + '</small></td>' +
                        '<td class="text-right">' + parseFloat(item.qty_keluar).toLocaleString() + '</td>' +
                        '<td class="text-right"><span class="' + stokClass + '">' + parseFloat(item.stok_tersedia).toLocaleString() + '</span></td>' +
                        '<td>' + (item.nama_satuan || '-') + '</td>';
                    
                    <?php if ($barang_keluar->status == 'draft'): ?>
                    row += '<td>' + actionButtons + '</td>';
                    <?php endif; ?>
                    
                    row += '</tr>';
                    
                    $('#tbl_detail_barang_keluar tbody').append(row);

                });
                
                // Tambahkan row total
                var totalRow = '<tr class="bg-light font-weight-bold">' +
                    '<td colspan="8" class="text-right"><strong>TOTAL NILAI:</strong></td>' +
                    '<td class="text-right"><strong>' + formatRupiah(total_nilai) + '</strong></td>';
                
                <?php if ($barang_keluar->status == 'draft'): ?>
                totalRow += '<td></td>';
                <?php endif; ?>
                
                totalRow += '</tr>';
                
                $('#tbl_detail_barang_keluar tbody').append(totalRow);
            } else {
                var emptyRow = '<tr><td colspan="<?php echo ($barang_keluar->status == 'draft') ? '10' : '9'; ?>" class="text-center text-muted">Belum ada detail item</td></tr>';
                $('#tbl_detail_barang_keluar tbody').append(emptyRow);
            }
            
            // Update total harga display jika ada
            if ($('#total_harga_display').length) {
                $('#total_harga_display').text(formatRupiah(total_nilai));
            }
        },
        error: function() {
            console.log('Error refreshing detail table');
        }
    });
}

function refreshParentTable() {
    // Coba refresh tabel di halaman parent
    try {
        if (window.parent && window.parent.table && typeof window.parent.table.ajax !== 'undefined') {
            window.parent.table.ajax.reload(null, false);
        } else if (window.parent && window.parent.$('#tbl_barang_keluar').length) {
            window.parent.$('#tbl_barang_keluar').DataTable().ajax.reload(null, false);
        } else {
            // Trigger custom event pada modal parent
            if (window.parent && window.parent.$('#modal_detail').length) {
                window.parent.$('#modal_detail').trigger('hidden.bs.modal.refresh');
            }
        }
    } catch (e) {
        console.log('Could not refresh parent table:', e);
    }
}

function formatRupiah(angka) {
    return 'Rp ' + parseFloat(angka).toLocaleString('id-ID');
}
</script>
