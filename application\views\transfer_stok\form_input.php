<form id="form" class="form-horizontal">
    <input type="hidden" name="id" id="id">
    
    <div class="form-group row">
        <label class="col-sm-3 col-form-label">Nomor Transfer</label>
        <div class="col-sm-9">
            <div class="input-group">
                <input type="text" class="form-control" name="nomor_transfer" id="nomor_transfer" placeholder="TSK-YYYYMMDD-0001">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-secondary" onclick="generateNomor()">
                        <i class="fas fa-magic"></i>
                    </button>
                </div>
            </div>
            <small class="form-text text-muted">Kosongkan untuk generate otomatis</small>
        </div>
    </div>

    <div class="form-group row">
        <label class="col-sm-3 col-form-label">Tanggal Transfer <span class="text-danger">*</span></label>
        <div class="col-sm-9">
            <input type="date" class="form-control" name="tanggal_transfer" id="tanggal_transfer" required>
            <span class="help-block"></span>
        </div>
    </div>

    <div class="form-group row">
        <label class="col-sm-3 col-form-label">Gudang Asal <span class="text-danger">*</span></label>
        <div class="col-sm-9">
            <select class="form-control select2" name="gudang_asal_id" id="gudang_asal_id" required style="width: 100%;">
                <option value="">-- Pilih Gudang Asal --</option>
                <?php foreach ($gudang_list as $gudang): ?>
                    <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                <?php endforeach; ?>
            </select>
            <span class="help-block"></span>
        </div>
    </div>

    <div class="form-group row">
        <label class="col-sm-3 col-form-label">Gudang Tujuan <span class="text-danger">*</span></label>
        <div class="col-sm-9">
            <select class="form-control select2" name="gudang_tujuan_id" id="gudang_tujuan_id" required style="width: 100%;">
                <option value="">-- Pilih Gudang Tujuan --</option>
                <?php foreach ($gudang_list as $gudang): ?>
                    <option value="<?= $gudang->id ?>"><?= $gudang->kode_gudang ?> - <?= $gudang->nama_gudang ?></option>
                <?php endforeach; ?>
            </select>
            <span class="help-block"></span>
        </div>
    </div>

    <div class="form-group row">
        <label class="col-sm-3 col-form-label">Keterangan</label>
        <div class="col-sm-9">
            <textarea class="form-control" name="keterangan" id="keterangan" rows="3"></textarea>
        </div>
    </div>

</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });

    // Set default tanggal ke hari ini
    $('#tanggal_transfer').val(new Date().toISOString().split('T')[0]);
});
</script> 